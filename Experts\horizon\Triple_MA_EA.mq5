//+------------------------------------------------------------------+
//| Triple MA Trend EA - 3 MA Sıralama Stratejisi                  |
//| MA50 < MA30 < MA14 = BUY | MA50 > MA30 > MA14 = SELL           |
//+------------------------------------------------------------------+
#property copyright "Horizon Trading"
#property version   "1.00"
#property description "Triple MA Trend Following EA with ATR TP/SL"

#include <Trade\Trade.mqh>
CTrade trade;

//--- Input parametreleri
input group "=== 📈 MOVING AVERAGE AYARLARI ==="
input int                   MA_Fast_Period = 14;                 // Hızlı MA Periyodu
input int                   MA_Medium_Period = 30;               // Orta MA Periyodu  
input int                   MA_Slow_Period = 50;                 // Yavaş MA Periyodu
input ENUM_MA_METHOD        MA_Method = MODE_EMA;                 // MA Metodu
input ENUM_APPLIED_PRICE    MA_Applied_Price = PRICE_CLOSE;       // MA Uygulama Fiyatı

input group "=== 🎯 TP/SL AYARLARI ==="
input bool                  Use_ATR_TP_SL = true;                // ATR Bazlı TP/SL Kullan
input double                ATR_TP_Multiplier = 3.0;             // ATR TP Çarpanı
input double                ATR_SL_Multiplier = 1.5;             // ATR SL Çarpanı
input int                   ATR_Period = 14;                     // ATR Periyodu
input double                Fixed_TP_Pips = 50.0;                // Sabit TP (pip) - ATR kapalıysa
input double                Fixed_SL_Pips = 25.0;                // Sabit SL (pip) - ATR kapalıysa

input group "=== 📊 TRAILING STOP ==="
input bool                  Use_Trailing_Stop = true;            // Trailing Stop Kullan
input double                Trailing_Stop_Pips = 15.0;           // Trailing Stop Mesafesi (pip)
input double                Trailing_Step_Pips = 5.0;            // Trailing Step (pip)

input group "=== 💰 RİSK YÖNETİMİ ==="
input bool                  Use_Risk_Management = true;          // Risk Yönetimi Kullan
input double                Risk_Percent = 2.0;                  // Risk Yüzdesi (% bakiye)
input double                Fixed_Lot_Size = 0.01;               // Sabit Lot (Risk yönetimi kapalıysa)
input double                Max_Lot_Size = 1.0;                  // Maksimum Lot
input double                Min_Lot_Size = 0.01;                 // Minimum Lot
input int                   Magic_Number = 9999;                 // Magic Number

input group "=== 🤖 İŞLEM AYARLARI ==="
input bool                  Enable_AutoTrade = true;             // ✅ Otomatik İşlem Yapma
input bool                  Signal_Only_Mode = false;            // 📡 Sadece Sinyal Modu

input group "=== 📱 TELEGRAM ==="
input bool                  Enable_Telegram = true;              // Telegram Bildirimleri
input string                Telegram_Token = "";                 // Bot Token
input string                Telegram_ChatID = "";                // Chat ID
input bool                  Send_Signal_Alerts = true;           // Sinyal Bildirimleri
input bool                  Send_Trade_Alerts = true;            // İşlem Bildirimleri

input group "=== ⚙️ GENEL ==="
input bool                  Enable_Debug = true;                 // Debug Logları

//--- Global değişkenler
int ma_fast_handle, ma_medium_handle, ma_slow_handle, atr_handle;
double ma_fast[], ma_medium[], ma_slow[], atr_buffer[];

// Sinyal takip değişkenleri
enum TREND_STATE
{
    TREND_NONE = 0,     // Trend yok
    TREND_BULLISH = 1,  // Yükseliş trendi (MA50 < MA30 < MA14)
    TREND_BEARISH = 2   // Düşüş trendi (MA50 > MA30 > MA14)
};

TREND_STATE current_trend = TREND_NONE;
TREND_STATE last_signal_trend = TREND_NONE;
datetime last_signal_time = 0;
datetime last_telegram_time = 0;

int debug_log_number = 0;
int total_signals = 0;
int successful_trades = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // MA handle'larını oluştur
    ma_fast_handle = iMA(_Symbol, _Period, MA_Fast_Period, 0, MA_Method, MA_Applied_Price);
    ma_medium_handle = iMA(_Symbol, _Period, MA_Medium_Period, 0, MA_Method, MA_Applied_Price);
    ma_slow_handle = iMA(_Symbol, _Period, MA_Slow_Period, 0, MA_Method, MA_Applied_Price);
    atr_handle = iATR(_Symbol, _Period, ATR_Period);
    
    if(ma_fast_handle == INVALID_HANDLE || ma_medium_handle == INVALID_HANDLE || 
       ma_slow_handle == INVALID_HANDLE || atr_handle == INVALID_HANDLE)
    {
        Print("❌ Indicator handle oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Array ayarları
    ArraySetAsSeries(ma_fast, true);
    ArraySetAsSeries(ma_medium, true);
    ArraySetAsSeries(ma_slow, true);
    ArraySetAsSeries(atr_buffer, true);
    
    // Değişkenleri sıfırla
    current_trend = TREND_NONE;
    last_signal_trend = TREND_NONE;
    last_signal_time = 0;
    last_telegram_time = 0;
    debug_log_number = 0;
    total_signals = 0;
    successful_trades = 0;
    
    Print("✅ Triple MA EA başlatıldı - Magic: ", Magic_Number);
    Print("📈 MA Periyotları: Fast=", MA_Fast_Period, " Medium=", MA_Medium_Period, " Slow=", MA_Slow_Period);
    Print("🎯 BUY: MA", MA_Slow_Period, " < MA", MA_Medium_Period, " < MA", MA_Fast_Period);
    Print("🎯 SELL: MA", MA_Slow_Period, " > MA", MA_Medium_Period, " > MA", MA_Fast_Period);
    
    if(Use_ATR_TP_SL)
    {
        Print("🎯 ATR TP/SL: TP=ATR×", ATR_TP_Multiplier, " | SL=ATR×", ATR_SL_Multiplier);
    }
    else
    {
        Print("🎯 Sabit TP/SL: TP=", Fixed_TP_Pips, "pip | SL=", Fixed_SL_Pips, "pip");
    }
    
    if(Use_Trailing_Stop)
    {
        Print("📊 Trailing Stop: ", Trailing_Stop_Pips, "pip (Step:", Trailing_Step_Pips, "pip)");
    }
    
    if(Enable_AutoTrade)
    {
        Print("🤖 AutoTrade: AÇIK - Otomatik işlem yapılacak");
    }
    else if(Signal_Only_Mode)
    {
        Print("📡 Sinyal Modu: AÇIK - Sadece sinyal verilecek");
    }
    else
    {
        Print("⏸️ AutoTrade: KAPALI");
    }
    
    // Telegram başlatma mesajı
    if(Enable_Telegram && StringLen(Telegram_Token) > 0 && StringLen(Telegram_ChatID) > 0)
    {
        string trade_mode = Enable_AutoTrade ? "AutoTrade ON" : (Signal_Only_Mode ? "Signal Only" : "AutoTrade OFF");
        SendTelegramMessage("Triple MA EA Started - " + _Symbol + " - " + trade_mode + " - " + TimeToString(TimeCurrent()));
    }
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(Enable_Telegram && StringLen(Telegram_Token) > 0 && StringLen(Telegram_ChatID) > 0)
    {
        SendTelegramMessage("Triple MA EA Stopped - " + _Symbol + " - Signals: " + IntegerToString(total_signals) + " - " + TimeToString(TimeCurrent()));
    }
    
    Print("🔄 Triple MA EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Yeterli bar kontrolü
    if(Bars(_Symbol, _Period) < MA_Slow_Period + 5)
        return;
    
    // Indicator verilerini al
    if(CopyBuffer(ma_fast_handle, 0, 0, 3, ma_fast) < 3 ||
       CopyBuffer(ma_medium_handle, 0, 0, 3, ma_medium) < 3 ||
       CopyBuffer(ma_slow_handle, 0, 0, 3, ma_slow) < 3 ||
       CopyBuffer(atr_handle, 0, 0, 2, atr_buffer) < 2)
    {
        return;
    }
    
    // Mevcut MA değerleri
    double ma_fast_current = ma_fast[0];
    double ma_medium_current = ma_medium[0];
    double ma_slow_current = ma_slow[0];
    double atr_current = atr_buffer[0];
    
    // Mevcut pozisyon kontrolü
    bool has_position = HasAnyPosition();
    
    // Trailing Stop kontrolü
    if(has_position && Use_Trailing_Stop && Enable_AutoTrade)
    {
        UpdateTrailingStop();
    }
    
    // Trend analizi
    TREND_STATE new_trend = AnalyzeTrend(ma_fast_current, ma_medium_current, ma_slow_current);
    
    // Debug - trend durumu
    static datetime last_debug_time = 0;
    if(TimeCurrent() - last_debug_time >= 5) // 5 saniyede bir
    {
        debug_log_number++;
        Print("📊 [", debug_log_number, "] TRIPLE MA ANALİZİ:");
        Print("   MA", MA_Fast_Period, ": ", DoubleToString(ma_fast_current, 5));
        Print("   MA", MA_Medium_Period, ": ", DoubleToString(ma_medium_current, 5));
        Print("   MA", MA_Slow_Period, ": ", DoubleToString(ma_slow_current, 5));
        Print("   ATR: ", DoubleToString(atr_current, 5));
        Print("   Mevcut Trend: ", TrendToString(new_trend));
        Print("   Son Sinyal Trend: ", TrendToString(last_signal_trend));
        Print("   Pozisyon Var: ", has_position);
        
        last_debug_time = TimeCurrent();
    }
    
    // Sinyal kontrolü - sadece trend değiştiğinde
    if(new_trend != TREND_NONE && new_trend != last_signal_trend && !has_position)
    {
        if(new_trend == TREND_BULLISH)
        {
            // BUY sinyali
            ProcessBuySignal(ma_fast_current, ma_medium_current, ma_slow_current, atr_current);
        }
        else if(new_trend == TREND_BEARISH)
        {
            // SELL sinyali
            ProcessSellSignal(ma_fast_current, ma_medium_current, ma_slow_current, atr_current);
        }
        
        last_signal_trend = new_trend;
        last_signal_time = TimeCurrent();
    }
    
    current_trend = new_trend;
}

//+------------------------------------------------------------------+
//| Trend analizi - MA sıralamasını kontrol et                     |
//+------------------------------------------------------------------+
TREND_STATE AnalyzeTrend(double ma_fast, double ma_medium, double ma_slow)
{
    // BUY trendi: MA50 < MA30 < MA14 (Yükseliş)
    if(ma_slow < ma_medium && ma_medium < ma_fast)
    {
        return TREND_BULLISH;
    }
    // SELL trendi: MA50 > MA30 > MA14 (Düşüş)
    else if(ma_slow > ma_medium && ma_medium > ma_fast)
    {
        return TREND_BEARISH;
    }

    return TREND_NONE;
}

//+------------------------------------------------------------------+
//| Trend durumunu string'e çevir                                  |
//+------------------------------------------------------------------+
string TrendToString(TREND_STATE trend)
{
    switch(trend)
    {
        case TREND_BULLISH: return "BULLISH (BUY)";
        case TREND_BEARISH: return "BEARISH (SELL)";
        case TREND_NONE: return "NONE";
        default: return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| BUY sinyali işle                                               |
//+------------------------------------------------------------------+
void ProcessBuySignal(double ma_fast, double ma_medium, double ma_slow, double atr_value)
{
    total_signals++;
    debug_log_number++;
    Print("🟢 [", debug_log_number, "] BUY SİNYALİ YAKALANDI!");
    Print("   MA", MA_Fast_Period, ": ", DoubleToString(ma_fast, 5));
    Print("   MA", MA_Medium_Period, ": ", DoubleToString(ma_medium, 5));
    Print("   MA", MA_Slow_Period, ": ", DoubleToString(ma_slow, 5));
    Print("   Sıralama: MA", MA_Slow_Period, " < MA", MA_Medium_Period, " < MA", MA_Fast_Period, " ✅");

    // Telegram bildirimi
    if(Enable_Telegram && Send_Signal_Alerts)
    {
        string trade_status = Enable_AutoTrade ? " - TRADE OPENING" : (Signal_Only_Mode ? " - SIGNAL ONLY" : " - NO TRADE");
        string signal_message = "BUY SIGNAL DETECTED" + trade_status + "\n\n" +
                               _Symbol + " - " + GetTimeframeString() + "\n" +
                               "MA" + IntegerToString(MA_Fast_Period) + ": " + DoubleToString(ma_fast, 5) + "\n" +
                               "MA" + IntegerToString(MA_Medium_Period) + ": " + DoubleToString(ma_medium, 5) + "\n" +
                               "MA" + IntegerToString(MA_Slow_Period) + ": " + DoubleToString(ma_slow, 5) + "\n" +
                               "Trend: BULLISH\n" +
                               "Time: " + TimeToString(TimeCurrent());
        SendTelegramMessage(signal_message);
    }

    // AutoTrade kontrolü
    if(Enable_AutoTrade)
    {
        OpenBuyOrder(atr_value);
    }
    else
    {
        debug_log_number++;
        Print("📡 [", debug_log_number, "] BUY sinyali - AutoTrade kapalı, işlem açılmadı");
    }
}

//+------------------------------------------------------------------+
//| SELL sinyali işle                                              |
//+------------------------------------------------------------------+
void ProcessSellSignal(double ma_fast, double ma_medium, double ma_slow, double atr_value)
{
    total_signals++;
    debug_log_number++;
    Print("🔴 [", debug_log_number, "] SELL SİNYALİ YAKALANDI!");
    Print("   MA", MA_Fast_Period, ": ", DoubleToString(ma_fast, 5));
    Print("   MA", MA_Medium_Period, ": ", DoubleToString(ma_medium, 5));
    Print("   MA", MA_Slow_Period, ": ", DoubleToString(ma_slow, 5));
    Print("   Sıralama: MA", MA_Slow_Period, " > MA", MA_Medium_Period, " > MA", MA_Fast_Period, " ✅");

    // Telegram bildirimi
    if(Enable_Telegram && Send_Signal_Alerts)
    {
        string trade_status = Enable_AutoTrade ? " - TRADE OPENING" : (Signal_Only_Mode ? " - SIGNAL ONLY" : " - NO TRADE");
        string signal_message = "SELL SIGNAL DETECTED" + trade_status + "\n\n" +
                               _Symbol + " - " + GetTimeframeString() + "\n" +
                               "MA" + IntegerToString(MA_Fast_Period) + ": " + DoubleToString(ma_fast, 5) + "\n" +
                               "MA" + IntegerToString(MA_Medium_Period) + ": " + DoubleToString(ma_medium, 5) + "\n" +
                               "MA" + IntegerToString(MA_Slow_Period) + ": " + DoubleToString(ma_slow, 5) + "\n" +
                               "Trend: BEARISH\n" +
                               "Time: " + TimeToString(TimeCurrent());
        SendTelegramMessage(signal_message);
    }

    // AutoTrade kontrolü
    if(Enable_AutoTrade)
    {
        OpenSellOrder(atr_value);
    }
    else
    {
        debug_log_number++;
        Print("📡 [", debug_log_number, "] SELL sinyali - AutoTrade kapalı, işlem açılmadı");
    }
}

//+------------------------------------------------------------------+
//| BUY emri aç                                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder(double atr_value)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl, tp;

    if(Use_ATR_TP_SL)
    {
        sl = ask - (atr_value * ATR_SL_Multiplier);
        tp = ask + (atr_value * ATR_TP_Multiplier);
    }
    else
    {
        sl = ask - (Fixed_SL_Pips * _Point * 10);
        tp = ask + (Fixed_TP_Pips * _Point * 10);
    }

    double lot_size = CalculateLotSize(MathAbs(ask - sl));

    trade.SetExpertMagicNumber(Magic_Number);

    if(trade.Buy(lot_size, _Symbol, ask, sl, tp, "Triple_MA_BUY"))
    {
        successful_trades++;
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(ask, 5));
        Print("   SL: ", DoubleToString(sl, 5), " TP: ", DoubleToString(tp, 5));
        Print("   Lot: ", DoubleToString(lot_size, 2));

        // Telegram bildirimi
        if(Enable_Telegram && Send_Trade_Alerts)
        {
            string trade_message = "BUY ORDER OPENED\n\n" +
                                 "Ticket: " + IntegerToString(trade.ResultOrder()) + "\n" +
                                 _Symbol + " - " + GetTimeframeString() + "\n" +
                                 "Price: " + DoubleToString(ask, 5) + "\n" +
                                 "SL: " + DoubleToString(sl, 5) + "\n" +
                                 "TP: " + DoubleToString(tp, 5) + "\n" +
                                 "Lot: " + DoubleToString(lot_size, 2) + "\n" +
                                 "Time: " + TimeToString(TimeCurrent());
            SendTelegramMessage(trade_message);
        }
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] BUY EMRİ HATASI: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder(double atr_value)
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl, tp;

    if(Use_ATR_TP_SL)
    {
        sl = bid + (atr_value * ATR_SL_Multiplier);
        tp = bid - (atr_value * ATR_TP_Multiplier);
    }
    else
    {
        sl = bid + (Fixed_SL_Pips * _Point * 10);
        tp = bid - (Fixed_TP_Pips * _Point * 10);
    }

    double lot_size = CalculateLotSize(MathAbs(bid - sl));

    trade.SetExpertMagicNumber(Magic_Number);

    if(trade.Sell(lot_size, _Symbol, bid, sl, tp, "Triple_MA_SELL"))
    {
        successful_trades++;
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] SELL EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(bid, 5));
        Print("   SL: ", DoubleToString(sl, 5), " TP: ", DoubleToString(tp, 5));
        Print("   Lot: ", DoubleToString(lot_size, 2));

        // Telegram bildirimi
        if(Enable_Telegram && Send_Trade_Alerts)
        {
            string trade_message = "SELL ORDER OPENED\n\n" +
                                 "Ticket: " + IntegerToString(trade.ResultOrder()) + "\n" +
                                 _Symbol + " - " + GetTimeframeString() + "\n" +
                                 "Price: " + DoubleToString(bid, 5) + "\n" +
                                 "SL: " + DoubleToString(sl, 5) + "\n" +
                                 "TP: " + DoubleToString(tp, 5) + "\n" +
                                 "Lot: " + DoubleToString(lot_size, 2) + "\n" +
                                 "Time: " + TimeToString(TimeCurrent());
            SendTelegramMessage(trade_message);
        }
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] SELL EMRİ HATASI: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| Telegram mesajı gönder                                          |
//+------------------------------------------------------------------+
bool SendTelegramMessage(string message)
{
    if(!Enable_Telegram || StringLen(Telegram_Token) == 0 || StringLen(Telegram_ChatID) == 0)
        return false;

    // Rate limiting
    if(TimeCurrent() - last_telegram_time < 2)
        return false;

    // Sadece ASCII karakterleri kullan
    string clean_message = "";
    for(int i = 0; i < StringLen(message); i++)
    {
        ushort ch = StringGetCharacter(message, i);
        if((ch >= 32 && ch <= 126) || ch == 10)
        {
            if(ch == 34) clean_message += "\\\"";
            else if(ch == 92) clean_message += "\\\\";
            else if(ch == 10) clean_message += "\\n";
            else clean_message += CharToString((char)ch);
        }
        else
        {
            clean_message += " ";
        }
    }

    string url = "https://api.telegram.org/bot" + Telegram_Token + "/sendMessage";
    string json_data = "{\"chat_id\":\"" + Telegram_ChatID + "\",\"text\":\"" + clean_message + "\"}";

    char post[], result[];
    string headers = "Content-Type: application/json\r\n";

    StringToCharArray(json_data, post, 0, StringLen(json_data));

    int timeout = 10000;
    int res = WebRequest("POST", url, headers, timeout, post, result, headers);

    last_telegram_time = TimeCurrent();

    if(res == 200)
    {
        if(Enable_Debug) Print("Telegram SUCCESS!");
        return true;
    }
    else
    {
        Print("Telegram ERROR: ", res, " - ", CharArrayToString(result));
        return false;
    }
}

//+------------------------------------------------------------------+
//| Timeframe string al                                             |
//+------------------------------------------------------------------+
string GetTimeframeString()
{
    switch(_Period)
    {
        case PERIOD_M1: return "M1";
        case PERIOD_M5: return "M5";
        case PERIOD_M15: return "M15";
        case PERIOD_M30: return "M30";
        case PERIOD_H1: return "H1";
        case PERIOD_H4: return "H4";
        case PERIOD_D1: return "D1";
        default: return "Unknown";
    }
}

//+------------------------------------------------------------------+
//| Herhangi bir pozisyon var mı kontrol et                         |
//+------------------------------------------------------------------+
bool HasAnyPosition()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number)
                {
                    return true;
                }
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Trailing Stop güncelle                                          |
//+------------------------------------------------------------------+
void UpdateTrailingStop()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number)
                {
                    double current_sl = PositionGetDouble(POSITION_SL);
                    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
                    int position_type = (int)PositionGetInteger(POSITION_TYPE);

                    double current_price = (position_type == POSITION_TYPE_BUY) ?
                                         SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                                         SymbolInfoDouble(_Symbol, SYMBOL_ASK);

                    double trailing_distance = Trailing_Stop_Pips * _Point * 10;
                    double trailing_step = Trailing_Step_Pips * _Point * 10;
                    double new_sl = 0;

                    if(position_type == POSITION_TYPE_BUY)
                    {
                        new_sl = current_price - trailing_distance;
                        if(new_sl > current_sl + trailing_step && new_sl > open_price)
                        {
                            trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP));
                            debug_log_number++;
                            Print("📈 [", debug_log_number, "] BUY Trailing Stop: ", DoubleToString(new_sl, 5));

                            if(Enable_Telegram && Send_Trade_Alerts)
                            {
                                SendTelegramMessage("BUY TRAILING STOP UPDATED\n\nTicket: " + IntegerToString(ticket) +
                                                  "\nNew SL: " + DoubleToString(new_sl, 5) +
                                                  "\nTime: " + TimeToString(TimeCurrent()));
                            }
                        }
                    }
                    else if(position_type == POSITION_TYPE_SELL)
                    {
                        new_sl = current_price + trailing_distance;
                        if((current_sl == 0 || new_sl < current_sl - trailing_step) && new_sl < open_price)
                        {
                            trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP));
                            debug_log_number++;
                            Print("📉 [", debug_log_number, "] SELL Trailing Stop: ", DoubleToString(new_sl, 5));

                            if(Enable_Telegram && Send_Trade_Alerts)
                            {
                                SendTelegramMessage("SELL TRAILING STOP UPDATED\n\nTicket: " + IntegerToString(ticket) +
                                                  "\nNew SL: " + DoubleToString(new_sl, 5) +
                                                  "\nTime: " + TimeToString(TimeCurrent()));
                            }
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Lot büyüklüğü hesapla                                           |
//+------------------------------------------------------------------+
double CalculateLotSize(double sl_distance)
{
    if(!Use_Risk_Management)
    {
        return Fixed_Lot_Size;
    }

    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * Risk_Percent / 100.0;

    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    double sl_pips = sl_distance / point;

    double lot_size = 0;
    if(sl_pips > 0 && tick_value > 0)
    {
        double pip_value = tick_value * (point / tick_size);
        lot_size = risk_amount / (sl_pips * pip_value);
    }
    else
    {
        lot_size = Fixed_Lot_Size;
    }

    if(lot_size < min_lot) lot_size = min_lot;
    if(lot_size > max_lot) lot_size = max_lot;
    if(lot_size > Max_Lot_Size) lot_size = Max_Lot_Size;
    if(lot_size < Min_Lot_Size) lot_size = Min_Lot_Size;

    lot_size = MathFloor(lot_size / lot_step) * lot_step;

    return lot_size;
}
