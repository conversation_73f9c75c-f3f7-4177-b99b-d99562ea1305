//+------------------------------------------------------------------+
//| TrendSignal EA                                                   |
//| Trendsignal_c13 indikatörü bazlı işlem sistemi                  |
//+------------------------------------------------------------------+
#property copyright "Horizon Trading"
#property version   "1.00"

#include <Trade\Trade.mqh>
CTrade trade;

//--- Input parametreleri
input group "=== TrendSignal Ayarları ==="
input int                   RISK = 3;                            // Risk Seviyesi (1-5)
input int                   CountBars = 300;                     // Analiz Bar Sayısı

input group "=== Risk Yönetimi ==="
input double                Lot_Size = 0.01;                     // Lot Büyüklüğü
input double                ATR_Multiplier = 2.0;                // ATR Çarpanı (SL için)
input int                   ATR_Period = 14;                     // ATR Periyodu
input double                Risk_Percent = 2.0;                  // Risk Yüzdesi (% bakiye)

input group "=== Genel Ayarlar ==="
input int                   Magic_Number = 8888;                 // Magic Number
input string                Comment_Prefix = "TRENDSIGNAL";      // Yorum Ön Eki
input bool                  Use_ATR_SL = true;                   // ATR bazlı SL kullan
input bool                  Enable_Debug = true;                 // Debug Logları

//--- Global değişkenler
int wpr_handle, atr_handle;
double wpr_buffer[], atr_buffer[];
int debug_log_number = 0;

// TrendSignal hesaplama değişkenleri
double Table_value2[];
int value10, value11;
double x1, x2;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Williams %R ve ATR handle'larını oluştur
    wpr_handle = iWPR(_Symbol, _Period, 14);
    atr_handle = iATR(_Symbol, _Period, ATR_Period);
    
    if(wpr_handle == INVALID_HANDLE || atr_handle == INVALID_HANDLE)
    {
        Print("❌ Indicator handle oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Array ayarları
    ArraySetAsSeries(wpr_buffer, true);
    ArraySetAsSeries(atr_buffer, true);
    ArrayResize(Table_value2, CountBars);
    ArraySetAsSeries(Table_value2, true);
    
    // TrendSignal parametrelerini hesapla
    value10 = 3 + RISK * 2;
    x1 = 67 + RISK;
    x2 = 33 - RISK;
    value11 = value10;
    
    // Değişkenleri sıfırla
    debug_log_number = 0;
    
    Print("✅ TrendSignal EA başlatıldı - Magic: ", Magic_Number);
    Print("📊 RISK: ", RISK, " | WPR Eşikleri: x1=", x1, " x2=", x2);
    Print("🎯 BUY: WPR<", x2, " sonra WPR>", x1, " | SELL: WPR>", x1, " sonra WPR<", x2);
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("🔄 TrendSignal EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Yeterli bar kontrolü
    if(Bars(_Symbol, _Period) < CountBars + 20)
        return;
    
    // Williams %R ve ATR verilerini al
    if(CopyBuffer(wpr_handle, 0, 0, CountBars + 10, wpr_buffer) < CountBars + 10 ||
       CopyBuffer(atr_handle, 0, 0, 2, atr_buffer) < 2)
    {
        return;
    }
    
    // Mevcut pozisyon kontrolü
    bool has_buy_position = HasPosition(ORDER_TYPE_BUY);
    bool has_sell_position = HasPosition(ORDER_TYPE_SELL);
    
    // TrendSignal hesaplama
    bool buy_signal = false;
    bool sell_signal = false;
    
    CalculateTrendSignal(buy_signal, sell_signal);
    
    // Debug - sadece sinyal olduğunda
    if(buy_signal || sell_signal)
    {
        debug_log_number++;
        Print("🔍 [", debug_log_number, "] TRENDSIGNAL SİNYALİ:");
        Print("   WPR: ", DoubleToString(wpr_buffer[0], 2), " Eşikler: x1=", x1, " x2=", x2);
        Print("   Sinyal - BUY: ", buy_signal, " SELL: ", sell_signal);
        Print("   Pozisyon - BUY: ", has_buy_position, " SELL: ", has_sell_position);
    }
    
    // Giriş sinyalleri
    if(buy_signal && !has_buy_position)
    {
        // Varsa SELL pozisyonunu kapat
        if(has_sell_position)
        {
            CloseAllPositions(ORDER_TYPE_SELL);
            debug_log_number++;
            Print("🔵 [", debug_log_number, "] SELL pozisyonu kapatıldı (BUY sinyali)");
        }
        
        OpenBuyOrder(atr_buffer[0]);
    }
    
    if(sell_signal && !has_sell_position)
    {
        // Varsa BUY pozisyonunu kapat
        if(has_buy_position)
        {
            CloseAllPositions(ORDER_TYPE_BUY);
            debug_log_number++;
            Print("🔵 [", debug_log_number, "] BUY pozisyonu kapatıldı (SELL sinyali)");
        }
        
        OpenSellOrder(atr_buffer[0]);
    }
}

//+------------------------------------------------------------------+
//| TrendSignal hesaplama fonksiyonu                                 |
//+------------------------------------------------------------------+
void CalculateTrendSignal(bool &buy_signal, bool &sell_signal)
{
    // Mevcut shift (0 = en son bar)
    int shift = 0;
    
    // Range hesaplama (son 10 bar)
    double Range = 0.0;
    double AvgRange = 0.0;
    
    for(int Counter = shift; Counter <= shift + 9; Counter++)
    {
        MqlRates rates[];
        if(CopyRates(_Symbol, _Period, Counter, 1, rates) == 1)
        {
            AvgRange += MathAbs(rates[0].high - rates[0].low);
        }
    }
    Range = AvgRange / 10.0;
    
    // Volatilite kontrolü
    int TrueCount = 0;
    int MRO1 = -1, MRO2 = -1;
    
    // İlk volatilite kontrolü
    for(int Counter = shift; Counter < shift + 9 && TrueCount < 1; Counter++)
    {
        MqlRates rates1[], rates2[];
        if(CopyRates(_Symbol, _Period, Counter, 1, rates1) == 1 &&
           CopyRates(_Symbol, _Period, Counter + 1, 1, rates2) == 1)
        {
            if(MathAbs(rates1[0].open - rates2[0].close) >= Range * 2.0)
            {
                TrueCount++;
                MRO1 = Counter;
            }
        }
    }
    
    // İkinci volatilite kontrolü
    TrueCount = 0;
    for(int Counter = shift; Counter < shift + 6 && TrueCount < 1; Counter++)
    {
        MqlRates rates1[], rates2[];
        if(CopyRates(_Symbol, _Period, Counter + 3, 1, rates1) == 1 &&
           CopyRates(_Symbol, _Period, Counter, 1, rates2) == 1)
        {
            if(MathAbs(rates1[0].close - rates2[0].close) >= Range * 4.6)
            {
                TrueCount++;
                MRO2 = Counter;
            }
        }
    }
    
    // WPR periyodu ayarlama
    int wpr_period = value10;
    if(MRO1 > -1) wpr_period = 3;
    if(MRO2 > -1) wpr_period = 4;
    
    // Güncel WPR değeri (0-100 arasına dönüştür)
    double current_wpr = 100 - MathAbs(wpr_buffer[0]);
    Table_value2[shift] = current_wpr;
    
    // BUY sinyali: WPR x2'nin altındayken, önceki değer x1'in üzerindeyse
    if(current_wpr < x2)
    {
        int i1 = 1;
        // Önceki değerleri kontrol et
        while(i1 < ArraySize(Table_value2) - shift && 
              Table_value2[shift + i1] >= x2 && 
              Table_value2[shift + i1] <= x1)
        {
            i1++;
        }
        
        if(i1 < ArraySize(Table_value2) - shift && Table_value2[shift + i1] > x1)
        {
            buy_signal = true;
        }
    }
    
    // SELL sinyali: WPR x1'in üzerindeyken, önceki değer x2'nin altındaysa
    if(current_wpr > x1)
    {
        int i1 = 1;
        // Önceki değerleri kontrol et
        while(i1 < ArraySize(Table_value2) - shift && 
              Table_value2[shift + i1] >= x2 && 
              Table_value2[shift + i1] <= x1)
        {
            i1++;
        }
        
        if(i1 < ArraySize(Table_value2) - shift && Table_value2[shift + i1] < x2)
        {
            sell_signal = true;
        }
    }
}

//+------------------------------------------------------------------+
//| BUY emri aç                                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder(double atr_value)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = 0, tp = 0;
    
    if(Use_ATR_SL)
    {
        sl = ask - (atr_value * ATR_Multiplier);
    }
    
    double lot = CalculateLotSize(sl > 0 ? MathAbs(ask - sl) : atr_value * ATR_Multiplier);
    
    trade.SetExpertMagicNumber(Magic_Number);
    
    if(trade.Buy(lot, _Symbol, ask, sl, tp, Comment_Prefix + "_BUY"))
    {
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(ask, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   WPR: ", DoubleToString(wpr_buffer[0], 2), " TrendSignal: BUY");
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] BUY EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder(double atr_value)
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = 0, tp = 0;
    
    if(Use_ATR_SL)
    {
        sl = bid + (atr_value * ATR_Multiplier);
    }
    
    double lot = CalculateLotSize(sl > 0 ? MathAbs(bid - sl) : atr_value * ATR_Multiplier);
    
    trade.SetExpertMagicNumber(Magic_Number);
    
    if(trade.Sell(lot, _Symbol, bid, sl, tp, Comment_Prefix + "_SELL"))
    {
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] SELL EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(bid, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   WPR: ", DoubleToString(wpr_buffer[0], 2), " TrendSignal: SELL");
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] SELL EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| Lot büyüklüğü hesapla                                           |
//+------------------------------------------------------------------+
double CalculateLotSize(double sl_distance)
{
    if(Risk_Percent <= 0) return Lot_Size;

    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * Risk_Percent / 100.0;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    double sl_points = sl_distance / point;
    double sl_ticks = sl_points * tick_size / point;
    double lot = risk_amount / (sl_ticks * tick_value);

    // Minimum ve maksimum lot kontrolü
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot = MathMax(lot, min_lot);
    lot = MathMin(lot, max_lot);
    lot = MathFloor(lot / lot_step) * lot_step;

    return lot;
}

//+------------------------------------------------------------------+
//| Pozisyon var mı kontrol et                                      |
//+------------------------------------------------------------------+
bool HasPosition(ENUM_ORDER_TYPE type)
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
                   PositionGetInteger(POSITION_TYPE) == type)
                {
                    return true;
                }
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void CloseAllPositions(ENUM_ORDER_TYPE type)
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
                   PositionGetInteger(POSITION_TYPE) == type)
                {
                    if(!trade.PositionClose(ticket))
                    {
                        debug_log_number++;
                        Print("❌ [", debug_log_number, "] Pozisyon kapatma hatası: ", trade.ResultRetcode());
                    }
                }
            }
        }
    }
}
