//+------------------------------------------------------------------+
//| Simple MA Cross EA                                               |
//| MA14 ve MA50 kesişimi ile basit işlem sistemi                   |
//+------------------------------------------------------------------+
#property copyright "Horizon Trading"
#property version   "1.00"

#include <Trade\Trade.mqh>
CTrade trade;

//--- Input parametreleri
input group "=== MA Ayarları ==="
input int                   MA_Fast_Period = 14;                 // Hızlı MA Periyodu (MA14)
input int                   MA_Slow_Period = 50;                 // Yavaş MA Periyodu (MA50)
input int                   MA_Fast_Shift = 0;                   // MA14 Kaydırma (Shift)
input int                   MA_Slow_Shift = 0;                   // MA50 Kaydırma (Shift)
input ENUM_MA_METHOD        MA_Method = MODE_EMA;                // MA Metodu
input ENUM_APPLIED_PRICE    MA_Applied_Price = PRICE_CLOSE;      // MA Uygulanan Fiyat

input group "=== Risk Yönetimi ==="
input double                Lot_Size = 0.01;                     // Lot Büyüklüğü
input double                ATR_Multiplier = 2.0;                // ATR Çarpanı (SL için)
input int                   ATR_Period = 14;                     // ATR Periyodu
input double                Risk_Percent = 2.0;                  // Risk Yüzdesi (% bakiye)

input group "=== Genel Ayarlar ==="
input int                   Magic_Number = 5555;                 // Magic Number
input string                Comment_Prefix = "MA_CROSS";         // Yorum Ön Eki
input bool                  Use_ATR_SL = true;                   // ATR bazlı SL kullan
input bool                  Enable_Debug = true;                 // Debug Logları

//--- Global değişkenler
int ma_fast_handle, ma_slow_handle, atr_handle;
double ma_fast_buffer[], ma_slow_buffer[], atr_buffer[];
int debug_log_number = 0;

// Önceki kesişim durumu
bool last_ma_fast_above = false;
bool first_run = true;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // MA handle'larını oluştur (shift parametresi ile)
    ma_fast_handle = iMA(_Symbol, _Period, MA_Fast_Period, MA_Fast_Shift, MA_Method, MA_Applied_Price);
    ma_slow_handle = iMA(_Symbol, _Period, MA_Slow_Period, MA_Slow_Shift, MA_Method, MA_Applied_Price);
    atr_handle = iATR(_Symbol, _Period, ATR_Period);
    
    if(ma_fast_handle == INVALID_HANDLE || ma_slow_handle == INVALID_HANDLE || atr_handle == INVALID_HANDLE)
    {
        Print("❌ MA handle oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Array ayarları
    ArraySetAsSeries(ma_fast_buffer, true);
    ArraySetAsSeries(ma_slow_buffer, true);
    ArraySetAsSeries(atr_buffer, true);
    
    // Değişkenleri sıfırla
    debug_log_number = 0;
    first_run = true;
    
    Print("✅ Simple MA Cross EA başlatıldı - Magic: ", Magic_Number);
    Print("📊 MA", MA_Fast_Period, "(shift:", MA_Fast_Shift, ") x MA", MA_Slow_Period, "(shift:", MA_Slow_Shift, ") kesişim sistemi aktif");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("🔄 Simple MA Cross EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Yeterli bar kontrolü
    if(Bars(_Symbol, _Period) < MathMax(MA_Fast_Period, MA_Slow_Period) + 5)
        return;
    
    // MA verilerini al
    if(CopyBuffer(ma_fast_handle, 0, 0, 3, ma_fast_buffer) < 3 ||
       CopyBuffer(ma_slow_handle, 0, 0, 3, ma_slow_buffer) < 3 ||
       CopyBuffer(atr_handle, 0, 0, 2, atr_buffer) < 2)
    {
        return;
    }
    
    // Mevcut değerler
    double ma_fast_current = ma_fast_buffer[0];
    double ma_slow_current = ma_slow_buffer[0];
    double ma_fast_previous = ma_fast_buffer[1];
    double ma_slow_previous = ma_slow_buffer[1];
    double atr_current = atr_buffer[0];
    
    // Mevcut pozisyon kontrolü
    bool has_buy_position = HasPosition(ORDER_TYPE_BUY);
    bool has_sell_position = HasPosition(ORDER_TYPE_SELL);
    
    // Kesişim kontrolü
    bool ma_fast_above_now = ma_fast_current > ma_slow_current;
    bool ma_fast_above_prev = ma_fast_previous > ma_slow_previous;
    
    // Kesişim sinyalleri
    bool bullish_cross = !ma_fast_above_prev && ma_fast_above_now;  // MA14 yukarı kesişim
    bool bearish_cross = ma_fast_above_prev && !ma_fast_above_now;  // MA14 aşağı kesişim
    
    // İlk çalıştırmada önceki durumu kaydet
    if(first_run)
    {
        last_ma_fast_above = ma_fast_above_now;
        first_run = false;
        if(Enable_Debug)
        {
            Print("🔄 İlk çalıştırma - MA14 durumu: ", (ma_fast_above_now ? "MA50 üzerinde" : "MA50 altında"));
        }
        return;
    }
    
    // Debug - sadece kesişim olduğunda
    if(bullish_cross || bearish_cross)
    {
        debug_log_number++;
        Print("🔍 [", debug_log_number, "] KESIŞIM SİNYALİ:");
        Print("   MA14(shift:", MA_Fast_Shift, "): ", DoubleToString(ma_fast_current, 5), " MA50(shift:", MA_Slow_Shift, "): ", DoubleToString(ma_slow_current, 5));
        Print("   Önceki - MA14: ", DoubleToString(ma_fast_previous, 5), " MA50: ", DoubleToString(ma_slow_previous, 5));
        Print("   Kesişim - Bullish: ", bullish_cross, " Bearish: ", bearish_cross);
        Print("   Pozisyon - BUY: ", has_buy_position, " SELL: ", has_sell_position);
    }
    
    // İşlem mantığı: Ters kesişimde pozisyon kapat ve ters işlem aç
    
    // BULLISH CROSS: MA14 > MA50 kesişimi
    if(bullish_cross)
    {
        // Varsa SELL pozisyonunu kapat
        if(has_sell_position)
        {
            CloseAllPositions(ORDER_TYPE_SELL);
            debug_log_number++;
            Print("🔵 [", debug_log_number, "] SELL pozisyonu kapatıldı (Bullish Cross)");
        }
        
        // BUY pozisyonu aç
        if(!has_buy_position)
        {
            OpenBuyOrder(atr_current);
        }
    }
    
    // BEARISH CROSS: MA14 < MA50 kesişimi
    if(bearish_cross)
    {
        // Varsa BUY pozisyonunu kapat
        if(has_buy_position)
        {
            CloseAllPositions(ORDER_TYPE_BUY);
            debug_log_number++;
            Print("🔵 [", debug_log_number, "] BUY pozisyonu kapatıldı (Bearish Cross)");
        }
        
        // SELL pozisyonu aç
        if(!has_sell_position)
        {
            OpenSellOrder(atr_current);
        }
    }
    
    // Önceki durumu güncelle
    last_ma_fast_above = ma_fast_above_now;
}

//+------------------------------------------------------------------+
//| BUY emri aç                                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder(double atr_value)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = 0, tp = 0;
    
    if(Use_ATR_SL)
    {
        sl = ask - (atr_value * ATR_Multiplier);
    }
    
    double lot = CalculateLotSize(sl > 0 ? MathAbs(ask - sl) : atr_value * ATR_Multiplier);
    
    trade.SetExpertMagicNumber(Magic_Number);
    
    if(trade.Buy(lot, _Symbol, ask, sl, tp, Comment_Prefix + "_BUY"))
    {
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(ask, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   MA14(shift:", MA_Fast_Shift, "): ", DoubleToString(ma_fast_buffer[0], 5), " MA50(shift:", MA_Slow_Shift, "): ", DoubleToString(ma_slow_buffer[0], 5));
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] BUY EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder(double atr_value)
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = 0, tp = 0;
    
    if(Use_ATR_SL)
    {
        sl = bid + (atr_value * ATR_Multiplier);
    }
    
    double lot = CalculateLotSize(sl > 0 ? MathAbs(bid - sl) : atr_value * ATR_Multiplier);
    
    trade.SetExpertMagicNumber(Magic_Number);
    
    if(trade.Sell(lot, _Symbol, bid, sl, tp, Comment_Prefix + "_SELL"))
    {
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] SELL EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(bid, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   MA14(shift:", MA_Fast_Shift, "): ", DoubleToString(ma_fast_buffer[0], 5), " MA50(shift:", MA_Slow_Shift, "): ", DoubleToString(ma_slow_buffer[0], 5));
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] SELL EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| Lot büyüklüğü hesapla                                           |
//+------------------------------------------------------------------+
double CalculateLotSize(double sl_distance)
{
    if(Risk_Percent <= 0) return Lot_Size;
    
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * Risk_Percent / 100.0;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    
    double sl_points = sl_distance / point;
    double sl_ticks = sl_points * tick_size / point;
    double lot = risk_amount / (sl_ticks * tick_value);
    
    // Minimum ve maksimum lot kontrolü
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lot = MathMax(lot, min_lot);
    lot = MathMin(lot, max_lot);
    lot = MathFloor(lot / lot_step) * lot_step;
    
    return lot;
}

//+------------------------------------------------------------------+
//| Pozisyon var mı kontrol et                                      |
//+------------------------------------------------------------------+
bool HasPosition(ENUM_ORDER_TYPE type)
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
                   PositionGetInteger(POSITION_TYPE) == type)
                {
                    return true;
                }
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void CloseAllPositions(ENUM_ORDER_TYPE type)
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
                   PositionGetInteger(POSITION_TYPE) == type)
                {
                    if(!trade.PositionClose(ticket))
                    {
                        debug_log_number++;
                        Print("❌ [", debug_log_number, "] Pozisyon kapatma hatası: ", trade.ResultRetcode());
                    }
                }
            }
        }
    }
}
