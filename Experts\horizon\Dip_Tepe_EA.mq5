//+------------------------------------------------------------------+
//| Dip Tepe Yakalama EA                                             |
//| Dip noktalarında BUY, Tepe noktalarında SELL                    |
//+------------------------------------------------------------------+
#property copyright "Horizon Trading"
#property version   "1.00"

#include <Trade\Trade.mqh>
CTrade trade;

//--- Input parametreleri
input group "=== Dip/Tepe Yakalama Ayarları ==="
input int                   RSI_Period = 14;                     // RSI Periyodu
input double                RSI_Oversold = 30.0;                 // RSI Aşırı Satım Seviyesi (Dip)
input double                RSI_Overbought = 70.0;               // RSI Aşırı Alım Seviyesi (Tepe)
input int                   WPR_Period = 14;                     // Williams %R Periyodu
input double                WPR_Oversold = -80.0;                // WPR Aşırı Satım (Dip)
input double                WPR_Overbought = -20.0;              // WPR Aşırı Alım (Tepe)

input group "=== Trend Filtresi ==="
input int                   MA_Period = 50;                      // MA Trend Filtresi
input ENUM_MA_METHOD        MA_Method = MODE_EMA;                 // MA Metodu

input group "=== Momentum Filtresi ==="
input int                   Momentum_Period = 10;                // Momentum Periyodu
input double                Min_Momentum_Change = 0.5;           // Minimum Momentum Değişimi

input group "=== Çıkış Ayarları ==="
input double                Profit_Target_Pips = 50.0;           // Kar Hedefi (pip)
input double                Stop_Loss_Pips = 25.0;               // Zarar Durdurma (pip)
input bool                  Use_Trailing_Stop = true;            // Takip Eden Stop Kullan
input double                Trailing_Stop_Pips = 15.0;           // Takip Eden Stop (pip)

input group "=== Risk Yönetimi ==="
input double                Lot_Size = 0.01;                     // Lot Büyüklüğü
input double                Risk_Percent = 2.0;                  // Risk Yüzdesi
input int                   Magic_Number = 8888;                 // Magic Number

input group "=== Genel Ayarlar ==="
input bool                  Enable_Debug = true;                 // Debug Logları

//--- Global değişkenler
int rsi_handle, wpr_handle, ma_handle, momentum_handle;
double rsi_buffer[], wpr_buffer[], ma_buffer[], momentum_buffer[];
int debug_log_number = 0;

// Dip/Tepe takip değişkenleri
bool dip_signal_active = false;    // Dip sinyali aktif mi?
bool tepe_signal_active = false;   // Tepe sinyali aktif mi?
datetime last_signal_time = 0;     // Son sinyal zamanı

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Indicator handle'larını oluştur
    rsi_handle = iRSI(_Symbol, _Period, RSI_Period, PRICE_CLOSE);
    wpr_handle = iWPR(_Symbol, _Period, WPR_Period);
    ma_handle = iMA(_Symbol, _Period, MA_Period, 0, MA_Method, PRICE_CLOSE);
    momentum_handle = iMomentum(_Symbol, _Period, Momentum_Period, PRICE_CLOSE);
    
    if(rsi_handle == INVALID_HANDLE || wpr_handle == INVALID_HANDLE ||
       ma_handle == INVALID_HANDLE || momentum_handle == INVALID_HANDLE)
    {
        Print("❌ Indicator handle oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Array ayarları
    ArraySetAsSeries(rsi_buffer, true);
    ArraySetAsSeries(wpr_buffer, true);
    ArraySetAsSeries(ma_buffer, true);
    ArraySetAsSeries(momentum_buffer, true);
    
    // Değişkenleri sıfırla
    debug_log_number = 0;
    dip_signal_active = false;
    tepe_signal_active = false;
    last_signal_time = 0;
    
    Print("✅ Dip Tepe Yakalama EA başlatıldı - Magic: ", Magic_Number);
    Print("📊 DIP: RSI<", RSI_Oversold, " + WPR<", WPR_Oversold, " → BUY");
    Print("📊 TEPE: RSI>", RSI_Overbought, " + WPR>", WPR_Overbought, " → SELL");
    Print("🎯 Kar: ", Profit_Target_Pips, "pip | SL: ", Stop_Loss_Pips, "pip");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("🔄 Dip Tepe Yakalama EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Yeterli bar kontrolü
    if(Bars(_Symbol, _Period) < MathMax(RSI_Period, MA_Period) + 5)
        return;
    
    // Indicator verilerini al
    if(CopyBuffer(rsi_handle, 0, 0, 3, rsi_buffer) < 3 ||
       CopyBuffer(wpr_handle, 0, 0, 3, wpr_buffer) < 3 ||
       CopyBuffer(ma_handle, 0, 0, 3, ma_buffer) < 3 ||
       CopyBuffer(momentum_handle, 0, 0, 3, momentum_buffer) < 3)
    {
        return;
    }
    
    // Mevcut değerler
    double rsi_current = rsi_buffer[0];
    double wpr_current = wpr_buffer[0];
    double ma_current = ma_buffer[0];
    double momentum_current = momentum_buffer[0];
    double momentum_prev = momentum_buffer[1];
    
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    // Mevcut pozisyon kontrolü
    bool has_position = HasAnyPosition();
    
    // Trailing Stop kontrolü
    if(has_position && Use_Trailing_Stop)
    {
        UpdateTrailingStop();
    }
    
    // Pozisyon varsa sinyal arama yapma
    if(has_position)
    {
        static datetime last_position_report = 0;
        if(TimeCurrent() - last_position_report >= 10)
        {
            debug_log_number++;
            Print("⏸️ [", debug_log_number, "] Pozisyon açık - Sinyal arama durduruldu");
            last_position_report = TimeCurrent();
        }
        return;
    }
    
    // Momentum değişimi hesapla
    double momentum_change = momentum_current - momentum_prev;
    
    // Trend yönü (MA ile)
    bool price_above_ma = current_price > ma_current;
    bool price_below_ma = current_price < ma_current;
    
    // 🎯 DIP YAKALAMA SİNYALİ (BUY)
    bool dip_conditions = (rsi_current <= RSI_Oversold) &&           // RSI aşırı satım
                         (wpr_current <= WPR_Oversold) &&            // WPR aşırı satım
                         (momentum_change > Min_Momentum_Change) &&  // Momentum artıyor
                         (price_below_ma);                           // Fiyat MA altında (düşüş trendi)
    
    // 🎯 TEPE YAKALAMA SİNYALİ (SELL)
    bool tepe_conditions = (rsi_current >= RSI_Overbought) &&        // RSI aşırı alım
                          (wpr_current >= WPR_Overbought) &&         // WPR aşırı alım
                          (momentum_change < -Min_Momentum_Change) && // Momentum azalıyor
                          (price_above_ma);                          // Fiyat MA üstünde (yükseliş trendi)
    
    // Debug - durum raporu
    static datetime last_debug_time = 0;
    if(TimeCurrent() - last_debug_time >= 5)
    {
        debug_log_number++;
        Print("📊 [", debug_log_number, "] DIP/TEPE ANALİZİ:");
        Print("   RSI: ", DoubleToString(rsi_current, 1), " WPR: ", DoubleToString(wpr_current, 1));
        Print("   Momentum: ", DoubleToString(momentum_current, 5), " Değişim: ", DoubleToString(momentum_change, 5));
        Print("   Fiyat: ", DoubleToString(current_price, 5), " MA: ", DoubleToString(ma_current, 5));
        Print("   DIP Koşulları: ", dip_conditions, " TEPE Koşulları: ", tepe_conditions);
        last_debug_time = TimeCurrent();
    }
    
    // DIP sinyali - BUY aç
    if(dip_conditions && !dip_signal_active)
    {
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] DIP YAKALANDI! BUY sinyali:");
        Print("   RSI: ", DoubleToString(rsi_current, 1), " (<=", RSI_Oversold, ")");
        Print("   WPR: ", DoubleToString(wpr_current, 1), " (<=", WPR_Oversold, ")");
        Print("   Momentum artış: ", DoubleToString(momentum_change, 5), " (>", Min_Momentum_Change, ")");
        Print("   Fiyat MA altında: ", price_below_ma);
        
        OpenBuyOrder();
        dip_signal_active = true;
        last_signal_time = TimeCurrent();
    }
    
    // TEPE sinyali - SELL aç
    if(tepe_conditions && !tepe_signal_active)
    {
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] TEPE YAKALANDI! SELL sinyali:");
        Print("   RSI: ", DoubleToString(rsi_current, 1), " (>=", RSI_Overbought, ")");
        Print("   WPR: ", DoubleToString(wpr_current, 1), " (>=", WPR_Overbought, ")");
        Print("   Momentum azalış: ", DoubleToString(momentum_change, 5), " (<-", Min_Momentum_Change, ")");
        Print("   Fiyat MA üstünde: ", price_above_ma);
        
        OpenSellOrder();
        tepe_signal_active = true;
        last_signal_time = TimeCurrent();
    }
    
    // Sinyal sıfırlama (zaman geçtikten sonra)
    if(TimeCurrent() - last_signal_time > 300) // 5 dakika
    {
        dip_signal_active = false;
        tepe_signal_active = false;
    }
}

//+------------------------------------------------------------------+
//| BUY emri aç                                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = ask - (Stop_Loss_Pips * _Point * 10);
    double tp = ask + (Profit_Target_Pips * _Point * 10);
    
    trade.SetExpertMagicNumber(Magic_Number);
    
    if(trade.Buy(Lot_Size, _Symbol, ask, sl, tp, "Dip_Yakalama_BUY"))
    {
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(ask, 5));
        Print("   SL: ", DoubleToString(sl, 5), " TP: ", DoubleToString(tp, 5));
        Print("   Lot: ", DoubleToString(Lot_Size, 2));
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] BUY EMRİ HATASI: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder()
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = bid + (Stop_Loss_Pips * _Point * 10);
    double tp = bid - (Profit_Target_Pips * _Point * 10);
    
    trade.SetExpertMagicNumber(Magic_Number);
    
    if(trade.Sell(Lot_Size, _Symbol, bid, sl, tp, "Tepe_Yakalama_SELL"))
    {
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] SELL EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(bid, 5));
        Print("   SL: ", DoubleToString(sl, 5), " TP: ", DoubleToString(tp, 5));
        Print("   Lot: ", DoubleToString(Lot_Size, 2));
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] SELL EMRİ HATASI: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| Herhangi bir pozisyon var mı kontrol et                         |
//+------------------------------------------------------------------+
bool HasAnyPosition()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number)
                {
                    return true;
                }
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Trailing Stop güncelle                                          |
//+------------------------------------------------------------------+
void UpdateTrailingStop()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number)
                {
                    double current_sl = PositionGetDouble(POSITION_SL);
                    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
                    int position_type = (int)PositionGetInteger(POSITION_TYPE);

                    double current_price = (position_type == POSITION_TYPE_BUY) ?
                                         SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                                         SymbolInfoDouble(_Symbol, SYMBOL_ASK);

                    double trailing_distance = Trailing_Stop_Pips * _Point * 10;
                    double new_sl = 0;

                    if(position_type == POSITION_TYPE_BUY)
                    {
                        new_sl = current_price - trailing_distance;
                        if(new_sl > current_sl && new_sl > open_price)
                        {
                            trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP));
                            debug_log_number++;
                            Print("📈 [", debug_log_number, "] BUY Trailing Stop güncellendi: ", DoubleToString(new_sl, 5));
                        }
                    }
                    else if(position_type == POSITION_TYPE_SELL)
                    {
                        new_sl = current_price + trailing_distance;
                        if((current_sl == 0 || new_sl < current_sl) && new_sl < open_price)
                        {
                            trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP));
                            debug_log_number++;
                            Print("📉 [", debug_log_number, "] SELL Trailing Stop güncellendi: ", DoubleToString(new_sl, 5));
                        }
                    }
                }
            }
        }
    }
}
