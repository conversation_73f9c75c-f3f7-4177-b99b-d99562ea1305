//+------------------------------------------------------------------+
//| Dip Tepe Yakalama EA                                             |
//| Dip noktalarında BUY, Tepe noktalarında SELL                    |
//+------------------------------------------------------------------+
#property copyright "Horizon Trading"
#property version   "1.00"

#include <Trade\Trade.mqh>
CTrade trade;

//--- Input parametreleri
input group "=== Dip/Tepe Yakalama Ayarları ==="
input int                   RSI_Period = 14;                     // RSI Periyodu
input double                RSI_Oversold = 40.0;                 // RSI Aşırı Satım Seviyesi (Dip) - Gevşetildi
input double                RSI_Overbought = 60.0;               // RSI Aşırı Alım <PERSON>i (Tepe) - Gevşetildi
input int                   WPR_Period = 14;                     // Williams %R Periyodu
input double                WPR_Oversold = -60.0;                // WPR Aşırı Satım (Dip) - Gevşetildi
input double                WPR_Overbought = -40.0;              // WPR Aşırı Alım (Tepe) - Gevşetildi

input group "=== Trend Filtresi ==="
input int                   MA_Period = 50;                      // MA Trend Filtresi
input ENUM_MA_METHOD        MA_Method = MODE_EMA;                 // MA Metodu
input bool                  Use_MA_Filter = false;               // MA Filtresi Kullan (Kapalı)

input group "=== Momentum Filtresi ==="
input int                   Momentum_Period = 10;                // Momentum Periyodu
input double                Min_Momentum_Change = 0.1;           // Minimum Momentum Değişimi - Gevşetildi
input bool                  Use_Momentum_Filter = false;         // Momentum Filtresi Kullan (Kapalı)

input group "=== Çıkış Ayarları ==="
input bool                  Use_ATR_TP_SL = true;                // ATR Bazlı TP/SL Kullan
input double                ATR_TP_Multiplier = 3.0;             // ATR TP Çarpanı
input double                ATR_SL_Multiplier = 1.5;             // ATR SL Çarpanı
input int                   ATR_Period = 14;                     // ATR Periyodu
input double                Profit_Target_Pips = 50.0;           // Kar Hedefi (pip) - ATR kapalıysa
input double                Stop_Loss_Pips = 25.0;               // Zarar Durdurma (pip) - ATR kapalıysa
input bool                  Use_Trailing_Stop = true;            // Takip Eden Stop Kullan
input double                Trailing_Stop_Pips = 15.0;           // Takip Eden Stop (pip)

input group "=== Risk Yönetimi ==="
input double                Lot_Size = 0.01;                     // Lot Büyüklüğü
input double                Risk_Percent = 2.0;                  // Risk Yüzdesi
input int                   Magic_Number = 8888;                 // Magic Number

input group "=== Zaman Filtresi ==="
input bool                  Use_Time_Filter = true;              // Zaman Filtresi Kullan
input int                   Start_Hour = 8;                      // Başlangıç Saati (0-23)
input int                   Start_Minute = 0;                    // Başlangıç Dakikası (0-59)
input int                   End_Hour = 18;                       // Bitiş Saati (0-23)
input int                   End_Minute = 0;                      // Bitiş Dakikası (0-59)
input bool                  Trade_Monday = true;                 // Pazartesi İşlem Yap
input bool                  Trade_Tuesday = true;                // Salı İşlem Yap
input bool                  Trade_Wednesday = true;              // Çarşamba İşlem Yap
input bool                  Trade_Thursday = true;               // Perşembe İşlem Yap
input bool                  Trade_Friday = true;                 // Cuma İşlem Yap
input bool                  Trade_Saturday = false;              // Cumartesi İşlem Yap
input bool                  Trade_Sunday = false;                // Pazar İşlem Yap

input group "=== Genel Ayarlar ==="
input bool                  Enable_Debug = true;                 // Debug Logları

//--- Global değişkenler
int rsi_handle, wpr_handle, ma_handle, momentum_handle, atr_handle;
double rsi_buffer[], wpr_buffer[], ma_buffer[], momentum_buffer[], atr_buffer[];
int debug_log_number = 0;

// Dip/Tepe takip değişkenleri
bool dip_signal_active = false;    // Dip sinyali aktif mi?
bool tepe_signal_active = false;   // Tepe sinyali aktif mi?
datetime last_signal_time = 0;     // Son sinyal zamanı

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Indicator handle'larını oluştur
    rsi_handle = iRSI(_Symbol, _Period, RSI_Period, PRICE_CLOSE);
    wpr_handle = iWPR(_Symbol, _Period, WPR_Period);
    ma_handle = iMA(_Symbol, _Period, MA_Period, 0, MA_Method, PRICE_CLOSE);
    momentum_handle = iMomentum(_Symbol, _Period, Momentum_Period, PRICE_CLOSE);
    atr_handle = iATR(_Symbol, _Period, ATR_Period);

    if(rsi_handle == INVALID_HANDLE || wpr_handle == INVALID_HANDLE ||
       ma_handle == INVALID_HANDLE || momentum_handle == INVALID_HANDLE ||
       atr_handle == INVALID_HANDLE)
    {
        Print("❌ Indicator handle oluşturulamadı!");
        return INIT_FAILED;
    }

    // Array ayarları
    ArraySetAsSeries(rsi_buffer, true);
    ArraySetAsSeries(wpr_buffer, true);
    ArraySetAsSeries(ma_buffer, true);
    ArraySetAsSeries(momentum_buffer, true);
    ArraySetAsSeries(atr_buffer, true);
    
    // Değişkenleri sıfırla
    debug_log_number = 0;
    dip_signal_active = false;
    tepe_signal_active = false;
    last_signal_time = 0;
    
    Print("✅ Dip Tepe Yakalama EA başlatıldı - Magic: ", Magic_Number);
    Print("📊 DIP: RSI<=", RSI_Oversold, " + WPR<=", WPR_Oversold, " → BUY");
    Print("📊 TEPE: RSI>=", RSI_Overbought, " + WPR>=", WPR_Overbought, " → SELL");
    if(Use_ATR_TP_SL)
    {
        Print("🎯 ATR TP/SL: TP=ATR×", ATR_TP_Multiplier, " | SL=ATR×", ATR_SL_Multiplier, " | ATR Periyod:", ATR_Period);
    }
    else
    {
        Print("🎯 Sabit TP/SL: TP=", Profit_Target_Pips, "pip | SL=", Stop_Loss_Pips, "pip");
    }
    Print("⚙️ MA Filtresi: ", Use_MA_Filter, " | Momentum Filtresi: ", Use_Momentum_Filter);
    Print("🔧 Gevşetilmiş parametreler - Daha fazla sinyal bekleniyor");

    if(Use_Time_Filter)
    {
        Print("⏰ Zaman Filtresi: ", StringFormat("%02d:%02d", Start_Hour, Start_Minute),
              " - ", StringFormat("%02d:%02d", End_Hour, End_Minute));
        Print("📅 İşlem Günleri: Pzt:", Trade_Monday, " Sal:", Trade_Tuesday, " Çar:", Trade_Wednesday,
              " Per:", Trade_Thursday, " Cum:", Trade_Friday, " Cmt:", Trade_Saturday, " Paz:", Trade_Sunday);
    }
    else
    {
        Print("⏰ Zaman Filtresi: KAPALI - 7/24 işlem");
    }
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("🔄 Dip Tepe Yakalama EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Yeterli bar kontrolü
    if(Bars(_Symbol, _Period) < MathMax(RSI_Period, MA_Period) + 5)
        return;
    
    // Indicator verilerini al
    if(CopyBuffer(rsi_handle, 0, 0, 3, rsi_buffer) < 3 ||
       CopyBuffer(wpr_handle, 0, 0, 3, wpr_buffer) < 3 ||
       CopyBuffer(ma_handle, 0, 0, 3, ma_buffer) < 3 ||
       CopyBuffer(momentum_handle, 0, 0, 3, momentum_buffer) < 3 ||
       CopyBuffer(atr_handle, 0, 0, 2, atr_buffer) < 2)
    {
        return;
    }

    // Mevcut değerler
    double rsi_current = rsi_buffer[0];
    double wpr_current = wpr_buffer[0];
    double ma_current = ma_buffer[0];
    double momentum_current = momentum_buffer[0];
    double momentum_prev = momentum_buffer[1];
    double atr_current = atr_buffer[0];
    
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    // Mevcut pozisyon kontrolü
    bool has_position = HasAnyPosition();
    
    // Trailing Stop kontrolü
    if(has_position && Use_Trailing_Stop)
    {
        UpdateTrailingStop();
    }
    
    // Zaman filtresi kontrolü
    if(Use_Time_Filter && !IsTimeToTrade())
    {
        static datetime last_time_report = 0;
        if(TimeCurrent() - last_time_report >= 30) // 30 saniyede bir
        {
            debug_log_number++;
            Print("⏰ [", debug_log_number, "] İşlem saati dışında - Sinyal arama durduruldu");
            last_time_report = TimeCurrent();
        }
        return;
    }

    // Pozisyon varsa sinyal arama yapma
    if(has_position)
    {
        static datetime last_position_report = 0;
        if(TimeCurrent() - last_position_report >= 10)
        {
            debug_log_number++;
            Print("⏸️ [", debug_log_number, "] Pozisyon açık - Sinyal arama durduruldu");
            last_position_report = TimeCurrent();
        }
        return;
    }
    
    // Momentum değişimi hesapla
    double momentum_change = momentum_current - momentum_prev;
    
    // Trend yönü (MA ile)
    bool price_above_ma = current_price > ma_current;
    bool price_below_ma = current_price < ma_current;
    
    // 🎯 DIP YAKALAMA SİNYALİ (BUY) - Gevşetilmiş koşullar
    bool rsi_dip = (rsi_current <= RSI_Oversold);
    bool wpr_dip = (wpr_current <= WPR_Oversold);
    bool momentum_up = Use_Momentum_Filter ? (momentum_change > Min_Momentum_Change) : true;
    bool trend_down = Use_MA_Filter ? price_below_ma : true;

    bool dip_conditions = rsi_dip && wpr_dip && momentum_up && trend_down;

    // 🎯 TEPE YAKALAMA SİNYALİ (SELL) - Gevşetilmiş koşullar
    bool rsi_tepe = (rsi_current >= RSI_Overbought);
    bool wpr_tepe = (wpr_current >= WPR_Overbought);
    bool momentum_down = Use_Momentum_Filter ? (momentum_change < -Min_Momentum_Change) : true;
    bool trend_up = Use_MA_Filter ? price_above_ma : true;

    bool tepe_conditions = rsi_tepe && wpr_tepe && momentum_down && trend_up;
    
    // Debug - detaylı durum raporu
    static datetime last_debug_time = 0;
    if(TimeCurrent() - last_debug_time >= 3) // 3 saniyede bir
    {
        debug_log_number++;
        Print("📊 [", debug_log_number, "] DIP/TEPE ANALİZİ - ", GetTimeInfo());
        Print("   Zaman Filtresi: ", Use_Time_Filter ? "AÇIK" : "KAPALI", " İşlem Zamanı: ", IsTimeToTrade());
        Print("   RSI: ", DoubleToString(rsi_current, 1), " (Dip<=", RSI_Oversold, " Tepe>=", RSI_Overbought, ")");
        Print("   WPR: ", DoubleToString(wpr_current, 1), " (Dip<=", WPR_Oversold, " Tepe>=", WPR_Overbought, ")");
        Print("   Momentum: ", DoubleToString(momentum_current, 5), " Değişim: ", DoubleToString(momentum_change, 5), " (Min:", Min_Momentum_Change, ")");
        Print("   Fiyat: ", DoubleToString(current_price, 5), " MA: ", DoubleToString(ma_current, 5));
        Print("   ATR: ", DoubleToString(atr_current, 5), " TP:", DoubleToString(atr_current * ATR_TP_Multiplier, 5), " SL:", DoubleToString(atr_current * ATR_SL_Multiplier, 5));
        Print("   Fiyat>MA: ", price_above_ma, " Fiyat<MA: ", price_below_ma);

        // Koşul detayları
        Print("   DIP Koşulları:");
        Print("     RSI<=", RSI_Oversold, ": ", rsi_dip);
        Print("     WPR<=", WPR_Oversold, ": ", wpr_dip);
        Print("     Momentum>", Min_Momentum_Change, ": ", momentum_up, " (Aktif:", Use_Momentum_Filter, ")");
        Print("     Fiyat<MA: ", trend_down, " (Aktif:", Use_MA_Filter, ")");
        Print("     TOPLAM DIP: ", dip_conditions);

        Print("   TEPE Koşulları:");
        Print("     RSI>=", RSI_Overbought, ": ", rsi_tepe);
        Print("     WPR>=", WPR_Overbought, ": ", wpr_tepe);
        Print("     Momentum<-", Min_Momentum_Change, ": ", momentum_down, " (Aktif:", Use_Momentum_Filter, ")");
        Print("     Fiyat>MA: ", trend_up, " (Aktif:", Use_MA_Filter, ")");
        Print("     TOPLAM TEPE: ", tepe_conditions);

        last_debug_time = TimeCurrent();
    }
    
    // DIP sinyali - BUY aç
    if(dip_conditions && !dip_signal_active)
    {
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] DIP YAKALANDI! BUY sinyali:");
        Print("   RSI: ", DoubleToString(rsi_current, 1), " (<=", RSI_Oversold, ")");
        Print("   WPR: ", DoubleToString(wpr_current, 1), " (<=", WPR_Oversold, ")");
        Print("   Momentum artış: ", DoubleToString(momentum_change, 5), " (>", Min_Momentum_Change, ")");
        Print("   Fiyat MA altında: ", price_below_ma);
        
        OpenBuyOrder(atr_current);
        dip_signal_active = true;
        last_signal_time = TimeCurrent();
    }
    
    // TEPE sinyali - SELL aç
    if(tepe_conditions && !tepe_signal_active)
    {
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] TEPE YAKALANDI! SELL sinyali:");
        Print("   RSI: ", DoubleToString(rsi_current, 1), " (>=", RSI_Overbought, ")");
        Print("   WPR: ", DoubleToString(wpr_current, 1), " (>=", WPR_Overbought, ")");
        Print("   Momentum azalış: ", DoubleToString(momentum_change, 5), " (<-", Min_Momentum_Change, ")");
        Print("   Fiyat MA üstünde: ", price_above_ma);
        
        OpenSellOrder(atr_current);
        tepe_signal_active = true;
        last_signal_time = TimeCurrent();
    }
    
    // Sinyal sıfırlama (zaman geçtikten sonra)
    if(TimeCurrent() - last_signal_time > 300) // 5 dakika
    {
        dip_signal_active = false;
        tepe_signal_active = false;
    }
}

//+------------------------------------------------------------------+
//| BUY emri aç                                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder(double atr_value)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl, tp;

    if(Use_ATR_TP_SL)
    {
        sl = ask - (atr_value * ATR_SL_Multiplier);
        tp = ask + (atr_value * ATR_TP_Multiplier);
    }
    else
    {
        sl = ask - (Stop_Loss_Pips * _Point * 10);
        tp = ask + (Profit_Target_Pips * _Point * 10);
    }

    trade.SetExpertMagicNumber(Magic_Number);

    if(trade.Buy(Lot_Size, _Symbol, ask, sl, tp, "Dip_Yakalama_BUY"))
    {
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(ask, 5));
        Print("   SL: ", DoubleToString(sl, 5), " TP: ", DoubleToString(tp, 5));
        Print("   Lot: ", DoubleToString(Lot_Size, 2));

        if(Use_ATR_TP_SL)
        {
            Print("   ATR: ", DoubleToString(atr_value, 5), " SL Mesafe: ", DoubleToString(atr_value * ATR_SL_Multiplier, 5));
            Print("   TP Mesafe: ", DoubleToString(atr_value * ATR_TP_Multiplier, 5), " Risk/Reward: 1:", DoubleToString(ATR_TP_Multiplier/ATR_SL_Multiplier, 2));
        }
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] BUY EMRİ HATASI: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder(double atr_value)
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl, tp;

    if(Use_ATR_TP_SL)
    {
        sl = bid + (atr_value * ATR_SL_Multiplier);
        tp = bid - (atr_value * ATR_TP_Multiplier);
    }
    else
    {
        sl = bid + (Stop_Loss_Pips * _Point * 10);
        tp = bid - (Profit_Target_Pips * _Point * 10);
    }

    trade.SetExpertMagicNumber(Magic_Number);

    if(trade.Sell(Lot_Size, _Symbol, bid, sl, tp, "Tepe_Yakalama_SELL"))
    {
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] SELL EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(bid, 5));
        Print("   SL: ", DoubleToString(sl, 5), " TP: ", DoubleToString(tp, 5));
        Print("   Lot: ", DoubleToString(Lot_Size, 2));

        if(Use_ATR_TP_SL)
        {
            Print("   ATR: ", DoubleToString(atr_value, 5), " SL Mesafe: ", DoubleToString(atr_value * ATR_SL_Multiplier, 5));
            Print("   TP Mesafe: ", DoubleToString(atr_value * ATR_TP_Multiplier, 5), " Risk/Reward: 1:", DoubleToString(ATR_TP_Multiplier/ATR_SL_Multiplier, 2));
        }
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] SELL EMRİ HATASI: ", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| Herhangi bir pozisyon var mı kontrol et                         |
//+------------------------------------------------------------------+
bool HasAnyPosition()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number)
                {
                    return true;
                }
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Trailing Stop güncelle                                          |
//+------------------------------------------------------------------+
void UpdateTrailingStop()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number)
                {
                    double current_sl = PositionGetDouble(POSITION_SL);
                    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
                    int position_type = (int)PositionGetInteger(POSITION_TYPE);

                    double current_price = (position_type == POSITION_TYPE_BUY) ?
                                         SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                                         SymbolInfoDouble(_Symbol, SYMBOL_ASK);

                    double trailing_distance = Trailing_Stop_Pips * _Point * 10;
                    double new_sl = 0;

                    if(position_type == POSITION_TYPE_BUY)
                    {
                        new_sl = current_price - trailing_distance;
                        if(new_sl > current_sl && new_sl > open_price)
                        {
                            trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP));
                            debug_log_number++;
                            Print("📈 [", debug_log_number, "] BUY Trailing Stop güncellendi: ", DoubleToString(new_sl, 5));
                        }
                    }
                    else if(position_type == POSITION_TYPE_SELL)
                    {
                        new_sl = current_price + trailing_distance;
                        if((current_sl == 0 || new_sl < current_sl) && new_sl < open_price)
                        {
                            trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP));
                            debug_log_number++;
                            Print("📉 [", debug_log_number, "] SELL Trailing Stop güncellendi: ", DoubleToString(new_sl, 5));
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| İşlem zamanı kontrolü                                            |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
    if(!Use_Time_Filter) return true;

    datetime current_time = TimeCurrent();
    MqlDateTime time_struct;
    TimeToStruct(current_time, time_struct);

    // Gün kontrolü
    bool day_allowed = false;
    switch(time_struct.day_of_week)
    {
        case 1: day_allowed = Trade_Monday; break;    // Pazartesi
        case 2: day_allowed = Trade_Tuesday; break;   // Salı
        case 3: day_allowed = Trade_Wednesday; break; // Çarşamba
        case 4: day_allowed = Trade_Thursday; break;  // Perşembe
        case 5: day_allowed = Trade_Friday; break;    // Cuma
        case 6: day_allowed = Trade_Saturday; break;  // Cumartesi
        case 0: day_allowed = Trade_Sunday; break;    // Pazar
        default: day_allowed = false;
    }

    if(!day_allowed) return false;

    // Saat kontrolü
    int current_hour = time_struct.hour;
    int current_minute = time_struct.min;
    int current_time_minutes = current_hour * 60 + current_minute;
    int start_time_minutes = Start_Hour * 60 + Start_Minute;
    int end_time_minutes = End_Hour * 60 + End_Minute;

    // Gece geçen zaman aralığı kontrolü (örn: 22:00 - 06:00)
    if(start_time_minutes > end_time_minutes)
    {
        return (current_time_minutes >= start_time_minutes || current_time_minutes <= end_time_minutes);
    }
    else
    {
        return (current_time_minutes >= start_time_minutes && current_time_minutes <= end_time_minutes);
    }
}

//+------------------------------------------------------------------+
//| Zaman bilgisi string formatında                                 |
//+------------------------------------------------------------------+
string GetTimeInfo()
{
    datetime current_time = TimeCurrent();
    MqlDateTime time_struct;
    TimeToStruct(current_time, time_struct);

    string day_names[] = {"Pazar", "Pazartesi", "Salı", "Çarşamba", "Perşembe", "Cuma", "Cumartesi"};
    string day_name = day_names[time_struct.day_of_week];

    return StringFormat("%s %02d:%02d", day_name, time_struct.hour, time_struct.min);
}
