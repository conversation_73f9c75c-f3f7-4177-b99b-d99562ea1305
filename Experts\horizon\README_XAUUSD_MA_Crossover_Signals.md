# XAUUSD MA Crossover + RSI Exit Signals İndikatörü

## 📊 Genel Bakış

Bu indikatör, XAUUSD (Altın) için özel olarak tasarlanmış gelişmiş bir hareketli ortalama kesişim sistemidir. Üç farklı MA periyodu kullanarak giriş sinyalleri üretir ve RSI bazlı iki aşamalı çıkış stratejisi ile pozisyon yönetimi sağlar.

## 🎯 Ana Özellikler

### ✅ Giriş Sistemi
- **MA7 x MA14 Kesişimi**: Ana sinyal kaynağı
- **MA50 Katı Onayı**: Trend filtresi (barın tamamı MA50'nin doğru tarafında)
- **Bekletmeli Onay**: Kesişim sonrası belirli süre içinde onay bekleme
- **Çift Doğrulama**: Hem kesişim hem trend onayı gerekli

### ✅ Çıkış Sistemi
- **RSI Bazlı**: <PERSON><PERSON><PERSON><PERSON><PERSON> alım/satım seviyelerini kullanır
- **<PERSON><PERSON>lı**: Seviyeyi geçip geri dönmeyi bekler
- **Momentum Onayı**: Trend değişimini doğrular

### ✅ Görsel Sistem
- **5 Çizgi**: 3 MA + 2 Sinyal oku
- **Renkli Kodlama**: Kolay tanımlama
- **Özelleştirilebilir**: Renkler ve semboller ayarlanabilir

## 🔧 Teknik Parametreler

### MA Ayarları
| Parametre | Varsayılan | Açıklama |
|-----------|------------|----------|
| MA_Period_Long | 50 | Uzun vadeli trend (MA50) |
| MA_Period_Mid | 14 | Orta vadeli sinyal (MA14) |
| MA_Period_Short | 7 | Kısa vadeli sinyal (MA7) |
| MA_Method | SMA | Hesaplama metodu |
| MA_Applied_Price | CLOSE | Uygulanan fiyat |

### Sinyal Ayarları
| Parametre | Varsayılan | Açıklama |
|-----------|------------|----------|
| Arrow_Offset_Pips | 5 | Ok mesafesi |
| Confirmation_Bars | 5 | MA50 onay bekleme süresi |
| Signal_Arrow_Code_Buy | 233 | BUY ok kodu |
| Signal_Arrow_Code_Sell | 234 | SELL ok kodu |

### RSI Çıkış Ayarları
| Parametre | Varsayılan | Açıklama |
|-----------|------------|----------|
| RSI_Period | 14 | RSI hesaplama periyodu |
| RSI_Overbought | 70 | Aşırı alım seviyesi |
| RSI_Oversold | 30 | Aşırı satım seviyesi |

## 📈 Sinyal Mantığı

### BUY Sinyali Oluşumu
1. **Kesişim Tespiti**: MA7, MA14'ü aşağıdan yukarıya keser
2. **Bekletme**: Sinyal beklemede kalır
3. **MA50 Onayı**: Barın LOW'u MA50'nin üzerinde olmalı
4. **Sinyal Onayı**: Yeşil ok çizilir

### SELL Sinyali Oluşumu
1. **Kesişim Tespiti**: MA7, MA14'ü yukarıdan aşağıya keser
2. **Bekletme**: Sinyal beklemede kalır
3. **MA50 Onayı**: Barın HIGH'ı MA50'nin altında olmalı
4. **Sinyal Onayı**: Kırmızı ok çizilir

### BUY Çıkış Stratejisi
1. **1. Aşama**: RSI ≥ 70 → Çıkış hazırlığı
2. **2. Aşama**: RSI < 70 → Çıkış sinyali (Mavi X)

### SELL Çıkış Stratejisi
1. **1. Aşama**: RSI ≤ 30 → Çıkış hazırlığı
2. **2. Aşama**: RSI > 30 → Çıkış sinyali (Mor X)

## 🎨 Görsel Rehber

### Çizgiler
- **🔵 Mavi Çizgi**: MA50 (Trend filtresi)
- **🟠 Turuncu Çizgi**: MA14 (Orta vadeli)
- **🟡 Sarı Çizgi**: MA7 (Kısa vadeli)

### Sinyaller
- **🟢 Yeşil Ok**: BUY giriş sinyali
- **🔴 Kırmızı Ok**: SELL giriş sinyali
- **🔵 Mavi X**: BUY çıkış sinyali
- **🟣 Mor X**: SELL çıkış sinyali

## 📋 Kurulum ve Kullanım

### Kurulum
1. `XAUUSD_MA_Crossover_Signals.mq5` dosyasını `MQL5/Indicators/` klasörüne kopyalayın
2. MetaTrader 5'i yeniden başlatın
3. Navigator'da "Custom Indicators" altında bulun

### Kullanım
1. XAUUSD grafiğini açın
2. 1 dakikalık (M1) zaman dilimini seçin
3. İndikatörü grafiğe sürükleyin
4. Parametreleri ihtiyacınıza göre ayarlayın

### Önerilen Ayarlar
- **Timeframe**: M1 (1 dakika)
- **Symbol**: XAUUSD
- **MA Method**: SMA (varsayılan)
- **Confirmation_Bars**: 5-10 arası

## 🔔 Bildirim Sistemi

### Mevcut Bildirim Türleri
- **Popup Uyarıları**: Ekranda mesaj kutusu
- **Ses Bildirimleri**: alert.wav dosyası
- **Email Bildirimleri**: E-posta gönderimi
- **Push Bildirimleri**: Mobil bildirim

### Bildirim Mesajları
- Giriş: "BUY/SELL Sinyal - XAUUSD: [Fiyat] ([Zaman])"
- Çıkış: "BUY/SELL Çıkış (RSI [Değer]) - Kar: [Pip] pip"

## 📊 Performans Özellikleri

### Avantajlar
- **Yüksek Doğruluk**: Çift onay sistemi
- **Trend Uyumu**: MA50 filtresi
- **Erken Giriş**: MA7-MA14 kesişimi
- **Akıllı Çıkış**: RSI momentum analizi
- **Düşük Gürültü**: Katı filtreleme

### Sınırlamalar
- **Yan Trend**: Düşük performans
- **Gecikmeli Giriş**: Onay bekleme süresi
- **Volatilite**: Yüksek volatilitede sahte sinyaller

## 🛠️ Teknik Detaylar

### Buffer Yapısı
- Buffer 0: MA50 çizgisi
- Buffer 1: MA14 çizgisi
- Buffer 2: MA7 çizgisi
- Buffer 3: BUY sinyalleri
- Buffer 4: SELL sinyalleri
- Buffer 5: BUY çıkış sinyalleri
- Buffer 6: SELL çıkış sinyalleri

### Handle Yönetimi
- Otomatik handle oluşturma
- Hata kontrolü ve güvenli temizleme
- Memory leak önleme

### Indexleme Sistemi
- Normal array indexleme (time series değil)
- Efficient data copying
- Optimized calculation loop

## 🔍 Sorun Giderme

### Sık Karşılaşılan Sorunlar

#### Sinyal Çıkmıyor
- **Sebep**: MA50 onay koşulu çok katı
- **Çözüm**: Confirmation_Bars parametresini artırın

#### Çok Fazla Sinyal
- **Sebep**: Volatil piyasa koşulları
- **Çözüm**: MA periyotlarını artırın

#### Geç Giriş
- **Sebep**: Bekletme süresi uzun
- **Çözüm**: Confirmation_Bars'ı azaltın

### Debug Logları
Konsol (Ctrl+T) üzerinden şu mesajları takip edin:
- "⏳ BUY Sinyal beklemede"
- "✅ BUY Sinyal onaylandı"
- "❌ BUY Sinyal iptal"
- "🔵 BUY Çıkış Tamamlandı"

## 📞 Destek ve İletişim

### Geliştirici
- **Ad**: Emre Usun
- **Versiyon**: 1.00
- **Platform**: MetaTrader 5

### Güncellemeler
- Bug fix'ler ve iyileştirmeler için düzenli güncellemeler
- Yeni özellik talepleri değerlendirilir
- Performans optimizasyonları

## 📚 Strategi Örnekleri

### Konservatif Yaklaşım
```
MA_Period_Long = 100
MA_Period_Mid = 21
MA_Period_Short = 10
Confirmation_Bars = 10
RSI_Overbought = 75
RSI_Oversold = 25
```

### Agresif Yaklaşım
```
MA_Period_Long = 30
MA_Period_Mid = 10
MA_Period_Short = 5
Confirmation_Bars = 3
RSI_Overbought = 65
RSI_Oversold = 35
```

### Scalping Ayarları
```
MA_Period_Long = 20
MA_Period_Mid = 8
MA_Period_Short = 3
Confirmation_Bars = 2
RSI_Overbought = 60
RSI_Oversold = 40
```

## 🧪 Backtest Rehberi

### Test Parametreleri
- **Sembol**: XAUUSD
- **Timeframe**: M1
- **Test Periyodu**: En az 3 ay
- **Spread**: Gerçek spread değerleri
- **Slippage**: 3-5 point

### Performans Metrikleri
- **Win Rate**: %60+ hedeflenir
- **Risk/Reward**: 1:1.5 minimum
- **Maximum Drawdown**: %10 altında
- **Profit Factor**: 1.3+ hedeflenir

### Test Adımları
1. Strategy Tester'ı açın
2. İndikatörü Expert Advisor'a dönüştürün
3. Test parametrelerini ayarlayın
4. Optimization yapın
5. Sonuçları analiz edin

## 🔄 Versiyon Geçmişi

### v1.00 (Mevcut)
- ✅ MA7-MA14-MA50 üçlü sistem
- ✅ Bekletmeli onay mekanizması
- ✅ RSI iki aşamalı çıkış
- ✅ Katı MA50 filtresi
- ✅ Çoklu bildirim sistemi
- ✅ Görsel sinyal sistemi

### Gelecek Güncellemeler (Planlanan)
- 🔄 Stochastic çıkış alternatifi
- 🔄 ATR bazlı dinamik stop loss
- 🔄 Telegram entegrasyonu
- 🔄 Multi-timeframe analiz
- 🔄 Otomatik risk yönetimi

## 📖 Algoritma Detayları

### Giriş Algoritması
```
1. MA7[i] > MA14[i] AND MA7[i-1] <= MA14[i-1]  // Kesişim
2. Bekletme: pending_signal.signal_type = 1
3. Onay: Low[i] > MA50[i]  // Katı filtre
4. Sinyal: Buy_Signals_Buffer[i] = Low[i] - offset
```

### Çıkış Algoritması
```
1. RSI[i] >= 70  // 1. Aşama: Hazırlık
2. exit_ready = true
3. RSI[i] < 70   // 2. Aşama: Çıkış
4. Buy_Exit_Buffer[i] = High[i] + offset
```

### Pozisyon Yönetimi
```
struct OpenPosition {
    int position_type;     // 1=BUY, -1=SELL, 0=NONE
    int entry_bar;         // Giriş bar index
    double entry_price;    // Giriş fiyatı
    bool exit_ready;       // Çıkış hazır mı?
}
```

## 🎓 Eğitim Materyalleri

### Temel Kavramlar
- **Moving Average**: Trend yönünü belirler
- **Crossover**: İki MA'nın kesişimi
- **RSI**: Momentum osilatörü (0-100)
- **Overbought/Oversold**: Aşırı alım/satım

### Strateji Mantığı
1. **Trend Takibi**: MA50 ana trend yönünü gösterir
2. **Giriş Timing**: MA7-MA14 kesişimi erken sinyal
3. **Onay Filtresi**: Katı MA50 kontrolü
4. **Çıkış Timing**: RSI momentum değişimi

### Risk Yönetimi
- **Position Size**: Hesap bakiyesinin %1-2'si
- **Stop Loss**: MA50 seviyesi
- **Take Profit**: 1.5-2x stop loss mesafesi
- **Maximum Risk**: Günlük %5 kayıp limiti

## 🔧 Özelleştirme Rehberi

### Renk Değişiklikleri
```mql5
#property indicator_color1  clrBlue     // MA50
#property indicator_color2  clrOrange   // MA14
#property indicator_color3  clrYellow   // MA7
#property indicator_color4  clrLime     // BUY
#property indicator_color5  clrRed      // SELL
```

### Arrow Kodları
- 233: Yukarı ok (BUY)
- 234: Aşağı ok (SELL)
- 251: X işareti (Çıkış)
- 159: Üçgen yukarı
- 160: Üçgen aşağı

### Ses Dosyaları
- alert.wav: Varsayılan uyarı sesi
- alert2.wav: Alternatif ses
- Özel ses: Sounds klasörüne ekleyin

---

**⚠️ Risk Uyarısı**: Bu indikatör sadece analiz amaçlıdır. Gerçek işlem yapmadan önce demo hesapta test edin ve risk yönetimi uygulayın.

**📄 Lisans**: Bu yazılım eğitim ve araştırma amaçlıdır. Ticari kullanım için izin gereklidir.

**🔄 Son Güncelleme**: 2025-01-19
