//+------------------------------------------------------------------+
//|                                            SuperSentinel_EA.mq5 |
//|                        Copyright 2025, Emre USUN - Horizon Team |
//|     Super Arrow Sinyalleri + Sentinel S/R Sistemi + Telegram    |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Emre USUN - Horizon Team"
#property link      "https://github.com/emreusun87"
#property version   "1.0"
#property description "Super Arrow Sinyalleri ile Otomatik Trading + Sentinel S/R + Telegram"

#include <Trade\Trade.mqh>

//--- Input parameters
input group "=== Super Arrow Ayarları ==="
input int FasterMA = 5;                    // Hızlı MA Periyodu
input int SlowerMA = 12;                   // Yavaş MA Periyodu
input int RSIPeriod = 14;                  // RSI Periyodu
input int BollingerPeriod = 20;            // Bollinger Bands Periyodu
input double BollingerDeviation = 2.0;     // Bollinger Sapma
input int BullsPowerPeriod = 13;           // Bulls Power Periyodu
input int BearsPowerPeriod = 13;           // Bears Power Periyodu
input int MinSignalConditions = 3;         // Minimum sinyal koşulu (3-6 arası)

enum SIGNAL_MODE
{
    SIGNAL_TICK_BASED,    // Tick bazlı (anlık)
    SIGNAL_BAR_BASED      // Bar bazlı (bar kapanışında)
};

input SIGNAL_MODE SignalMode = SIGNAL_BAR_BASED;  // Sinyal modu
input bool EnablePatternAnalysis = true;              // Pattern Analizi Aktif

input group "=== Destek/Direnç Ayarları ==="
input int SR_LookbackPeriod = 100;         // S/R geriye bakış periyodu
input int SR_MinTouchCount = 2;            // Minimum test sayısı
input double SR_TouchTolerance = 0.5;      // Test toleransı (pip)
input double SafeZoneRatio = 0.1;          // Güvenli alan oranı (%10)

input group "=== Risk Yönetimi ==="
input double FixedLotSize = 0.01;          // Sabit lot boyutu
input double FixedSLDollar = 1.0;          // SL hedefi (dolar)
input double FixedTPDollar = 2.0;          // TP hedefi (dolar)
input double MaxSpread = 3.0;              // Maksimum spread (pip)
input bool ForceReanalysis = false;       // Parametreler değiştiğinde yeniden analiz et

input group "=== Trading Ayarları ==="
input bool EnableAutoTrade = false;        // Otomatik işlem
input int MagicNumber = 123456;            // Magic number
input int MaxDailyTrades = 10;             // Günlük max işlem

input group "=== History Analiz Ayarları ==="
input bool EnableHistoryAnalysis = true;   // Geçmiş analizi etkinleştir
input int HistoryAnalysisBars = 1000;      // Analiz edilecek bar sayısı
input bool ShowHistoryOnChart = true;      // Geçmiş sinyalleri grafikte göster
input bool DetailedHistoryLog = true;      // Detaylı geçmiş log
input bool ShowLossAnalysis = true;        // Zararlı işlem analizi göster

input group "=== Telegram Ayarları ==="
input bool EnableTelegram = false;         // Telegram bildirimleri
input string TelegramBotToken = "";        // Bot Token
input string TelegramChatID = "";          // Chat ID
input bool SendSignalAlerts = true;        // Sinyal bildirimleri
input bool SendTradeAlerts = true;         // İşlem bildirimleri
input bool SendSRAlerts = true;            // S/R bildirimleri

//--- Global variables
CTrade trade;

// Super Arrow Handles
int fastMA_handle = INVALID_HANDLE;
int slowMA_handle = INVALID_HANDLE;
int rsi_handle = INVALID_HANDLE;
int bb_handle = INVALID_HANDLE;
int bulls_handle = INVALID_HANDLE;
int bears_handle = INVALID_HANDLE;

// Sinyal kontrolü
datetime lastBarTime = 0;
string lastSignal = "";
datetime lastSignalTime = 0;

// S/R seviyeleri
double currentSupportLevel = 0;
double currentResistanceLevel = 0;
datetime lastSR_UpdateTime = 0;

// Trading istatistikleri
int dailyTradeCount = 0;
datetime lastTradeDate = 0;
double dailyStartBalance = 0;

// Telegram
datetime lastTelegramTime = 0;
int lastUpdateId = 0;

// History Analysis
struct HistorySignal
{
    datetime time;
    string type;           // "BUY" or "SELL"
    double price;          // Sinyal fiyatı
    double support_level;
    double resistance_level;
    int conditions_met;    // Kaç koşul sağlandı (5'ten kaçı)
};

HistorySignal historySignals[];
int totalHistorySignals = 0;
int profitableHistorySignals = 0;
bool historyAnalysisCompleted = false;
ENUM_TIMEFRAMES lastTimeframe = PERIOD_CURRENT;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Trade setup
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(30);
    trade.SetTypeFilling(ORDER_FILLING_FOK);
    
    // Super Arrow handle'larını oluştur
    fastMA_handle = iMA(_Symbol, _Period, FasterMA, 0, MODE_EMA, PRICE_CLOSE);
    slowMA_handle = iMA(_Symbol, _Period, SlowerMA, 0, MODE_EMA, PRICE_CLOSE);
    rsi_handle = iRSI(_Symbol, _Period, RSIPeriod, PRICE_CLOSE);
    bb_handle = iBands(_Symbol, _Period, BollingerPeriod, 0, BollingerDeviation, PRICE_CLOSE);
    bulls_handle = iBullsPower(_Symbol, _Period, BullsPowerPeriod);
    bears_handle = iBearsPower(_Symbol, _Period, BearsPowerPeriod);
    
    // Handle kontrolü
    if(fastMA_handle == INVALID_HANDLE || slowMA_handle == INVALID_HANDLE ||
       rsi_handle == INVALID_HANDLE || bb_handle == INVALID_HANDLE ||
       bulls_handle == INVALID_HANDLE || bears_handle == INVALID_HANDLE)
    {
        Print("❌ Super Arrow handle'ları oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // İlk S/R hesaplama
    CalculateSupportResistance();

    // Timeframe'i kaydet
    lastTimeframe = _Period;

    // Geçmiş analizi durumunu her zaman sıfırla (güvenlik için)
    Print("🔄 Geçmiş analizi durumu sıfırlanıyor...");
    historyAnalysisCompleted = false;
    ArrayResize(historySignals, 0);
    totalHistorySignals = 0;
    profitableHistorySignals = 0;

    if(ForceReanalysis)
        Print("🔄 ForceReanalysis=true - Zorla yeniden analiz yapılacak");

    Print("✅ Geçmiş analizi durumu temizlendi");

    // İstatistikleri başlat
    dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    dailyTradeCount = 0;
    lastTradeDate = TimeCurrent();
    
    // Telegram başlangıç mesajı
    if(EnableTelegram)
    {
        string startMessage = "🚀 <b>SUPER SENTINEL EA BAŞLATILDI</b>\n\n";
        startMessage += "📊 <b>Sembol:</b> " + _Symbol + "\n";
        startMessage += "⏰ <b>Timeframe:</b> " + GetTimeframeName(_Period) + "\n";
        startMessage += "🤖 <b>AutoTrade:</b> " + (EnableAutoTrade ? "✅ Açık" : "❌ Kapalı") + "\n";
        startMessage += "📈 <b>Sinyal Modu:</b> " + (SignalMode == SIGNAL_TICK_BASED ? "Tick Bazlı" : "Bar Bazlı") + "\n";
        startMessage += "💰 <b>Lot Size:</b> " + DoubleToString(FixedLotSize, 2) + "\n";
        startMessage += "🎯 <b>SL/TP:</b> $" + DoubleToString(FixedSLDollar, 2) + "/$" + DoubleToString(FixedTPDollar, 2);
        
        SendTelegramMessage(startMessage);
    }
    
    Print("🚀 Super Sentinel EA başlatıldı");
    Print("   Sinyal Modu: ", SignalMode == SIGNAL_TICK_BASED ? "Tick Bazlı" : "Bar Bazlı");
    Print("   AutoTrade: ", EnableAutoTrade ? "Açık" : "Kapalı");
    Print("   Lot: ", FixedLotSize, " | SL: $", FixedSLDollar, " | TP: $", FixedTPDollar);

    // History Analysis başlat (indicator'lar hazır olduğunda)
    if(EnableHistoryAnalysis)
    {
        Print("📊 Geçmiş analizi başlatılıyor...");
        Print("🔍 Başlangıç durumu - historyAnalysisCompleted:", historyAnalysisCompleted, " totalSignals:", totalHistorySignals);

        // Basit kontrol - eğer hazırsa hemen başlat, değilse timer kullan
        if(AreIndicatorsReady())
        {
            Print("✅ Indicator'lar zaten hazır, analiz başlatılıyor...");
            PerformHistoryAnalysis();
        }
        else
        {
            Print("⏳ Indicator'ların hazır olması için timer başlatılıyor...");
            EventSetTimer(1); // 1 saniye sonra kontrol et
        }
    }

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Timer'ı durdur
    EventKillTimer();

    // Handle'ları temizle
    if(fastMA_handle != INVALID_HANDLE) IndicatorRelease(fastMA_handle);
    if(slowMA_handle != INVALID_HANDLE) IndicatorRelease(slowMA_handle);
    if(rsi_handle != INVALID_HANDLE) IndicatorRelease(rsi_handle);
    if(bb_handle != INVALID_HANDLE) IndicatorRelease(bb_handle);
    if(bulls_handle != INVALID_HANDLE) IndicatorRelease(bulls_handle);
    if(bears_handle != INVALID_HANDLE) IndicatorRelease(bears_handle);

    // Chart objelerini temizle
    CleanupOldHistoryObjects();

    // Telegram bitiş mesajı
    if(EnableTelegram)
    {
        string endMessage = "🛑 <b>SUPER SENTINEL EA DURDURULDU</b>\n\n";
        endMessage += "📊 <b>Günlük İşlem:</b> " + IntegerToString(dailyTradeCount) + "\n";
        endMessage += "💰 <b>Günlük P&L:</b> $" + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE) - dailyStartBalance, 2);
        
        SendTelegramMessage(endMessage);
    }
    
    Print("🛑 Super Sentinel EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Timeframe değişikliği kontrolü
    if(_Period != lastTimeframe)
    {
        Print("📊 Timeframe değişikliği algılandı: ", EnumToString(lastTimeframe), " -> ", EnumToString(_Period));
        HandleTimeframeChange();
        lastTimeframe = _Period;
        return; // Bu tick'te başka işlem yapma
    }

    // Yeni bar kontrolü
    datetime currentBarTime = iTime(_Symbol, _Period, 0);
    bool isNewBar = (currentBarTime != lastBarTime);
    
    if(isNewBar)
    {
        lastBarTime = currentBarTime;
        
        // S/R seviyelerini güncelle (her yeni bar'da)
        CalculateSupportResistance();
        
        // Günlük trade sayacını sıfırla
        MqlDateTime currentTime, lastTime;
        TimeToStruct(TimeCurrent(), currentTime);
        TimeToStruct(lastTradeDate, lastTime);

        if(currentTime.day != lastTime.day)
        {
            dailyTradeCount = 0;
            dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
            lastTradeDate = TimeCurrent();
        }
    }
    
    // Geçmiş analizi henüz tamamlanmadıysa kontrol et (timer backup)
    if(EnableHistoryAnalysis && !historyAnalysisCompleted && isNewBar)
    {
        Print("🔄 OnTick backup kontrolü - totalSignals:", totalHistorySignals);
        if(AreIndicatorsReady())
        {
            Print("✅ OnTick'te indicator'lar hazır bulundu, analiz başlatılıyor...");
            PerformHistoryAnalysis();
            EventKillTimer(); // Timer varsa durdur
        }
        else
        {
            Print("⏳ OnTick'te indicator'lar henüz hazır değil");
        }
    }

    // Sinyal analizi
    if(SignalMode == SIGNAL_TICK_BASED || isNewBar)
    {
        AnalyzeSignals();
    }
    
    // Telegram komutları kontrol et
    if(EnableTelegram && TimeCurrent() - lastTelegramTime > 5)
    {
        CheckTelegramCommands();
        lastTelegramTime = TimeCurrent();
    }
    
    // Aktif pozisyonları yönet
    ManageActivePositions();
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
    Print("⏰ OnTimer çağrıldı - EnableHistoryAnalysis:", EnableHistoryAnalysis, " historyAnalysisCompleted:", historyAnalysisCompleted);
    Print("⏰ Sinyal sayısı:", totalHistorySignals, " Array boyutu:", ArraySize(historySignals));

    // Eğer geçmiş analizi henüz tamamlanmadıysa ve etkinse
    if(EnableHistoryAnalysis && !historyAnalysisCompleted)
    {
        Print("🔍 Indicator'lar kontrol ediliyor...");
        // Indicator handle'ları hazır mı kontrol et
        if(AreIndicatorsReady())
        {
            Print("✅ Indicator'lar hazır, geçmiş analizi başlatılıyor...");
            PerformHistoryAnalysis();
            EventKillTimer(); // Timer'ı durdur
            Print("⏰ Timer durduruldu - analiz tamamlandı");
        }
        else
        {
            Print("⏳ Indicator'ların hazır olması bekleniyor...");
        }
    }
    else
    {
        if(!EnableHistoryAnalysis)
        {
            Print("⏰ Timer durdurulacak - geçmiş analizi devre dışı");
        }
        else if(historyAnalysisCompleted)
        {
            Print("🐛 BUG TESPIT EDİLDİ! historyAnalysisCompleted=true ama analiz yapılmamış!");
            Print("🔧 Zorla yeniden analiz başlatılıyor...");
            historyAnalysisCompleted = false; // Zorla sıfırla
            totalHistorySignals = 0;
            ArrayResize(historySignals, 0);

            if(AreIndicatorsReady())
            {
                PerformHistoryAnalysis();
                EventKillTimer();
                Print("⏰ Timer durduruldu - bug düzeltildi");
            }
            else
            {
                Print("⏳ Indicator'lar hazır değil, beklemeye devam...");
                return; // Timer'ı durdurmadan devam et
            }
        }

        EventKillTimer(); // Timer'ı durdur
    }
}

//+------------------------------------------------------------------+
//| Timeframe ismini al                                              |
//+------------------------------------------------------------------+
string GetTimeframeName(ENUM_TIMEFRAMES period)
{
    switch(period)
    {
        case PERIOD_M1:  return "M1";
        case PERIOD_M5:  return "M5";
        case PERIOD_M15: return "M15";
        case PERIOD_M30: return "M30";
        case PERIOD_H1:  return "H1";
        case PERIOD_H4:  return "H4";
        case PERIOD_D1:  return "D1";
        case PERIOD_W1:  return "W1";
        case PERIOD_MN1: return "MN1";
        default:         return IntegerToString(period);
    }
}

//+------------------------------------------------------------------+
//| Timeframe değişikliğini işle                                    |
//+------------------------------------------------------------------+
void HandleTimeframeChange()
{
    Print("🔄 Timeframe değişikliği işleniyor...");

    // Eski chart objelerini temizle
    CleanupOldHistoryObjects();

    // Indicator handle'larını yeniden oluştur
    RecreateIndicatorHandles();

    // Geçmiş analizi durumunu sıfırla
    historyAnalysisCompleted = false;
    ArrayResize(historySignals, 0);
    totalHistorySignals = 0;
    profitableHistorySignals = 0;

    // S/R seviyelerini yeniden hesapla
    CalculateSupportResistance();

    // Yeni timeframe için geçmiş analizi başlat
    if(EnableHistoryAnalysis)
    {
        Print("📊 Yeni timeframe için geçmiş analizi başlatılıyor...");
        EventSetTimer(1); // 1 saniye sonra kontrol et
    }

    Print("✅ Timeframe değişikliği tamamlandı");
}



//+------------------------------------------------------------------+
//| Indicator handle'larını yeniden oluştur                         |
//+------------------------------------------------------------------+
void RecreateIndicatorHandles()
{
    Print("🔄 Indicator handle'ları yeniden oluşturuluyor...");

    // Eski handle'ları temizle
    if(fastMA_handle != INVALID_HANDLE) IndicatorRelease(fastMA_handle);
    if(slowMA_handle != INVALID_HANDLE) IndicatorRelease(slowMA_handle);
    if(rsi_handle != INVALID_HANDLE) IndicatorRelease(rsi_handle);
    if(bb_handle != INVALID_HANDLE) IndicatorRelease(bb_handle);
    if(bulls_handle != INVALID_HANDLE) IndicatorRelease(bulls_handle);
    if(bears_handle != INVALID_HANDLE) IndicatorRelease(bears_handle);

    // Yeni handle'ları oluştur
    fastMA_handle = iMA(_Symbol, _Period, FasterMA, 0, MODE_EMA, PRICE_CLOSE);
    slowMA_handle = iMA(_Symbol, _Period, SlowerMA, 0, MODE_EMA, PRICE_CLOSE);
    rsi_handle = iRSI(_Symbol, _Period, RSIPeriod, PRICE_CLOSE);
    bb_handle = iBands(_Symbol, _Period, BollingerPeriod, 0, BollingerDeviation, PRICE_CLOSE);
    bulls_handle = iBullsPower(_Symbol, _Period, BullsPowerPeriod);
    bears_handle = iBearsPower(_Symbol, _Period, BearsPowerPeriod);

    // Handle kontrolü
    if(fastMA_handle == INVALID_HANDLE || slowMA_handle == INVALID_HANDLE ||
       rsi_handle == INVALID_HANDLE || bb_handle == INVALID_HANDLE ||
       bulls_handle == INVALID_HANDLE || bears_handle == INVALID_HANDLE)
    {
        Print("❌ Yeni timeframe için indicator handle'ları oluşturulamadı!");
        return;
    }

    Print("✅ Indicator handle'ları yeniden oluşturuldu");
}

//+------------------------------------------------------------------+
//| Indicator'ların hazır olup olmadığını kontrol et                |
//+------------------------------------------------------------------+
bool AreIndicatorsReady()
{
    // Yeterli bar var mı kontrol et
    int totalBars = Bars(_Symbol, _Period);
    int requiredBars = HistoryAnalysisBars + SlowerMA + BollingerPeriod + 50;
    if(totalBars < requiredBars)
    {
        Print("⏳ Yeterli bar yok. Mevcut: ", totalBars, " Gerekli: ", requiredBars);
        return false;
    }

    // Handle'lar geçerli mi kontrol et
    if(fastMA_handle == INVALID_HANDLE || slowMA_handle == INVALID_HANDLE ||
       rsi_handle == INVALID_HANDLE || bb_handle == INVALID_HANDLE ||
       bulls_handle == INVALID_HANDLE || bears_handle == INVALID_HANDLE)
    {
        Print("⏳ Geçersiz handle var");
        return false;
    }

    // Yeterli veri hesaplanmış mı kontrol et
    int minRequiredBars = HistoryAnalysisBars + 50; // Güvenlik marjı

    int fastMA_calc = BarsCalculated(fastMA_handle);
    int slowMA_calc = BarsCalculated(slowMA_handle);
    int rsi_calc = BarsCalculated(rsi_handle);
    int bb_calc = BarsCalculated(bb_handle);
    int bulls_calc = BarsCalculated(bulls_handle);
    int bears_calc = BarsCalculated(bears_handle);

    Print("⏳ Indicator durumu - FastMA:", fastMA_calc, " SlowMA:", slowMA_calc,
          " RSI:", rsi_calc, " BB:", bb_calc, " Bulls:", bulls_calc, " Bears:", bears_calc,
          " (Gerekli:", minRequiredBars, ")");

    if(fastMA_calc < minRequiredBars || slowMA_calc < minRequiredBars ||
       rsi_calc < minRequiredBars || bb_calc < minRequiredBars ||
       bulls_calc < minRequiredBars || bears_calc < minRequiredBars)
    {
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Super Arrow sinyallerini analiz et (Gömülü Algoritma)           |
//+------------------------------------------------------------------+
void AnalyzeSignals()
{
    // Yeterli veri var mı kontrol et
    if(Bars(_Symbol, _Period) < SlowerMA + BollingerPeriod + 50) return;

    // Handle'lar hazır mı kontrol et
    if(BarsCalculated(fastMA_handle) < 10 || BarsCalculated(slowMA_handle) < 10 ||
       BarsCalculated(rsi_handle) < 10 || BarsCalculated(bb_handle) < 10 ||
       BarsCalculated(bulls_handle) < 10 || BarsCalculated(bears_handle) < 10)
        return;

    // Buffer'ları hazırla
    double fastMA[3], slowMA[3], rsi_values[3];
    double bb_upper[3], bb_lower[3], bb_middle[3];
    double bulls_power[3], bears_power[3];

    // Veri kopyala (son 3 bar)
    if(CopyBuffer(fastMA_handle, 0, 0, 3, fastMA) <= 0) return;
    if(CopyBuffer(slowMA_handle, 0, 0, 3, slowMA) <= 0) return;
    if(CopyBuffer(rsi_handle, 0, 0, 3, rsi_values) <= 0) return;
    if(CopyBuffer(bb_handle, 1, 0, 3, bb_upper) <= 0) return;    // Upper band
    if(CopyBuffer(bb_handle, 2, 0, 3, bb_lower) <= 0) return;    // Lower band
    if(CopyBuffer(bb_handle, 0, 0, 3, bb_middle) <= 0) return;   // Middle band
    if(CopyBuffer(bulls_handle, 0, 0, 3, bulls_power) <= 0) return;
    if(CopyBuffer(bears_handle, 0, 0, 3, bears_power) <= 0) return;

    // Mevcut ve önceki bar indeksleri
    int current_bar = SignalMode == SIGNAL_TICK_BASED ? 0 : 1;
    int prev_bar = current_bar + 1;

    // Fiyat verileri
    double current_close = iClose(_Symbol, _Period, current_bar);
    double prev_close = iClose(_Symbol, _Period, prev_bar);
    double current_high = iHigh(_Symbol, _Period, current_bar);
    double current_low = iLow(_Symbol, _Period, current_bar);

    // === SUPER ARROW SİNYAL ALGORİTMASI (GÖMÜLÜ) ===

    // 1. MA Cross Kontrolü
    bool ma_cross_up = (fastMA[current_bar] > slowMA[current_bar]) &&
                       (fastMA[prev_bar] <= slowMA[prev_bar]);
    bool ma_cross_down = (fastMA[current_bar] < slowMA[current_bar]) &&
                         (fastMA[prev_bar] >= slowMA[prev_bar]);

    // 2. RSI Cross Kontrolü
    bool rsi_cross_up = (rsi_values[current_bar] > 50.0) &&
                        (rsi_values[prev_bar] <= 50.0);
    bool rsi_cross_down = (rsi_values[current_bar] < 50.0) &&
                          (rsi_values[prev_bar] >= 50.0);

    // 3. Bollinger Bands Kontrolü (Super Arrow DOĞRU mantık)
    // BUY: Alt banda giriş (oversold)
    bool bb_lower_condition = (current_close < bb_lower[current_bar]);
    // SELL: Üst banda çıkış (overbought)
    bool bb_upper_condition = (current_close > bb_upper[current_bar]);

    // 4. Bulls/Bears Power Kontrolü (Super Arrow DOĞRU mantık)
    // BUY: Bears power < 0 ve güçleniyor (azalıyor)
    bool bears_strengthening = (bears_power[current_bar] < 0.0) &&
                               (bears_power[prev_bar] < bears_power[current_bar]);
    // SELL: Bulls power > 0 ve zayıflıyor (azalıyor)
    bool bulls_weakening = (bulls_power[current_bar] > 0.0) &&
                           (bulls_power[prev_bar] > bulls_power[current_bar]);

    // 5. Magic Filter (Super Arrow orijinal)
    double magic_filter = CalculateMagicFilter(current_bar);
    bool magic_bullish = magic_filter >= 0.0;
    bool magic_bearish = magic_filter < 0.0;

    // 6. Trend Filtresi (M5 için kritik - ters sinyal önleme)
    // Kısa vadeli trend yönü kontrolü - array sınır kontrolü ile
    bool uptrend = false;
    bool downtrend = false;

    if(ArraySize(slowMA) > current_bar + 3)
    {
        uptrend = (fastMA[current_bar] > slowMA[current_bar]) &&
                  (slowMA[current_bar] > slowMA[current_bar + 3]);
        downtrend = (fastMA[current_bar] < slowMA[current_bar]) &&
                    (slowMA[current_bar] < slowMA[current_bar + 3]);
    }
    else
    {
        // Yeterli veri yoksa sadece mevcut MA karşılaştırması
        uptrend = (fastMA[current_bar] > slowMA[current_bar]);
        downtrend = (fastMA[current_bar] < slowMA[current_bar]);
    }

    // === SUPER ARROW ESNEK SİNYAL KOŞULLARI ===

    // Koşul sayısını hesapla (DOĞRU mantık)
    int buy_conditions = 0;
    int sell_conditions = 0;

    // BUY koşulları (Super Arrow orijinal + Trend Filtresi)
    if(ma_cross_up) buy_conditions++;
    if(rsi_cross_up) buy_conditions++;
    if(bb_lower_condition) buy_conditions++;      // Alt banda giriş
    if(bears_strengthening) buy_conditions++;     // Bears güçleniyor
    if(magic_bullish) buy_conditions++;
    if(uptrend) buy_conditions++;                 // Trend filtresi

    // SELL koşulları (Super Arrow orijinal + Trend Filtresi)
    if(ma_cross_down) sell_conditions++;
    if(rsi_cross_down) sell_conditions++;
    if(bb_upper_condition) sell_conditions++;     // Üst banda çıkış
    if(bulls_weakening) sell_conditions++;        // Bulls zayıflıyor
    if(magic_bearish) sell_conditions++;
    if(downtrend) sell_conditions++;              // Trend filtresi

    // BUY Sinyali (MinSignalConditions + Trend Filtresi zorunlu)
    bool buy_signal = (buy_conditions >= MinSignalConditions) && ma_cross_up && uptrend;

    // SELL Sinyali (MinSignalConditions + Trend Filtresi zorunlu)
    bool sell_signal = (sell_conditions >= MinSignalConditions) && ma_cross_down && downtrend;

    // Debug bilgileri
    if(buy_signal || sell_signal)
    {
        Print("🎯 SUPER ARROW SİNYAL ALGISI:");
        Print("   MA Cross: UP=", ma_cross_up, " DOWN=", ma_cross_down);
        Print("   RSI Cross: UP=", rsi_cross_up, " DOWN=", rsi_cross_down, " Value=", DoubleToString(rsi_values[current_bar], 2));
        Print("   BB: Lower Cond=", bb_lower_condition, " Upper Cond=", bb_upper_condition);
        Print("   Power: Bears Strong=", bears_strengthening, " Bulls Weak=", bulls_weakening);
        Print("   Magic Filter: ", DoubleToString(magic_filter, 4), " (Bull=", magic_bullish, " Bear=", magic_bearish, ")");
        Print("   Trend Filter: UP=", uptrend, " DOWN=", downtrend);
        Print("   BUY Conditions: ", buy_conditions, "/6 (Min:", MinSignalConditions, ") | SELL Conditions: ", sell_conditions, "/6 (Min:", MinSignalConditions, ")");
    }

    // Pattern analizi kontrolü
    string pattern_signal = "NONE";
    if(EnablePatternAnalysis)
    {
        pattern_signal = CheckAllPatterns(current_bar);
        if(pattern_signal != "NONE")
        {
            Print("🎯 Pattern Sinyali: ", pattern_signal);
        }
    }

    // Sinyal işle (Super Arrow + Pattern kombinasyonu)
    bool final_buy_signal = buy_signal && lastSignal != "BUY";
    bool final_sell_signal = sell_signal && lastSignal != "SELL";

    // Pattern sinyali varsa öncelik ver
    if(EnablePatternAnalysis && pattern_signal != "NONE")
    {
        if(pattern_signal == "BUY" && lastSignal != "BUY")
        {
            ProcessSignal("BUY", current_close);
            Print("📊 Pattern BUY sinyali işlendi");
        }
        else if(pattern_signal == "SELL" && lastSignal != "SELL")
        {
            ProcessSignal("SELL", current_close);
            Print("📊 Pattern SELL sinyali işlendi");
        }
    }
    // Pattern yoksa normal Super Arrow sinyali
    else if(final_buy_signal)
    {
        ProcessSignal("BUY", current_close);
        Print("⚡ Super Arrow BUY sinyali işlendi");
    }
    else if(final_sell_signal)
    {
        ProcessSignal("SELL", current_close);
        Print("⚡ Super Arrow SELL sinyali işlendi");
    }
}

//+------------------------------------------------------------------+
//| Magic Filter hesapla (Gömülü)                                   |
//+------------------------------------------------------------------+
double CalculateMagicFilter(int bar_index)
{
    // Basit momentum tabanlı magic filter
    double current_close = iClose(_Symbol, _Period, bar_index);
    double prev_close = iClose(_Symbol, _Period, bar_index + 1);
    double prev2_close = iClose(_Symbol, _Period, bar_index + 2);

    // Fiyat momentum
    double price_momentum = (current_close - prev_close) / _Point;

    // Volume momentum (varsa)
    long current_volume = iTickVolume(_Symbol, _Period, bar_index);
    long prev_volume = iTickVolume(_Symbol, _Period, bar_index + 1);
    double volume_ratio = prev_volume > 0 ? (double)current_volume / prev_volume : 1.0;

    // Basit trend strength
    double trend_strength = 0;
    for(int i = 0; i < 5; i++)
    {
        double close1 = iClose(_Symbol, _Period, bar_index + i);
        double close2 = iClose(_Symbol, _Period, bar_index + i + 1);
        if(close1 > close2) trend_strength += 1.0;
        else trend_strength -= 1.0;
    }
    trend_strength /= 5.0; // Normalize (-1 ile 1 arası)

    // Magic filter kombinasyonu
    double magic = (price_momentum * 0.4) + (trend_strength * 0.4) + ((volume_ratio - 1.0) * 0.2);

    return magic;
}



//+------------------------------------------------------------------+
//| Sinyali işle                                                     |
//+------------------------------------------------------------------+
void ProcessSignal(string signalType, double signalPrice)
{
    lastSignal = signalType;
    lastSignalTime = TimeCurrent();

    Print("🔔 ", signalType, " Sinyali - Fiyat: ", DoubleToString(signalPrice, _Digits),
          " | Mod: ", SignalMode == SIGNAL_TICK_BASED ? "Tick" : "Bar");

    // Telegram bildirimi
    if(EnableTelegram && SendSignalAlerts)
    {
        string message = "🎯 <b>SUPER ARROW SİNYALİ</b>\n\n";
        message += "📊 <b>Sembol:</b> " + _Symbol + "\n";
        message += "⏰ <b>Zaman:</b> " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES) + "\n";
        message += "🎯 <b>Yön:</b> " + (signalType == "BUY" ? "🟢 ALIŞ" : "🔴 SATIŞ") + "\n";
        message += "💰 <b>Fiyat:</b> " + DoubleToString(signalPrice, _Digits) + "\n";
        message += "🟢 <b>Destek:</b> " + DoubleToString(currentSupportLevel, _Digits) + "\n";
        message += "🔴 <b>Direnç:</b> " + DoubleToString(currentResistanceLevel, _Digits) + "\n";
        message += "📊 <b>Mod:</b> " + (SignalMode == SIGNAL_TICK_BASED ? "Tick Bazlı" : "Bar Bazlı");

        SendTelegramMessage(message);
    }

    // Otomatik işlem
    if(EnableAutoTrade && IsSignalSafe(signalType))
    {
        ExecuteAutoTrade(signalType, signalPrice);
    }
}

//+------------------------------------------------------------------+
//| Sinyalin güvenli olup olmadığını kontrol et                     |
//+------------------------------------------------------------------+
bool IsSignalSafe(string signalType)
{
    // Günlük trade limiti
    if(dailyTradeCount >= MaxDailyTrades)
    {
        Print("❌ Günlük trade limiti aşıldı: ", dailyTradeCount, "/", MaxDailyTrades);
        return false;
    }

    // Spread kontrolü
    double spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    if(_Digits == 5 || _Digits == 3) spread /= 10;

    if(spread > MaxSpread)
    {
        Print("❌ Spread çok yüksek: ", DoubleToString(spread, 1), " > ", MaxSpread);
        return false;
    }

    // S/R seviyeleri var mı
    if(currentSupportLevel <= 0 || currentResistanceLevel <= 0)
    {
        Print("❌ S/R seviyeleri hesaplanmamış");
        return false;
    }

    // Safe zone kontrolü
    double current_price = SymbolInfoDouble(_Symbol, signalType == "BUY" ? SYMBOL_ASK : SYMBOL_BID);
    double sfr = (currentResistanceLevel - currentSupportLevel) * SafeZoneRatio;
    double ds = currentSupportLevel + sfr;   // DownSafe
    double rs = currentResistanceLevel - sfr; // UpSafe

    if(signalType == "BUY")
    {
        // BUY: Fiyat support ile downsafe arasında olmalı
        if(current_price < currentSupportLevel || current_price > ds)
        {
            Print("❌ BUY sinyali safe zone dışında: ", DoubleToString(current_price, _Digits));
            return false;
        }
    }
    else // SELL
    {
        // SELL: Fiyat upsafe ile resistance arasında olmalı
        if(current_price < rs || current_price > currentResistanceLevel)
        {
            Print("❌ SELL sinyali safe zone dışında: ", DoubleToString(current_price, _Digits));
            return false;
        }
    }

    Print("✅ Sinyal güvenli - Safe zone içinde");
    return true;
}

//+------------------------------------------------------------------+
//| Otomatik işlem gerçekleştir                                     |
//+------------------------------------------------------------------+
void ExecuteAutoTrade(string signalType, double signalPrice)
{
    // Sabit lot size kullan
    double lotSize = FixedLotSize;

    // Lot size kontrolü
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    if(lotSize < minLot || lotSize > maxLot)
    {
        Print("❌ Geçersiz lot size: ", lotSize, " (Min:", minLot, " Max:", maxLot, ")");
        return;
    }

    // SL/TP hesapla
    double stopLoss = CalculateStopLoss(signalType);
    double takeProfit = CalculateTakeProfit(signalType, lotSize);

    // İşlem gönder
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lotSize;
    request.type = (signalType == "BUY") ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    request.price = (signalType == "BUY") ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    request.sl = 0;  // Programatik yönetim
    request.tp = 0;  // Programatik yönetim
    request.magic = MagicNumber;
    request.comment = "SuperSentinel_" + signalType + "|SL:" + DoubleToString(stopLoss, _Digits) + "|TP:" + DoubleToString(takeProfit, _Digits);

    if(OrderSend(request, result))
    {
        if(result.retcode == TRADE_RETCODE_DONE)
        {
            dailyTradeCount++;

            Print("✅ İşlem açıldı: ", signalType, " | Lot: ", lotSize, " | Ticket: ", result.order);

            // Telegram bildirimi
            if(EnableTelegram && SendTradeAlerts)
            {
                string message = "💰 <b>İŞLEM AÇILDI</b>\n\n";
                message += "🎯 <b>Yön:</b> " + (signalType == "BUY" ? "🟢 ALIŞ" : "🔴 SATIŞ") + "\n";
                message += "💰 <b>Fiyat:</b> " + DoubleToString(request.price, _Digits) + "\n";
                message += "📦 <b>Lot:</b> " + DoubleToString(lotSize, 2) + "\n";
                message += "🛑 <b>SL:</b> " + DoubleToString(stopLoss, _Digits) + "\n";
                message += "🎯 <b>TP:</b> " + DoubleToString(takeProfit, _Digits) + "\n";
                message += "🎫 <b>Ticket:</b> " + IntegerToString(result.order);

                SendTelegramMessage(message);
            }
        }
        else
        {
            Print("❌ İşlem hatası: ", result.retcode, " - ", result.comment);
        }
    }
    else
    {
        Print("❌ OrderSend başarısız: ", GetLastError());
    }
}



//+------------------------------------------------------------------+
//| Stop Loss hesapla                                               |
//+------------------------------------------------------------------+
double CalculateStopLoss(string signalType)
{
    double currentPrice = SymbolInfoDouble(_Symbol, signalType == "BUY" ? SYMBOL_ASK : SYMBOL_BID);

    // Dolar bazlı SL hesaplama (sabit sistem)
    double slDistance = CalculateSLDistanceFromDollar(FixedSLDollar);

    if(signalType == "BUY")
        return currentPrice - slDistance;
    else
        return currentPrice + slDistance;
}

//+------------------------------------------------------------------+
//| Take Profit hesapla                                             |
//+------------------------------------------------------------------+
double CalculateTakeProfit(string signalType, double lotSize)
{
    // Dolar bazlı TP
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    double dollarPerPip = (tickValue / tickSize) * lotSize;
    double tpDistance = FixedTPDollar / dollarPerPip;

    double currentPrice = SymbolInfoDouble(_Symbol, signalType == "BUY" ? SYMBOL_ASK : SYMBOL_BID);

    if(signalType == "BUY")
        return currentPrice + (tpDistance * _Point);
    else
        return currentPrice - (tpDistance * _Point);
}

//+------------------------------------------------------------------+
//| Basit S/R hesaplama (Sentinel'den uyarlandı)                    |
//+------------------------------------------------------------------+
void CalculateSupportResistance()
{
    double currentPrice = iClose(_Symbol, _Period, 0);

    // Basit yöntem: Son X bar içindeki en yüksek/düşük
    double highestHigh = 0;
    double lowestLow = 999999;

    for(int i = 1; i <= SR_LookbackPeriod; i++)
    {
        double high = iHigh(_Symbol, _Period, i);
        double low = iLow(_Symbol, _Period, i);

        if(high > highestHigh) highestHigh = high;
        if(low < lowestLow) lowestLow = low;
    }

    // Mevcut fiyata göre S/R belirle
    if(currentPrice > (highestHigh + lowestLow) / 2)
    {
        currentSupportLevel = (highestHigh + lowestLow) / 2;
        currentResistanceLevel = highestHigh;
    }
    else
    {
        currentSupportLevel = lowestLow;
        currentResistanceLevel = (highestHigh + lowestLow) / 2;
    }

    lastSR_UpdateTime = TimeCurrent();

    // Telegram S/R bildirimi
    if(EnableTelegram && SendSRAlerts && TimeCurrent() - lastSR_UpdateTime > 3600) // Saatte bir
    {
        string message = "📊 <b>S/R GÜNCELLEMESİ</b>\n\n";
        message += "🟢 <b>Destek:</b> " + DoubleToString(currentSupportLevel, _Digits) + "\n";
        message += "🔴 <b>Direnç:</b> " + DoubleToString(currentResistanceLevel, _Digits) + "\n";
        message += "📏 <b>Mesafe:</b> " + DoubleToString(currentResistanceLevel - currentSupportLevel, _Digits) + " pip";

        SendTelegramMessage(message);
    }
}

//+------------------------------------------------------------------+
//| Aktif pozisyonları yönet                                        |
//+------------------------------------------------------------------+
void ManageActivePositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double currentPrice = (posType == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

            // SL/TP seviyelerini comment'ten oku
            string comment = PositionGetString(POSITION_COMMENT);
            double stopLoss = 0, takeProfit = 0;

            int slPos = StringFind(comment, "|SL:");
            int tpPos = StringFind(comment, "|TP:");

            if(slPos >= 0 && tpPos >= 0)
            {
                string slStr = StringSubstr(comment, slPos + 4, tpPos - slPos - 4);
                stopLoss = StringToDouble(slStr);

                string tpStr = StringSubstr(comment, tpPos + 4);
                int spacePos = StringFind(tpStr, " ");
                if(spacePos >= 0) tpStr = StringSubstr(tpStr, 0, spacePos);
                takeProfit = StringToDouble(tpStr);

                // Programatik SL/TP kontrolü
                CheckAndCloseProgrammaticSLTP(ticket, posType, currentPrice, stopLoss, takeProfit);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Programatik SL/TP kontrolü                                      |
//+------------------------------------------------------------------+
void CheckAndCloseProgrammaticSLTP(ulong ticket, ENUM_POSITION_TYPE posType, double currentPrice, double stopLoss, double takeProfit)
{
    bool shouldClose = false;
    string reason = "";

    if(posType == POSITION_TYPE_BUY)
    {
        if(currentPrice <= stopLoss)
        {
            shouldClose = true;
            reason = "Stop Loss";
        }
        else if(currentPrice >= takeProfit)
        {
            shouldClose = true;
            reason = "Take Profit";
        }
    }
    else // SELL
    {
        if(currentPrice >= stopLoss)
        {
            shouldClose = true;
            reason = "Stop Loss";
        }
        else if(currentPrice <= takeProfit)
        {
            shouldClose = true;
            reason = "Take Profit";
        }
    }

    if(shouldClose)
    {
        if(trade.PositionClose(ticket))
        {
            double profit = PositionGetDouble(POSITION_PROFIT);
            Print("✅ Pozisyon kapatıldı (", reason, ") - Ticket: ", ticket, " | P&L: $", DoubleToString(profit, 2));

            // Telegram bildirimi
            if(EnableTelegram && SendTradeAlerts)
            {
                string message = (reason == "Take Profit" ? "🎯" : "🛑") + " <b>POZİSYON KAPATILDI</b>\n\n";
                message += "📋 <b>Ticket:</b> " + IntegerToString(ticket) + "\n";
                message += "💰 <b>P&L:</b> $" + DoubleToString(profit, 2) + "\n";
                message += "🎯 <b>Sebep:</b> " + reason;

                SendTelegramMessage(message);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Telegram mesajı gönder                                          |
//+------------------------------------------------------------------+
bool SendTelegramMessage(string message)
{
    if(!EnableTelegram || StringLen(TelegramBotToken) == 0 || StringLen(TelegramChatID) == 0)
        return false;

    string url = "https://api.telegram.org/bot" + TelegramBotToken + "/sendMessage";
    string postData = "chat_id=" + TelegramChatID + "&text=" + UrlEncode(message) + "&parse_mode=HTML";

    char data[];
    char result[];
    string headers = "Content-Type: application/x-www-form-urlencoded\r\n";

    StringToCharArray(postData, data, 0, StringLen(postData));

    int res = WebRequest("POST", url, headers, 5000, data, result, headers);

    if(res == 200)
    {
        return true;
    }
    else
    {
        Print("❌ Telegram mesaj hatası: ", res);
        return false;
    }
}

//+------------------------------------------------------------------+
//| URL encode                                                       |
//+------------------------------------------------------------------+
string UrlEncode(string str)
{
    string result = "";
    for(int i = 0; i < StringLen(str); i++)
    {
        ushort ch = StringGetCharacter(str, i);
        if((ch >= 'A' && ch <= 'Z') || (ch >= 'a' && ch <= 'z') || (ch >= '0' && ch <= '9') ||
           ch == '-' || ch == '_' || ch == '.' || ch == '~')
        {
            result += CharToString((char)ch);
        }
        else
        {
            result += StringFormat("%%%02X", ch);
        }
    }
    return result;
}

//+------------------------------------------------------------------+
//| Telegram komutlarını kontrol et                                 |
//+------------------------------------------------------------------+
void CheckTelegramCommands()
{
    if(!EnableTelegram) return;

    string url = "https://api.telegram.org/bot" + TelegramBotToken + "/getUpdates";
    if(lastUpdateId > 0)
        url += "?offset=" + IntegerToString(lastUpdateId + 1);

    char data[];
    char result[];
    string headers = "";

    int res = WebRequest("GET", url, headers, 5000, data, result, headers);

    if(res == 200)
    {
        string response = CharArrayToString(result);
        ProcessTelegramUpdates(response);
    }
}

//+------------------------------------------------------------------+
//| Telegram güncellemelerini işle                                  |
//+------------------------------------------------------------------+
void ProcessTelegramUpdates(string response)
{
    // Basit komut işleme
    if(StringFind(response, "/status") >= 0)
    {
        string status = "🛡️ <b>SUPER SENTINEL DURUMU</b>\n\n";
        status += "📊 <b>Sembol:</b> " + _Symbol + "\n";
        status += "🤖 <b>AutoTrade:</b> " + (EnableAutoTrade ? "✅ Açık" : "❌ Kapalı") + "\n";
        status += "🟢 <b>Destek:</b> " + DoubleToString(currentSupportLevel, _Digits) + "\n";
        status += "🔴 <b>Direnç:</b> " + DoubleToString(currentResistanceLevel, _Digits) + "\n";
        status += "📊 <b>Pozisyon:</b> " + IntegerToString(PositionsTotal()) + "\n";
        status += "💰 <b>Bakiye:</b> $" + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2) + "\n";
        status += "📈 <b>Günlük İşlem:</b> " + IntegerToString(dailyTradeCount) + "/" + IntegerToString(MaxDailyTrades);

        SendTelegramMessage(status);
    }

    if(StringFind(response, "/balance") >= 0)
    {
        string balance = "💰 <b>HESAP BİLGİLERİ</b>\n\n";
        balance += "💰 <b>Bakiye:</b> $" + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2) + "\n";
        balance += "📈 <b>Equity:</b> $" + DoubleToString(AccountInfoDouble(ACCOUNT_EQUITY), 2) + "\n";
        balance += "📊 <b>Margin:</b> $" + DoubleToString(AccountInfoDouble(ACCOUNT_MARGIN), 2) + "\n";
        balance += "🆓 <b>Free Margin:</b> $" + DoubleToString(AccountInfoDouble(ACCOUNT_MARGIN_FREE), 2);

        SendTelegramMessage(balance);
    }
}

//+------------------------------------------------------------------+
//| Eski geçmiş analiz objelerini temizle                           |
//+------------------------------------------------------------------+
void CleanupOldHistoryObjects()
{
    Print("🧹 Eski Super Arrow sinyalleri temizleniyor...");

    int deletedCount = 0;

    // Tüm objeleri kontrol et
    int totalObjects = ObjectsTotal(0);

    for(int i = totalObjects - 1; i >= 0; i--)
    {
        string objName = ObjectName(0, i);

        // Sadece sinyal oklarını temizle
        if(StringFind(objName, "HistorySignal_") >= 0)
        {
            if(ObjectDelete(0, objName))
            {
                deletedCount++;
            }
        }
    }

    // Chart'ı yenile
    ChartRedraw(0);

    Print("🧹 ", deletedCount, " eski sinyal oku temizlendi");
}

//+------------------------------------------------------------------+
//| Geçmiş analizi gerçekleştir                                     |
//+------------------------------------------------------------------+
void PerformHistoryAnalysis()
{
    Print("📊 Geçmiş analizi başlıyor - ", HistoryAnalysisBars, " bar analiz edilecek...");

    // Eski çizgileri temizle
    CleanupOldHistoryObjects();

    // Array'i yeniden boyutlandır
    ArrayResize(historySignals, 0);
    totalHistorySignals = 0;
    profitableHistorySignals = 0;

    // Yeterli veri var mı kontrol et
    int totalBars = Bars(_Symbol, _Period);
    if(totalBars < HistoryAnalysisBars + SlowerMA + BollingerPeriod + 50)
    {
        Print("❌ Yeterli geçmiş veri yok. Mevcut: ", totalBars, " Gerekli: ", HistoryAnalysisBars + SlowerMA + BollingerPeriod + 50);
        return;
    }

    // Handle'lar hazır mı kontrol et (güvenlik kontrolü)
    if(!AreIndicatorsReady())
    {
        Print("❌ Indicator handle'ları henüz hazır değil");
        return;
    }

    // Büyük buffer'ları hazırla
    double fastMA[], slowMA[], rsi_values[];
    double bb_upper[], bb_lower[], bb_middle[];
    double bulls_power[], bears_power[];

    // Veri kopyala
    int copyBars = HistoryAnalysisBars + 10;
    if(CopyBuffer(fastMA_handle, 0, 0, copyBars, fastMA) <= 0) return;
    if(CopyBuffer(slowMA_handle, 0, 0, copyBars, slowMA) <= 0) return;
    if(CopyBuffer(rsi_handle, 0, 0, copyBars, rsi_values) <= 0) return;
    if(CopyBuffer(bb_handle, 1, 0, copyBars, bb_upper) <= 0) return;
    if(CopyBuffer(bb_handle, 2, 0, copyBars, bb_lower) <= 0) return;
    if(CopyBuffer(bb_handle, 0, 0, copyBars, bb_middle) <= 0) return;
    if(CopyBuffer(bulls_handle, 0, 0, copyBars, bulls_power) <= 0) return;
    if(CopyBuffer(bears_handle, 0, 0, copyBars, bears_power) <= 0) return;

    Print("📈 Veri kopyalama tamamlandı, analiz başlıyor...");

    // Geçmişten bugüne analiz (en eskiden en yeniye)
    for(int i = HistoryAnalysisBars - 1; i >= 10; i--)
    {
        // Progress göstergesi
        if(i % 100 == 0)
        {
            Print("📊 Analiz ilerliyor: ", HistoryAnalysisBars - i, "/", HistoryAnalysisBars, " (",
                  MathRound(((double)(HistoryAnalysisBars - i) / HistoryAnalysisBars) * 100), "%)");
        }

        // Bu bar için sinyal analizi
        AnalyzeHistoryBar(i, fastMA, slowMA, rsi_values, bb_upper, bb_lower, bb_middle, bulls_power, bears_power);
    }

    // Analiz sonuçlarını raporla
    GenerateHistoryReport();



    // Grafik üzerinde göster
    if(ShowHistoryOnChart)
    {
        DrawHistorySignalsOnChart();
    }

    historyAnalysisCompleted = true;
    Print("✅ Geçmiş analizi tamamlandı!");
}

//+------------------------------------------------------------------+
//| Tek bar için geçmiş analizi                                     |
//+------------------------------------------------------------------+
void AnalyzeHistoryBar(int bar_index, double &fastMA[], double &slowMA[], double &rsi_values[],
                      double &bb_upper[], double &bb_lower[], double &bb_middle[],
                      double &bulls_power[], double &bears_power[])
{
    // Fiyat verileri
    double current_close = iClose(_Symbol, _Period, bar_index);
    double prev_close = iClose(_Symbol, _Period, bar_index + 1);
    double current_high = iHigh(_Symbol, _Period, bar_index);
    double current_low = iLow(_Symbol, _Period, bar_index);
    datetime bar_time = iTime(_Symbol, _Period, bar_index);

    // === SUPER ARROW SİNYAL ALGORİTMASI (GEÇMİŞ İÇİN) ===

    // 1. MA Cross Kontrolü
    bool ma_cross_up = (fastMA[bar_index] > slowMA[bar_index]) &&
                       (fastMA[bar_index + 1] <= slowMA[bar_index + 1]);
    bool ma_cross_down = (fastMA[bar_index] < slowMA[bar_index]) &&
                         (fastMA[bar_index + 1] >= slowMA[bar_index + 1]);

    // 2. RSI Cross Kontrolü
    bool rsi_cross_up = (rsi_values[bar_index] > 50.0) &&
                        (rsi_values[bar_index + 1] <= 50.0);
    bool rsi_cross_down = (rsi_values[bar_index] < 50.0) &&
                          (rsi_values[bar_index + 1] >= 50.0);

    // 3. Bollinger Bands Kontrolü (Super Arrow DOĞRU mantık)
    // BUY: Alt banda giriş (oversold)
    bool bb_lower_condition = (current_close < bb_lower[bar_index]);
    // SELL: Üst banda çıkış (overbought)
    bool bb_upper_condition = (current_close > bb_upper[bar_index]);

    // 4. Bulls/Bears Power Kontrolü (Super Arrow DOĞRU mantık)
    // BUY: Bears power < 0 ve güçleniyor (azalıyor)
    bool bears_strengthening = (bears_power[bar_index] < 0.0) &&
                               (bears_power[bar_index + 1] < bears_power[bar_index]);
    // SELL: Bulls power > 0 ve zayıflıyor (azalıyor)
    bool bulls_weakening = (bulls_power[bar_index] > 0.0) &&
                           (bulls_power[bar_index + 1] > bulls_power[bar_index]);

    // 5. Magic Filter (trend yönü)
    double magic_filter = CalculateHistoryMagicFilter(bar_index);
    bool magic_bullish = magic_filter >= 0.0;
    bool magic_bearish = magic_filter < 0.0;

    // 6. Trend Filtresi (geçmiş analiz için - array sınır kontrolü ile)
    bool uptrend = false;
    bool downtrend = false;

    if(ArraySize(slowMA) > bar_index + 3)
    {
        uptrend = (fastMA[bar_index] > slowMA[bar_index]) &&
                  (slowMA[bar_index] > slowMA[bar_index + 3]);
        downtrend = (fastMA[bar_index] < slowMA[bar_index]) &&
                    (slowMA[bar_index] < slowMA[bar_index + 3]);
    }
    else
    {
        // Yeterli veri yoksa sadece mevcut MA karşılaştırması
        uptrend = (fastMA[bar_index] > slowMA[bar_index]);
        downtrend = (fastMA[bar_index] < slowMA[bar_index]);
    }

    // === SUPER ARROW ESNEK SİNYAL KOŞULLARI ===

    // Koşul sayısını hesapla (DOĞRU mantık)
    int buy_conditions = 0;
    int sell_conditions = 0;

    // BUY koşulları (Super Arrow orijinal + Trend Filtresi)
    if(ma_cross_up) buy_conditions++;
    if(rsi_cross_up) buy_conditions++;
    if(bb_lower_condition) buy_conditions++;      // Alt banda giriş
    if(bears_strengthening) buy_conditions++;     // Bears güçleniyor
    if(magic_bullish) buy_conditions++;
    if(uptrend) buy_conditions++;                 // Trend filtresi

    // SELL koşulları (Super Arrow orijinal + Trend Filtresi)
    if(ma_cross_down) sell_conditions++;
    if(rsi_cross_down) sell_conditions++;
    if(bb_upper_condition) sell_conditions++;     // Üst banda çıkış
    if(bulls_weakening) sell_conditions++;        // Bulls zayıflıyor
    if(magic_bearish) sell_conditions++;
    if(downtrend) sell_conditions++;              // Trend filtresi

    // BUY Sinyali (MinSignalConditions + Trend Filtresi zorunlu)
    bool buy_signal = (buy_conditions >= MinSignalConditions) && ma_cross_up && uptrend;

    // SELL Sinyali (MinSignalConditions + Trend Filtresi zorunlu)
    bool sell_signal = (sell_conditions >= MinSignalConditions) && ma_cross_down && downtrend;

    // Sinyal varsa kaydet
    if(buy_signal || sell_signal)
    {
        // S/R seviyelerini hesapla (o anki durum için)
        double support_level, resistance_level;
        CalculateHistorySR(bar_index, support_level, resistance_level);

        // Sinyal kaydı oluştur
        HistorySignal signal;
        signal.time = bar_time;
        signal.type = buy_signal ? "BUY" : "SELL";
        signal.price = current_close;
        signal.support_level = support_level;
        signal.resistance_level = resistance_level;
        signal.conditions_met = buy_signal ? buy_conditions : sell_conditions;

        // Sadece sinyal bilgilerini kaydet

        // Array'e ekle
        int newSize = ArraySize(historySignals) + 1;
        ArrayResize(historySignals, newSize);
        historySignals[newSize - 1] = signal;

        totalHistorySignals++;

        // Sadece sinyal sayısını say (kar/zarar hesaplama yok)
        profitableHistorySignals++;

        // Detaylı log
        if(DetailedHistoryLog && totalHistorySignals <= 10) // İlk 10 sinyali detaylı göster
        {
            Print("📊 Super Arrow Sinyal #", totalHistorySignals, ": ", signal.type,
                  " @ ", TimeToString(signal.time, TIME_DATE|TIME_MINUTES),
                  " | Fiyat: ", DoubleToString(signal.price, _Digits),
                  " | Koşul: ", signal.conditions_met, "/5");
        }
    }
}

//+------------------------------------------------------------------+
//| Pattern Analizi Fonksiyonları (CSV Analizinden)                |
//+------------------------------------------------------------------+

// Yardımcı fonksiyonlar
double GetBarSize(int bar)
{
    return (iHigh(_Symbol, _Period, bar) - iLow(_Symbol, _Period, bar)) / _Point;
}

double GetBodySize(int bar)
{
    return MathAbs(iClose(_Symbol, _Period, bar) - iOpen(_Symbol, _Period, bar)) / _Point;
}

double GetClosePosition(int bar)
{
    double high = iHigh(_Symbol, _Period, bar);
    double low = iLow(_Symbol, _Period, bar);
    double close = iClose(_Symbol, _Period, bar);
    if(high == low) return 50.0; // Doji durumu
    return (close - low) / (high - low) * 100;
}

bool IsBullish(int bar)
{
    return iClose(_Symbol, _Period, bar) > iOpen(_Symbol, _Period, bar);
}

bool IsBearish(int bar)
{
    return iClose(_Symbol, _Period, bar) < iOpen(_Symbol, _Period, bar);
}

bool IsDoji(int bar)
{
    return GetBodySize(bar) < 2;
}

// Pattern kontrol fonksiyonları
bool Check_BUY_Reversal_After_2Bearish(int bar = 1)
{
    // Başarı oranı: 50.5% (2859 test)
    if(!IsBearish(bar) || !IsBearish(bar + 1)) return false;
    if(GetClosePosition(bar) >= 30) return false;
    if(GetBarSize(bar) < 8 || GetBarSize(bar) > 30) return false;
    return true;
}

bool Check_BUY_London_Momentum(int bar = 1)
{
    // Başarı oranı: 49.7% (1058 test)
    datetime current_time = iTime(_Symbol, _Period, bar);
    MqlDateTime dt;
    TimeToStruct(current_time, dt);
    bool is_london_session = (dt.hour >= 8 && dt.hour <= 11);

    if(!is_london_session) return false;
    if(!IsBullish(bar)) return false;
    if(GetBodySize(bar) <= GetBarSize(bar) * 0.6) return false;
    if(GetBarSize(bar) <= 12) return false;

    return true;
}

bool Check_BUY_Doji_After_StrongDown(int bar = 1)
{
    // Başarı oranı: 48.1% (1319 test)
    if(!IsDoji(bar)) return false;
    if(GetBarSize(bar) <= 10) return false;
    if(GetBarSize(bar + 1) <= 15) return false;
    if(!IsBearish(bar + 1)) return false;

    return true;
}

bool Check_SELL_NY_Momentum(int bar = 1)
{
    // Başarı oranı: 47.9% (1020 test)
    datetime current_time = iTime(_Symbol, _Period, bar);
    MqlDateTime dt;
    TimeToStruct(current_time, dt);
    bool is_ny_session = (dt.hour >= 14 && dt.hour <= 17);

    if(!is_ny_session) return false;
    if(!IsBearish(bar)) return false;
    if(GetBodySize(bar) <= GetBarSize(bar) * 0.6) return false;
    if(GetBarSize(bar) <= 12) return false;

    return true;
}

string CheckAllPatterns(int bar = 1)
{
    if(Check_BUY_Reversal_After_2Bearish(bar)) return "BUY";
    if(Check_BUY_London_Momentum(bar)) return "BUY";
    if(Check_BUY_Doji_After_StrongDown(bar)) return "BUY";
    if(Check_SELL_NY_Momentum(bar)) return "SELL";
    return "NONE";
}

//+------------------------------------------------------------------+
//| Geçmiş için Magic Filter hesapla                                |
//+------------------------------------------------------------------+
double CalculateHistoryMagicFilter(int bar_index)
{
    double current_close = iClose(_Symbol, _Period, bar_index);
    double prev_close = iClose(_Symbol, _Period, bar_index + 1);

    // Basit momentum
    double price_momentum = (current_close - prev_close) / _Point;

    // Trend strength (son 5 bar)
    double trend_strength = 0;
    for(int i = 0; i < 5; i++)
    {
        double close1 = iClose(_Symbol, _Period, bar_index + i);
        double close2 = iClose(_Symbol, _Period, bar_index + i + 1);
        if(close1 > close2) trend_strength += 1.0;
        else trend_strength -= 1.0;
    }
    trend_strength /= 5.0;

    return (price_momentum * 0.6) + (trend_strength * 0.4);
}

//+------------------------------------------------------------------+
//| Geçmiş için S/R hesapla                                         |
//+------------------------------------------------------------------+
void CalculateHistorySR(int bar_index, double &support, double &resistance)
{
    double currentPrice = iClose(_Symbol, _Period, bar_index);

    // Basit S/R hesaplama (o anki bar'dan geriye bakarak)
    double highestHigh = 0;
    double lowestLow = 999999;

    for(int i = 1; i <= SR_LookbackPeriod && (bar_index + i) < Bars(_Symbol, _Period); i++)
    {
        double high = iHigh(_Symbol, _Period, bar_index + i);
        double low = iLow(_Symbol, _Period, bar_index + i);

        if(high > highestHigh) highestHigh = high;
        if(low < lowestLow) lowestLow = low;
    }

    // S/R belirle
    if(currentPrice > (highestHigh + lowestLow) / 2)
    {
        support = (highestHigh + lowestLow) / 2;
        resistance = highestHigh;
    }
    else
    {
        support = lowestLow;
        resistance = (highestHigh + lowestLow) / 2;
    }
}

//+------------------------------------------------------------------+
//| Geçmiş için TP mesafesi hesapla                                 |
//+------------------------------------------------------------------+
double CalculateHistoryTPDistance(double entryPrice, double slPrice)
{
    // Dolar bazlı sistem: Direkt dolar değerini point'e çevir
    // XAUUSD: $1 = 100 point (0.01 lot için)
    double tpPoints = FixedTPDollar * 100.0;
    double tpDistance = tpPoints * _Point;

    Print("🔧 TP Hesaplama: $", FixedTPDollar, " = ", tpPoints, " point = ", tpDistance, " distance");

    return tpDistance;
}

//+------------------------------------------------------------------+
//| Dolar bazlı SL mesafesi hesapla                                 |
//+------------------------------------------------------------------+
double CalculateSLDistanceFromDollar(double slDollar)
{
    // XAUUSD için basit hesaplama - 0.01 lot sabit
    // XAUUSD: 1 point = $0.01 (0.01 lot için)
    // $1 zarar için = 100 point gerekir

    double slPoints = slDollar * 100.0; // $1 = 100 point
    double slDistance = slPoints * _Point;

    Print("🔧 SL Hesaplama: $", slDollar, " = ", slPoints, " point = ", slDistance, " distance");

    return slDistance;
}

//+------------------------------------------------------------------+
//| Pip'i dolar cinsine çevir                                       |
//+------------------------------------------------------------------+
double CalculateProfitInDollar(double profitPips)
{
    // Basit hesaplama: Sabit lot size ile
    // XAUUSD: 1 point = $0.01 (0.01 lot için)
    // 100 point = $1
    return profitPips / 100.0;
}



//+------------------------------------------------------------------+
//| Geçmiş analiz raporu oluştur                                    |
//+------------------------------------------------------------------+
void GenerateHistoryReport()
{
    // Sadece sinyal sayısı raporu (kar/zarar hesaplama yok)
    Print("📊 ========== SUPER ARROW SİNYAL RAPORU ==========");
    Print("⚙️ Parametreler: MinSignal:", MinSignalConditions, " (Super Arrow Esnek) Lot:", FixedLotSize, " SL:$", FixedSLDollar, " TP:$", FixedTPDollar);
    Print("📈 Analiz Edilen Bar Sayısı: ", HistoryAnalysisBars);
    Print("🎯 Toplam Super Arrow Sinyali: ", totalHistorySignals);
    Print("📊 ==========================================");

    // Sinyal türü analizi
    int buySignals = 0, sellSignals = 0;

    for(int i = 0; i < ArraySize(historySignals); i++)
    {
        if(historySignals[i].type == "BUY")
            buySignals++;
        else
            sellSignals++;
    }

    Print("🟢 BUY Sinyalleri: ", buySignals);
    Print("🔴 SELL Sinyalleri: ", sellSignals);
    Print("📊 ==========================================");

    // Telegram raporu
    if(EnableTelegram)
    {
        string report = "📊 <b>SUPER ARROW SİNYAL RAPORU</b>\n\n";
        report += "📈 <b>Analiz Edilen Bar:</b> " + IntegerToString(HistoryAnalysisBars) + "\n";
        report += "🎯 <b>Toplam Sinyal:</b> " + IntegerToString(totalHistorySignals) + "\n";
        report += "🟢 <b>BUY Sinyalleri:</b> " + IntegerToString(buySignals) + "\n";
        report += "🔴 <b>SELL Sinyalleri:</b> " + IntegerToString(sellSignals);

        SendTelegramMessage(report);
    }
}



//+------------------------------------------------------------------+
//| Geçmiş sinyalleri grafikte göster                               |
//+------------------------------------------------------------------+
void DrawHistorySignalsOnChart()
{
    Print("🎨 Super Arrow sinyalleri grafikte çiziliyor...");

    for(int i = 0; i < ArraySize(historySignals); i++)
    {
        // Sadece sinyal oku çiz
        string objName = "HistorySignal_" + IntegerToString(i);
        color arrowColor = historySignals[i].type == "BUY" ? clrLime : clrRed;

        if(ObjectCreate(0, objName, OBJ_ARROW, 0, historySignals[i].time, historySignals[i].price))
        {
            ObjectSetInteger(0, objName, OBJPROP_ARROWCODE, historySignals[i].type == "BUY" ? 233 : 234);
            ObjectSetInteger(0, objName, OBJPROP_COLOR, arrowColor);
            ObjectSetInteger(0, objName, OBJPROP_WIDTH, 3);
            ObjectSetInteger(0, objName, OBJPROP_BACK, false);

            // Basit tooltip
            string tooltip = "🎯 Super Arrow " + historySignals[i].type + " Sinyali\n";
            tooltip += "📅 Zaman: " + TimeToString(historySignals[i].time, TIME_DATE|TIME_MINUTES) + "\n";
            tooltip += "💰 Fiyat: " + DoubleToString(historySignals[i].price, _Digits) + "\n";
            tooltip += "🎯 Koşul: " + IntegerToString(historySignals[i].conditions_met) + "/5";

            ObjectSetString(0, objName, OBJPROP_TOOLTIP, tooltip);
        }
    }

    Print("✅ ", ArraySize(historySignals), " Super Arrow sinyali grafikte gösterildi");
    Print("🎨 Yeşil Ok: BUY Sinyali | Kırmızı Ok: SELL Sinyali");
}
