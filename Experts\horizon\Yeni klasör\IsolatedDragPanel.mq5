//+------------------------------------------------------------------+
//| IsolatedDragPanel.mq5 - Grafik Etkilenmeyen Sürüklenebilir Panel|
//| Panel sürüklerken grafik tamamen izole edilir                   |
//+------------------------------------------------------------------+
#property copyright "Horizon Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Grafik Etkilenmeyen Sürüklenebilir Panel"

#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| Panel ayarları                                                  |
//+------------------------------------------------------------------+
input int StartX = 50;              // Başlangıç X pozisyonu
input int StartY = 50;              // Başlangıç Y pozisyonu

//+------------------------------------------------------------------+
//| Panel boyutları                                                 |
//+------------------------------------------------------------------+
#define PANEL_WIDTH     350
#define PANEL_HEIGHT    250
#define TITLE_HEIGHT    25
#define BUTTON_HEIGHT   22
#define MARGIN          8

//+------------------------------------------------------------------+
//| Global değişkenler                                              |
//+------------------------------------------------------------------+
string g_panelName = "IsolatedDragPanel";
datetime g_lastUpdate = 0;

// Sürükleme durumu
bool g_isDragging = false;
int g_dragStartX = 0;
int g_dragStartY = 0;
int g_currentX = 0;
int g_currentY = 0;

// Chart ayarları - orijinal değerler
bool g_originalMouseScroll = true;
bool g_originalDragLevels = true;
bool g_originalKeyboardControl = true;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    g_currentX = StartX;
    g_currentY = StartY;
    
    // Orijinal chart ayarlarını kaydet
    g_originalMouseScroll = (bool)ChartGetInteger(0, CHART_MOUSE_SCROLL);
    g_originalDragLevels = (bool)ChartGetInteger(0, CHART_DRAG_TRADE_LEVELS);
    g_originalKeyboardControl = (bool)ChartGetInteger(0, CHART_KEYBOARD_CONTROL);
    
    // Mouse event'lerini aktif et
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, true);
    
    CreateIsolatedDraggablePanel();
    UpdatePanelData();
    
    Print("✅ Isolated Drag Panel başlatıldı - Grafik korumalı sürükleme!");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    ObjectsDeleteAll(0, g_panelName);
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, false);
    
    // Orijinal chart ayarlarını geri yükle
    ChartSetInteger(0, CHART_MOUSE_SCROLL, g_originalMouseScroll);
    ChartSetInteger(0, CHART_DRAG_TRADE_LEVELS, g_originalDragLevels);
    ChartSetInteger(0, CHART_KEYBOARD_CONTROL, g_originalKeyboardControl);
    
    Print("🔄 Isolated Drag Panel kapatıldı - Chart ayarları geri yüklendi");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(TimeCurrent() - g_lastUpdate >= 3)
    {
        UpdatePanelData();
        g_lastUpdate = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        HandleButtonClick(sparam);
    }
    else if(id == CHARTEVENT_MOUSE_MOVE)
    {
        HandleIsolatedMouseMove((int)lparam, (int)dparam, (int)sparam);
    }
}

//+------------------------------------------------------------------+
//| İzole edilmiş mouse hareket işleyici                            |
//+------------------------------------------------------------------+
void HandleIsolatedMouseMove(int x, int y, int flags)
{
    // Sol mouse tuşu basılı mı kontrol et
    bool leftButtonPressed = (flags & 1) != 0;
    
    // Panel üzerinde mi kontrol et
    bool mouseOverPanel = IsMouseOverPanel(x, y);
    
    if(leftButtonPressed)
    {
        if(!g_isDragging && IsMouseOverTitleBar(x, y))
        {
            // Başlık çubuğunda sürükleme başlat
            g_isDragging = true;
            g_dragStartX = x;
            g_dragStartY = y;
            
            // Chart'ı tamamen bloke et
            BlockChartInteraction();
            
            Print("🖱️ İzole sürükleme başladı - Chart bloke edildi");
        }
        
        if(g_isDragging)
        {
            // Panel pozisyonunu güncelle
            int deltaX = x - g_dragStartX;
            int deltaY = y - g_dragStartY;
            
            g_currentX += deltaX;
            g_currentY += deltaY;
            
            // Ekran sınırları kontrolü
            int chartWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
            int chartHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);
            
            if(g_currentX < 0) g_currentX = 0;
            if(g_currentY < 0) g_currentY = 0;
            if(g_currentX + PANEL_WIDTH > chartWidth) g_currentX = chartWidth - PANEL_WIDTH;
            if(g_currentY + PANEL_HEIGHT > chartHeight) g_currentY = chartHeight - PANEL_HEIGHT;
            
            // Panel pozisyonunu güncelle
            UpdateAllPositions();
            
            // Yeni başlangıç noktası
            g_dragStartX = x;
            g_dragStartY = y;
            
            // Chart event'lerini engelle
            return;
        }
        else if(mouseOverPanel)
        {
            // Panel üzerindeyken chart interaction'ı bloke et
            BlockChartInteraction();
        }
    }
    else
    {
        // Sol mouse tuşu bırakıldı
        if(g_isDragging)
        {
            g_isDragging = false;
            
            // Chart'ı tekrar aktif et
            RestoreChartInteraction();
            
            Print("🖱️ İzole sürükleme bitti - Chart tekrar aktif: (", g_currentX, ",", g_currentY, ")");
        }
        else if(!mouseOverPanel)
        {
            // Panel dışındayken chart'ı aktif et
            RestoreChartInteraction();
        }
    }
}

//+------------------------------------------------------------------+
//| Chart interaction'ı bloke et                                    |
//+------------------------------------------------------------------+
void BlockChartInteraction()
{
    ChartSetInteger(0, CHART_MOUSE_SCROLL, false);
    ChartSetInteger(0, CHART_DRAG_TRADE_LEVELS, false);
    ChartSetInteger(0, CHART_KEYBOARD_CONTROL, false);
}

//+------------------------------------------------------------------+
//| Chart interaction'ı geri yükle                                  |
//+------------------------------------------------------------------+
void RestoreChartInteraction()
{
    ChartSetInteger(0, CHART_MOUSE_SCROLL, g_originalMouseScroll);
    ChartSetInteger(0, CHART_DRAG_TRADE_LEVELS, g_originalDragLevels);
    ChartSetInteger(0, CHART_KEYBOARD_CONTROL, g_originalKeyboardControl);
}

//+------------------------------------------------------------------+
//| Mouse panel üzerinde mi kontrol et                              |
//+------------------------------------------------------------------+
bool IsMouseOverPanel(int mouseX, int mouseY)
{
    return (mouseX >= g_currentX && mouseX <= g_currentX + PANEL_WIDTH &&
            mouseY >= g_currentY && mouseY <= g_currentY + PANEL_HEIGHT);
}

//+------------------------------------------------------------------+
//| Mouse başlık çubuğu üzerinde mi kontrol et                      |
//+------------------------------------------------------------------+
bool IsMouseOverTitleBar(int mouseX, int mouseY)
{
    return (mouseX >= g_currentX && mouseX <= g_currentX + PANEL_WIDTH &&
            mouseY >= g_currentY && mouseY <= g_currentY + TITLE_HEIGHT);
}

//+------------------------------------------------------------------+
//| İzole edilmiş sürüklenebilir panel oluştur                      |
//+------------------------------------------------------------------+
void CreateIsolatedDraggablePanel()
{
    // Ana panel arka planı
    CreateRectangle("Background", g_currentX, g_currentY, PANEL_WIDTH, PANEL_HEIGHT, C'45,45,45', C'120,120,120', 1000);
    
    // Başlık çubuğu - sürükleme alanı
    CreateRectangle("TitleBar", g_currentX, g_currentY, PANEL_WIDTH, TITLE_HEIGHT, C'0,120,215', C'0,90,180', 1001);
    
    // Başlık metni
    CreateLabel("Title", "📊 İzole Sürükleme Paneli", g_currentX + PANEL_WIDTH/2, g_currentY + TITLE_HEIGHT/2 - 6, clrWhite, 9, ANCHOR_CENTER, 1002);
    
    // Kapat butonu (X)
    CreateLabel("CloseBtn", "✖", g_currentX + PANEL_WIDTH - 20, g_currentY + TITLE_HEIGHT/2 - 6, clrWhite, 10, ANCHOR_CENTER, 1003);
    
    // Durum göstergesi
    CreateLabel("Status", "🛡️ Grafik Korumalı", g_currentX + MARGIN, g_currentY + TITLE_HEIGHT + 5, clrLimeGreen, 8, ANCHOR_LEFT_UPPER, 1002);
    
    CreatePanelButtons();
}

//+------------------------------------------------------------------+
//| Panel butonlarını oluştur                                       |
//+------------------------------------------------------------------+
void CreatePanelButtons()
{
    int btn_y = g_currentY + 150;
    int btn_width = (PANEL_WIDTH - 4 * MARGIN) / 3;
    
    // Karlı Kapat butonu
    CreateButton("BtnProfit", "💚 Karlı", g_currentX + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, C'0,128,0');
    
    // Zarar Kapat butonu
    CreateButton("BtnLoss", "❤️ Zarar", g_currentX + MARGIN + btn_width + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, C'128,0,0');
    
    // Hepsini Kapat butonu
    CreateButton("BtnAll", "🚫 Hepsini Kapat", g_currentX + MARGIN, btn_y + BUTTON_HEIGHT + MARGIN, 2 * btn_width + MARGIN, BUTTON_HEIGHT, C'128,128,0');
}

//+------------------------------------------------------------------+
//| Dikdörtgen oluştur                                              |
//+------------------------------------------------------------------+
void CreateRectangle(string name, int x, int y, int width, int height, color bgColor, color borderColor, int zorder)
{
    string objName = g_panelName + "_" + name;
    
    ObjectCreate(0, objName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, objName, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, objName, OBJPROP_YSIZE, height);
    ObjectSetInteger(0, objName, OBJPROP_BGCOLOR, bgColor);
    ObjectSetInteger(0, objName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, borderColor);
    ObjectSetInteger(0, objName, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, objName, OBJPROP_BACK, false);
    ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName, OBJPROP_ZORDER, zorder);
}

//+------------------------------------------------------------------+
//| Buton oluştur                                                   |
//+------------------------------------------------------------------+
void CreateButton(string name, string text, int x, int y, int width, int height, color bgColor)
{
    string objName = g_panelName + "_" + name;
    
    // Buton arka planı
    CreateRectangle(name, x, y, width, height, bgColor, C'80,80,80', 1002);
    
    // Buton metni
    CreateLabel(name + "_Text", text, x + width/2, y + height/2 - 6, clrWhite, 8, ANCHOR_CENTER, 1003);
}

//+------------------------------------------------------------------+
//| Label oluştur                                                   |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int x, int y, color clr, int fontSize, ENUM_ANCHOR_POINT anchor, int zorder)
{
    string objName = g_panelName + "_" + name;
    
    ObjectCreate(0, objName, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetString(0, objName, OBJPROP_TEXT, text);
    ObjectSetString(0, objName, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, fontSize);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, objName, OBJPROP_ANCHOR, anchor);
    ObjectSetInteger(0, objName, OBJPROP_BACK, false);
    ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName, OBJPROP_ZORDER, zorder);
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları güncelle                                       |
//+------------------------------------------------------------------+
void UpdateAllPositions()
{
    // Ana panel
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_XDISTANCE, g_currentX);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_YDISTANCE, g_currentY);

    // Başlık çubuğu
    ObjectSetInteger(0, g_panelName + "_TitleBar", OBJPROP_XDISTANCE, g_currentX);
    ObjectSetInteger(0, g_panelName + "_TitleBar", OBJPROP_YDISTANCE, g_currentY);

    // Başlık metni
    ObjectSetInteger(0, g_panelName + "_Title", OBJPROP_XDISTANCE, g_currentX + PANEL_WIDTH/2);
    ObjectSetInteger(0, g_panelName + "_Title", OBJPROP_YDISTANCE, g_currentY + TITLE_HEIGHT/2 - 6);

    // Kapat butonu
    ObjectSetInteger(0, g_panelName + "_CloseBtn", OBJPROP_XDISTANCE, g_currentX + PANEL_WIDTH - 20);
    ObjectSetInteger(0, g_panelName + "_CloseBtn", OBJPROP_YDISTANCE, g_currentY + TITLE_HEIGHT/2 - 6);

    // Durum göstergesi
    ObjectSetInteger(0, g_panelName + "_Status", OBJPROP_XDISTANCE, g_currentX + MARGIN);
    ObjectSetInteger(0, g_panelName + "_Status", OBJPROP_YDISTANCE, g_currentY + TITLE_HEIGHT + 5);

    // Butonları güncelle
    UpdateButtonPositions();

    // İçeriği güncelle
    UpdatePanelData();
}

//+------------------------------------------------------------------+
//| Buton pozisyonlarını güncelle                                   |
//+------------------------------------------------------------------+
void UpdateButtonPositions()
{
    int btn_y = g_currentY + 150;
    int btn_width = (PANEL_WIDTH - 4 * MARGIN) / 3;

    // Karlı Kapat butonu
    ObjectSetInteger(0, g_panelName + "_BtnProfit", OBJPROP_XDISTANCE, g_currentX + MARGIN);
    ObjectSetInteger(0, g_panelName + "_BtnProfit", OBJPROP_YDISTANCE, btn_y);
    ObjectSetInteger(0, g_panelName + "_BtnProfit_Text", OBJPROP_XDISTANCE, g_currentX + MARGIN + btn_width/2);
    ObjectSetInteger(0, g_panelName + "_BtnProfit_Text", OBJPROP_YDISTANCE, btn_y + BUTTON_HEIGHT/2 - 6);

    // Zarar Kapat butonu
    ObjectSetInteger(0, g_panelName + "_BtnLoss", OBJPROP_XDISTANCE, g_currentX + MARGIN + btn_width + MARGIN);
    ObjectSetInteger(0, g_panelName + "_BtnLoss", OBJPROP_YDISTANCE, btn_y);
    ObjectSetInteger(0, g_panelName + "_BtnLoss_Text", OBJPROP_XDISTANCE, g_currentX + MARGIN + btn_width + MARGIN + btn_width/2);
    ObjectSetInteger(0, g_panelName + "_BtnLoss_Text", OBJPROP_YDISTANCE, btn_y + BUTTON_HEIGHT/2 - 6);

    // Hepsini Kapat butonu
    ObjectSetInteger(0, g_panelName + "_BtnAll", OBJPROP_XDISTANCE, g_currentX + MARGIN);
    ObjectSetInteger(0, g_panelName + "_BtnAll", OBJPROP_YDISTANCE, btn_y + BUTTON_HEIGHT + MARGIN);
    ObjectSetInteger(0, g_panelName + "_BtnAll_Text", OBJPROP_XDISTANCE, g_currentX + MARGIN + (2 * btn_width + MARGIN)/2);
    ObjectSetInteger(0, g_panelName + "_BtnAll_Text", OBJPROP_YDISTANCE, btn_y + BUTTON_HEIGHT + MARGIN + BUTTON_HEIGHT/2 - 6);
}

//+------------------------------------------------------------------+
//| Panel verilerini güncelle                                       |
//+------------------------------------------------------------------+
void UpdatePanelData()
{
    // Panel verilerini hesapla
    int positionCount = 0;
    double totalVolume = 0;
    double totalProfit = 0;
    int profitableCount = 0;
    int losingCount = 0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);

        positionCount++;
        totalVolume += volume;
        totalProfit += profit;

        if(profit > 0)
            profitableCount++;
        else if(profit < 0)
            losingCount++;
    }

    // Günlük P&L hesapla
    double dailyPL = CalculateDailyPL(totalProfit);

    // Panel içeriğini güncelle
    DrawPanelContent(positionCount, totalVolume, totalProfit, profitableCount, losingCount, dailyPL);
}

//+------------------------------------------------------------------+
//| Panel içeriğini çiz                                             |
//+------------------------------------------------------------------+
void DrawPanelContent(int posCount, double totalVol, double totalPL, int profitCount, int lossCount, double dailyPL)
{
    int y_pos = g_currentY + TITLE_HEIGHT + 25;

    // Günlük P&L
    string dailyText = "💰 Günlük P&L: $" + DoubleToString(dailyPL, 2);
    color dailyColor = (dailyPL >= 0) ? clrLimeGreen : clrTomato;
    CreateLabel("DailyPL", dailyText, g_currentX + MARGIN, y_pos, dailyColor, 9, ANCHOR_LEFT_UPPER, 1002);
    y_pos += 18;

    // Açık pozisyon sayısı
    string posText = "📈 Açık Pozisyon: " + IntegerToString(posCount);
    CreateLabel("PositionCount", posText, g_currentX + MARGIN, y_pos, clrWhite, 8, ANCHOR_LEFT_UPPER, 1002);
    y_pos += 16;

    // Toplam hacim
    string volText = "📊 Toplam Hacim: " + DoubleToString(totalVol, 2);
    CreateLabel("TotalVolume", volText, g_currentX + MARGIN, y_pos, clrWhite, 8, ANCHOR_LEFT_UPPER, 1002);
    y_pos += 16;

    // Toplam P&L
    string plText = "💵 Toplam P&L: $" + DoubleToString(totalPL, 2);
    color plColor = (totalPL >= 0) ? clrLimeGreen : clrTomato;
    CreateLabel("TotalPL", plText, g_currentX + MARGIN, y_pos, plColor, 9, ANCHOR_LEFT_UPPER, 1002);
    y_pos += 16;

    // Karlı/Zararlı sayısı
    string countText = "📊 Karlı: " + IntegerToString(profitCount) + " | Zararlı: " + IntegerToString(lossCount);
    CreateLabel("ProfitLossCount", countText, g_currentX + MARGIN, y_pos, clrWhite, 8, ANCHOR_LEFT_UPPER, 1002);
}

//+------------------------------------------------------------------+
//| Günlük P&L hesapla                                             |
//+------------------------------------------------------------------+
double CalculateDailyPL(double currentPL)
{
    double dailyPL = 0;
    datetime todayStart = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));

    HistorySelect(todayStart, TimeCurrent());

    for(int i = 0; i < HistoryDealsTotal(); i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket > 0)
        {
            ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);
            if(dealType == DEAL_TYPE_BUY || dealType == DEAL_TYPE_SELL)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                double commission = HistoryDealGetDouble(ticket, DEAL_COMMISSION);
                double swap = HistoryDealGetDouble(ticket, DEAL_SWAP);

                dailyPL += (profit + commission + swap);
            }
        }
    }

    dailyPL += currentPL;
    return dailyPL;
}

//+------------------------------------------------------------------+
//| Buton tıklama işleyici                                          |
//+------------------------------------------------------------------+
void HandleButtonClick(string objName)
{
    if(StringFind(objName, "BtnProfit") >= 0)
    {
        CloseProfitablePositions();
    }
    else if(StringFind(objName, "BtnLoss") >= 0)
    {
        CloseLosingPositions();
    }
    else if(StringFind(objName, "BtnAll") >= 0)
    {
        CloseAllPositions();
    }
    else if(StringFind(objName, "CloseBtn") >= 0)
    {
        // Panel kapat
        ObjectsDeleteAll(0, g_panelName);
        ExpertRemove();
    }
}

//+------------------------------------------------------------------+
//| İşlem kapatma fonksiyonları                                     |
//+------------------------------------------------------------------+
void CloseProfitablePositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "" || PositionGetDouble(POSITION_PROFIT) <= 0)
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
            closedCount++;
    }

    Print("💚 ", closedCount, " karlı pozisyon kapatıldı");
    UpdatePanelData();
}

void CloseLosingPositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "" || PositionGetDouble(POSITION_PROFIT) >= 0)
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
            closedCount++;
    }

    Print("❤️ ", closedCount, " zararlı pozisyon kapatıldı");
    UpdatePanelData();
}

void CloseAllPositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
            closedCount++;
    }

    Print("🚫 ", closedCount, " pozisyon kapatıldı");
    UpdatePanelData();
}
