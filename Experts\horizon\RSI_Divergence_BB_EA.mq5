//+------------------------------------------------------------------+
//| RSI Divergence + Bollinger Bands EA                             |
//| Tepe/Dip dönüş noktalarını yakalar                              |
//+------------------------------------------------------------------+
#property copyright "Horizon Trading"
#property version   "1.00"

#include <Trade\Trade.mqh>
CTrade trade;

//--- Input parametreleri
input group "=== RSI Ayarları ==="
input int                   RSI_Period = 14;                      // RSI Periyodu
input double                RSI_Overbought = 70.0;               // RSI Aşırı Alım Seviyesi
input double                RSI_Oversold = 30.0;                 // RSI Aşırı Satım Seviyesi

input group "=== Bollinger Bands Ayarları ==="
input int                   BB_Period = 20;                      // BB Periyodu
input double                BB_Deviation = 2.0;                  // BB Standart Sapma
input ENUM_APPLIED_PRICE    BB_Applied_Price = PRICE_CLOSE;      // BB Uygulanan Fiyat

input group "=== Çıkış Ayarları ==="
input double                BUY_Exit_RSI = 40.0;                 // BUY Çıkış RSI Seviyesi
input double                SELL_Exit_RSI = 60.0;                // SELL Çıkış RSI Seviyesi

input group "=== Risk Yönetimi ==="
input double                Lot_Size = 0.01;                     // Lot Büyüklüğü
input double                ATR_Multiplier = 2.0;                // ATR Çarpanı (SL için)
input int                   ATR_Period = 14;                     // ATR Periyodu
input double                Risk_Percent = 2.0;                  // Risk Yüzdesi (% bakiye)

input group "=== Genel Ayarlar ==="
input int                   Magic_Number = 9999;                 // Magic Number
input string                Comment_Prefix = "RSI_DIV_BB";       // Yorum Ön Eki
input bool                  Use_ATR_SL = true;                   // ATR bazlı SL kullan

input group "=== Debug Ayarları ==="
input bool                  Enable_Debug = true;                 // Debug Logları Aktif
input bool                  Show_Every_Tick = false;             // Her Tick Debug
input int                   Debug_Frequency = 60;                // Debug Frekansı (saniye)

//--- Global değişkenler
int rsi_handle, bb_handle, atr_handle;
double rsi_buffer[], bb_upper_buffer[], bb_middle_buffer[], bb_lower_buffer[], atr_buffer[];
int debug_log_number = 0;

// Divergence takip yapıları
struct DivergencePoint
{
    int bar_index;
    double price;
    double rsi_value;
    datetime time;
};

DivergencePoint last_high, last_low;
bool position_opened = false;
int current_position_type = 0; // 1=BUY, -1=SELL, 0=YOK

// Debug değişkenleri
datetime last_debug_time = 0;
int tick_count = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Indicator handle'larını oluştur
    rsi_handle = iRSI(_Symbol, _Period, RSI_Period, PRICE_CLOSE);
    bb_handle = iBands(_Symbol, _Period, BB_Period, 0, BB_Deviation, BB_Applied_Price);
    atr_handle = iATR(_Symbol, _Period, ATR_Period);
    
    if(rsi_handle == INVALID_HANDLE || bb_handle == INVALID_HANDLE || atr_handle == INVALID_HANDLE)
    {
        Print("❌ Indicator handle oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Array ayarları
    ArraySetAsSeries(rsi_buffer, true);
    ArraySetAsSeries(bb_upper_buffer, true);
    ArraySetAsSeries(bb_middle_buffer, true);
    ArraySetAsSeries(bb_lower_buffer, true);
    ArraySetAsSeries(atr_buffer, true);
    
    // Değişkenleri sıfırla
    debug_log_number = 0;
    position_opened = false;
    current_position_type = 0;
    
    // Divergence noktalarını sıfırla
    last_high.bar_index = -1;
    last_high.price = 0;
    last_high.rsi_value = 0;
    last_low.bar_index = -1;
    last_low.price = 0;
    last_low.rsi_value = 0;
    
    Print("✅ RSI Divergence + Bollinger Bands EA başlatıldı - Magic: ", Magic_Number);
    Print("📊 RSI(", RSI_Period, ") + BB(", BB_Period, ",", BB_Deviation, ") + Divergence sistemi aktif");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("🔄 RSI Divergence + Bollinger Bands EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    tick_count++;

    // Yeterli bar kontrolü
    int required_bars = MathMax(RSI_Period, BB_Period) + Divergence_Lookback + 10;
    int available_bars = Bars(_Symbol, _Period);

    if(available_bars < required_bars)
    {
        if(Enable_Debug && tick_count <= 5)
        {
            Print("⚠️ [DEBUG] Yetersiz bar sayısı - Gerekli: ", required_bars, " Mevcut: ", available_bars);
        }
        return;
    }
    
    // Indicator verilerini al
    int rsi_copied = CopyBuffer(rsi_handle, 0, 0, Divergence_Lookback + 5, rsi_buffer);
    int bb_upper_copied = CopyBuffer(bb_handle, 0, 0, 5, bb_upper_buffer);
    int bb_middle_copied = CopyBuffer(bb_handle, 1, 0, 5, bb_middle_buffer);
    int bb_lower_copied = CopyBuffer(bb_handle, 2, 0, 5, bb_lower_buffer);
    int atr_copied = CopyBuffer(atr_handle, 0, 0, 2, atr_buffer);

    if(rsi_copied < Divergence_Lookback + 5 || bb_upper_copied < 5 ||
       bb_middle_copied < 5 || bb_lower_copied < 5 || atr_copied < 2)
    {
        if(Enable_Debug && tick_count <= 10)
        {
            Print("⚠️ [DEBUG] Buffer kopyalama hatası:");
            Print("   RSI: ", rsi_copied, "/", Divergence_Lookback + 5);
            Print("   BB Upper: ", bb_upper_copied, "/5");
            Print("   BB Middle: ", bb_middle_copied, "/5");
            Print("   BB Lower: ", bb_lower_copied, "/5");
            Print("   ATR: ", atr_copied, "/2");
        }
        return;
    }
    
    // Fiyat verilerini al
    MqlRates rates[];
    int rates_copied = CopyRates(_Symbol, _Period, 0, Divergence_Lookback + 5, rates);
    if(rates_copied < Divergence_Lookback + 5)
    {
        if(Enable_Debug && tick_count <= 10)
        {
            Print("⚠️ [DEBUG] Rates kopyalama hatası: ", rates_copied, "/", Divergence_Lookback + 5);
        }
        return;
    }
    ArraySetAsSeries(rates, true);
    
    // Mevcut değerler
    double current_price = rates[0].close;
    double current_rsi = rsi_buffer[0];
    double bb_upper = bb_upper_buffer[0];
    double bb_lower = bb_lower_buffer[0];
    double atr_current = atr_buffer[0];
    
    // Mevcut pozisyon kontrolü
    bool has_buy_position = HasPosition(ORDER_TYPE_BUY);
    bool has_sell_position = HasPosition(ORDER_TYPE_SELL);
    
    // Divergence analizi - KAPALI
    bool bearish_divergence = false;
    bool bullish_divergence = false;
    bool bearish_exit_divergence = false;
    bool bullish_exit_divergence = false;

    // AnalyzeDivergence fonksiyonu çağrılmıyor - Divergence kapatıldı
    
    // Bollinger Bands koşulları
    bool bb_upper_touch = (current_price >= bb_upper * 0.999); // %99.9 yakınlık
    bool bb_lower_touch = (current_price <= bb_lower * 1.001); // %100.1 yakınlık

    // SELL sinyali (BB Upper Touch + RSI Overbought) - Divergence kapatıldı
    bool sell_signal = bb_upper_touch && current_rsi >= RSI_Overbought;

    // BUY sinyali (BB Lower Touch + RSI Oversold) - Divergence kapatıldı
    bool buy_signal = bb_lower_touch && current_rsi <= RSI_Oversold;

    // Periyodik debug (her X saniyede bir)
    if(Enable_Debug && (Show_Every_Tick || TimeCurrent() - last_debug_time >= Debug_Frequency))
    {
        last_debug_time = TimeCurrent();
        debug_log_number++;
        Print("📊 [DEBUG-", debug_log_number, "] Genel Durum:");
        Print("   Tick: ", tick_count, " Fiyat: ", DoubleToString(current_price, 5), " RSI: ", DoubleToString(current_rsi, 2));
        Print("   BB - Upper: ", DoubleToString(bb_upper, 5), " Lower: ", DoubleToString(bb_lower, 5));
        Print("   BB Touch - Upper: ", bb_upper_touch, " (", DoubleToString(current_price/bb_upper*100, 2), "%)");
        Print("   BB Touch - Lower: ", bb_lower_touch, " (", DoubleToString(current_price/bb_lower*100, 2), "%)");
        Print("   RSI Seviye - Overbought: ", (current_rsi >= RSI_Overbought), " (", current_rsi, ">=", RSI_Overbought, ")");
        Print("   RSI Seviye - Oversold: ", (current_rsi <= RSI_Oversold), " (", current_rsi, "<=", RSI_Oversold, ")");
        Print("   Divergence - KAPALI (Basit BB+RSI sistemi aktif)");
        Print("   Son Yüksek - Bar: ", last_high.bar_index, " Fiyat: ", DoubleToString(last_high.price, 5), " RSI: ", DoubleToString(last_high.rsi_value, 2));
        Print("   Son Düşük - Bar: ", last_low.bar_index, " Fiyat: ", DoubleToString(last_low.price, 5), " RSI: ", DoubleToString(last_low.rsi_value, 2));
        Print("   Sinyal - SELL: ", sell_signal, " BUY: ", buy_signal);
        Print("   Pozisyon - BUY: ", has_buy_position, " SELL: ", has_sell_position, " Type: ", current_position_type);
    }
    
    // Debug - sadece sinyal olduğunda
    if(sell_signal || buy_signal || bearish_exit_divergence || bullish_exit_divergence)
    {
        debug_log_number++;
        Print("🔍 [", debug_log_number, "] DIVERGENCE SİNYALİ:");
        Print("   Fiyat: ", DoubleToString(current_price, 5), " RSI: ", DoubleToString(current_rsi, 2));
        Print("   BB Upper: ", DoubleToString(bb_upper, 5), " BB Lower: ", DoubleToString(bb_lower, 5));
        Print("   BB Touch - Upper: ", bb_upper_touch, " Lower: ", bb_lower_touch);
        Print("   Divergence - Bearish: ", bearish_divergence, " Bullish: ", bullish_divergence);
        Print("   Exit Divergence - Bearish: ", bearish_exit_divergence, " Bullish: ", bullish_exit_divergence);
        Print("   Sinyal - SELL: ", sell_signal, " BUY: ", buy_signal);
        Print("   Pozisyon - BUY: ", has_buy_position, " SELL: ", has_sell_position);
    }
    
    // Çıkış sinyalleri - Basit RSI bazlı çıkış
    if(has_buy_position && current_rsi <= BUY_Exit_RSI) // BUY çıkış: RSI belirlenen seviyenin altına düştüğünde
    {
        CloseAllPositions(ORDER_TYPE_BUY);
        debug_log_number++;
        Print("🔵 [", debug_log_number, "] BUY pozisyonu kapatıldı (RSI düşük: ", DoubleToString(current_rsi, 2), " <= ", BUY_Exit_RSI, ")");
        position_opened = false;
        current_position_type = 0;
    }

    if(has_sell_position && current_rsi >= SELL_Exit_RSI) // SELL çıkış: RSI belirlenen seviyenin üzerine çıktığında
    {
        CloseAllPositions(ORDER_TYPE_SELL);
        debug_log_number++;
        Print("🔵 [", debug_log_number, "] SELL pozisyonu kapatıldı (RSI yüksek: ", DoubleToString(current_rsi, 2), " >= ", SELL_Exit_RSI, ")");
        position_opened = false;
        current_position_type = 0;
    }
    
    // Giriş sinyalleri
    if(sell_signal && !has_sell_position)
    {
        OpenSellOrder(atr_current);
        position_opened = true;
        current_position_type = -1;
    }
    
    if(buy_signal && !has_buy_position)
    {
        OpenBuyOrder(atr_current);
        position_opened = true;
        current_position_type = 1;
    }
}

//+------------------------------------------------------------------+
//| Divergence analizi                                               |
//+------------------------------------------------------------------+
void AnalyzeDivergence(MqlRates &rates[], bool &bearish_div, bool &bullish_div, bool &bearish_exit_div, bool &bullish_exit_div)
{
    // Son yüksek ve düşük noktaları bul
    DivergencePoint current_high, current_low;

    // Debug için sayaçlar
    static int debug_divergence_count = 0;
    debug_divergence_count++;
    
    // Yüksek nokta arama
    int high_found = 0;
    for(int i = 2; i < Divergence_Lookback; i++)
    {
        if(rates[i].high > rates[i-1].high && rates[i].high > rates[i-2].high &&
           rates[i].high > rates[i+1].high && rates[i].high > rates[i+2].high)
        {
            current_high.bar_index = i;
            current_high.price = rates[i].high;
            current_high.rsi_value = rsi_buffer[i];
            current_high.time = rates[i].time;
            high_found = 1;
            break;
        }
    }

    if(Enable_Debug && debug_divergence_count <= 5)
    {
        Print("🔍 [DIVERGENCE DEBUG] Yüksek nokta arama:");
        Print("   Bulunan yüksek: ", high_found, " Bar: ", current_high.bar_index, " Fiyat: ", DoubleToString(current_high.price, 5));
    }
    
    // Düşük nokta arama
    int low_found = 0;
    for(int i = 2; i < Divergence_Lookback; i++)
    {
        if(rates[i].low < rates[i-1].low && rates[i].low < rates[i-2].low &&
           rates[i].low < rates[i+1].low && rates[i].low < rates[i+2].low)
        {
            current_low.bar_index = i;
            current_low.price = rates[i].low;
            current_low.rsi_value = rsi_buffer[i];
            current_low.time = rates[i].time;
            low_found = 1;
            break;
        }
    }

    if(Enable_Debug && debug_divergence_count <= 5)
    {
        Print("🔍 [DIVERGENCE DEBUG] Düşük nokta arama:");
        Print("   Bulunan düşük: ", low_found, " Bar: ", current_low.bar_index, " Fiyat: ", DoubleToString(current_low.price, 5));
    }
    
    // Bearish Divergence kontrolü (Giriş)
    if(current_high.bar_index > 0 && last_high.bar_index > 0)
    {
        double price_diff = current_high.price - last_high.price;
        double rsi_diff = current_high.rsi_value - last_high.rsi_value;
        double price_diff_points = price_diff / _Point;

        bool price_condition = price_diff > Min_Price_Move * _Point;
        bool rsi_condition = rsi_diff < -Min_RSI_Move;

        if(Enable_Debug && debug_divergence_count <= 10)
        {
            Print("🔍 [BEARISH DIV DEBUG]:");
            Print("   Şimdiki Yüksek - Bar: ", current_high.bar_index, " Fiyat: ", DoubleToString(current_high.price, 5), " RSI: ", DoubleToString(current_high.rsi_value, 2));
            Print("   Önceki Yüksek - Bar: ", last_high.bar_index, " Fiyat: ", DoubleToString(last_high.price, 5), " RSI: ", DoubleToString(last_high.rsi_value, 2));
            Print("   Fiyat Farkı: ", DoubleToString(price_diff_points, 1), " point (Min: ", Min_Price_Move, ") Koşul: ", price_condition);
            Print("   RSI Farkı: ", DoubleToString(rsi_diff, 2), " (Min: -", Min_RSI_Move, ") Koşul: ", rsi_condition);
        }

        if(price_condition && rsi_condition)
        {
            bearish_div = true;
            if(Enable_Debug)
            {
                Print("✅ [BEARISH DIVERGENCE BULUNDU!] Fiyat: +", DoubleToString(price_diff_points, 1), "pt, RSI: ", DoubleToString(rsi_diff, 2));
            }
        }

        // Bearish Exit Divergence (pozisyon varken ters yönde)
        if(current_position_type == -1 && price_diff < -Min_Price_Move * _Point && rsi_diff > Min_RSI_Move)
        {
            bearish_exit_div = true;
            if(Enable_Debug)
            {
                Print("🔵 [BEARISH EXIT DIVERGENCE] SELL pozisyonu için çıkış sinyali");
            }
        }
    }
    
    // Bullish Divergence kontrolü (Giriş)
    if(current_low.bar_index > 0 && last_low.bar_index > 0)
    {
        double price_diff = current_low.price - last_low.price;
        double rsi_diff = current_low.rsi_value - last_low.rsi_value;
        double price_diff_points = MathAbs(price_diff) / _Point;

        bool price_condition = price_diff < -Min_Price_Move * _Point;
        bool rsi_condition = rsi_diff > Min_RSI_Move;

        if(Enable_Debug && debug_divergence_count <= 10)
        {
            Print("🔍 [BULLISH DIV DEBUG]:");
            Print("   Şimdiki Düşük - Bar: ", current_low.bar_index, " Fiyat: ", DoubleToString(current_low.price, 5), " RSI: ", DoubleToString(current_low.rsi_value, 2));
            Print("   Önceki Düşük - Bar: ", last_low.bar_index, " Fiyat: ", DoubleToString(last_low.price, 5), " RSI: ", DoubleToString(last_low.rsi_value, 2));
            Print("   Fiyat Farkı: ", DoubleToString(price_diff_points, 1), " point (Min: ", Min_Price_Move, ") Koşul: ", price_condition);
            Print("   RSI Farkı: ", DoubleToString(rsi_diff, 2), " (Min: +", Min_RSI_Move, ") Koşul: ", rsi_condition);
        }

        if(price_condition && rsi_condition)
        {
            bullish_div = true;
            if(Enable_Debug)
            {
                Print("✅ [BULLISH DIVERGENCE BULUNDU!] Fiyat: -", DoubleToString(price_diff_points, 1), "pt, RSI: +", DoubleToString(rsi_diff, 2));
            }
        }

        // Bullish Exit Divergence (pozisyon varken ters yönde)
        if(current_position_type == 1 && price_diff > Min_Price_Move * _Point && rsi_diff < -Min_RSI_Move)
        {
            bullish_exit_div = true;
            if(Enable_Debug)
            {
                Print("🔵 [BULLISH EXIT DIVERGENCE] BUY pozisyonu için çıkış sinyali");
            }
        }
    }
    
    // Son noktaları güncelle
    if(current_high.bar_index > 0)
        last_high = current_high;
    if(current_low.bar_index > 0)
        last_low = current_low;
}

//+------------------------------------------------------------------+
//| BUY emri aç                                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder(double atr_value)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = 0, tp = 0;
    
    if(Use_ATR_SL)
    {
        sl = ask - (atr_value * ATR_Multiplier);
    }
    
    double lot = CalculateLotSize(sl > 0 ? MathAbs(ask - sl) : atr_value * ATR_Multiplier);
    
    trade.SetExpertMagicNumber(Magic_Number);
    
    if(trade.Buy(lot, _Symbol, ask, sl, tp, Comment_Prefix + "_BUY"))
    {
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(ask, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   RSI: ", DoubleToString(rsi_buffer[0], 2), " BB Lower: ", DoubleToString(bb_lower_buffer[0], 5));
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] BUY EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder(double atr_value)
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = 0, tp = 0;
    
    if(Use_ATR_SL)
    {
        sl = bid + (atr_value * ATR_Multiplier);
    }
    
    double lot = CalculateLotSize(sl > 0 ? MathAbs(bid - sl) : atr_value * ATR_Multiplier);
    
    trade.SetExpertMagicNumber(Magic_Number);
    
    if(trade.Sell(lot, _Symbol, bid, sl, tp, Comment_Prefix + "_SELL"))
    {
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] SELL EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(bid, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   RSI: ", DoubleToString(rsi_buffer[0], 2), " BB Upper: ", DoubleToString(bb_upper_buffer[0], 5));
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] SELL EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| Lot büyüklüğü hesapla                                           |
//+------------------------------------------------------------------+
double CalculateLotSize(double sl_distance)
{
    if(Risk_Percent <= 0) return Lot_Size;

    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * Risk_Percent / 100.0;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    double sl_points = sl_distance / point;
    double sl_ticks = sl_points * tick_size / point;
    double lot = risk_amount / (sl_ticks * tick_value);

    // Minimum ve maksimum lot kontrolü
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot = MathMax(lot, min_lot);
    lot = MathMin(lot, max_lot);
    lot = MathFloor(lot / lot_step) * lot_step;

    return lot;
}

//+------------------------------------------------------------------+
//| Pozisyon var mı kontrol et                                      |
//+------------------------------------------------------------------+
bool HasPosition(ENUM_ORDER_TYPE type)
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
                   PositionGetInteger(POSITION_TYPE) == type)
                {
                    return true;
                }
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void CloseAllPositions(ENUM_ORDER_TYPE type)
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
                   PositionGetInteger(POSITION_TYPE) == type)
                {
                    if(!trade.PositionClose(ticket))
                    {
                        debug_log_number++;
                        Print("❌ [", debug_log_number, "] Pozisyon kapatma hatası: ", trade.ResultRetcode());
                    }
                }
            }
        }
    }
}
