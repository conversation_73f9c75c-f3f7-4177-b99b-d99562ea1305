//+------------------------------------------------------------------+
//| WinAPITradePanel.mq5 - WinAPI Tabanlı Gelişmiş Sürükleme        |
//| Windows API kullanarak doğrudan mouse kontrolü                  |
//+------------------------------------------------------------------+
#property copyright "Horizon Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "WinAPI Tabanlı Gelişmiş Sürüklenebilir Panel"

#include <Trade\Trade.mqh>
#include <WinAPI\winapi.mqh> // This likely includes GetAsyncKeyState already

//+------------------------------------------------------------------+
//| Panel ayarları                                                  |
//+------------------------------------------------------------------+
input int PanelX = 50;           // Panel X pozisyonu
input int PanelY = 50;           // Panel Y pozisyonu

//+------------------------------------------------------------------+
//| Panel boyutları                                                 |
//+------------------------------------------------------------------+
#define PANEL_WIDTH     360
#define PANEL_HEIGHT    280
#define HEADER_HEIGHT   24
#define BUTTON_HEIGHT   20
#define ROW_HEIGHT      15
#define MARGIN          6

//+------------------------------------------------------------------+
//| WinAPI fonksiyonları                                            |
//+------------------------------------------------------------------+
#import "user32.dll"
int GetCursorPos(int& lpPoint[]);
int ScreenToClient(int hWnd, int& lpPoint[]);
int ClientToScreen(int hWnd, int& lpPoint[]);
// int GetAsyncKeyState(int vKey); // REMOVED: Likely already defined by winapi.mqh
int SetCapture(int hWnd);
int ReleaseCapture();
#import

//+------------------------------------------------------------------+
//| Global değişkenler                                              |
//+------------------------------------------------------------------+
string g_panelName = "WinAPITradePanel";
datetime g_lastUpdate = 0;

// Sürükleme durumu
bool g_isDragging = false;
int g_dragStartX = 0;
int g_dragStartY = 0;
int g_currentX = 0;
int g_currentY = 0;
long g_chartHandle = 0;

//+------------------------------------------------------------------+
//| Define VK_LBUTTON if not already defined                       |
//+------------------------------------------------------------------+
// Typically 0x01 for the left mouse button
#ifndef VK_LBUTTON
  #define VK_LBUTTON 0x01
#endif

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    g_currentX = PanelX;
    g_currentY = PanelY;
    g_chartHandle = ChartGetInteger(0, CHART_WINDOW_HANDLE);
    
    // Mouse event'lerini aktif et
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, true);
    
    CreatePanel();
    UpdatePanelContent(); // Changed from UpdatePanel()
    
    Print("✅ WinAPI Trade Panel başlatıldı - Gelişmiş sürükleme aktif");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    ObjectsDeleteAll(0, g_panelName);
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, false);
    ReleaseCapture();
    Print("🔄 WinAPI Trade Panel kapatıldı");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Sürükleme durumunu kontrol et
    CheckDragState();
    
    if(TimeCurrent() - g_lastUpdate >= 2)
    {
        UpdatePanelContent();
        g_lastUpdate = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        HandleButtonClick(sparam);
    }
    else if(id == CHARTEVENT_MOUSE_MOVE)
    {
        HandleMouseMove((int)lparam, (int)dparam, (int)sparam);
    }
}

//+------------------------------------------------------------------+
//| Sürükleme durumunu kontrol et                                   |
//+------------------------------------------------------------------+
void CheckDragState()
{
    // Sol mouse tuşu durumunu kontrol et
    bool leftButtonPressed = (GetAsyncKeyState(VK_LBUTTON) & 0x8000) != 0; 
    
    if(leftButtonPressed && !g_isDragging)
    {
        // Mouse pozisyonunu al
        int mousePos[2];
        GetCursorPos(mousePos);
        ScreenToClient((int)g_chartHandle, mousePos);
        
        // Mouse panel üzerinde mi kontrol et
        if(IsMouseOverPanel(mousePos[0], mousePos[1]))
        {
            StartDragging(mousePos[0], mousePos[1]);
        }
    }
    else if(!leftButtonPressed && g_isDragging)
    {
        StopDragging();
    }
    else if(leftButtonPressed && g_isDragging)
    {
        ContinueDragging();
    }
}

//+------------------------------------------------------------------+
//| Sürükleme başlat                                                |
//+------------------------------------------------------------------+
void StartDragging(int mouseX, int mouseY)
{
    g_isDragging = true;
    g_dragStartX = mouseX;
    g_dragStartY = mouseY;
    
    // Mouse'u yakala
    SetCapture((int)g_chartHandle);
    
    Print("🖱️ WinAPI Sürükleme başladı: (", mouseX, ",", mouseY, ")");
}

//+------------------------------------------------------------------+
//| Sürükleme devam et                                              |
//+------------------------------------------------------------------+
void ContinueDragging()
{
    // Mouse pozisyonunu al
    int mousePos[2];
    GetCursorPos(mousePos);
    ScreenToClient((int)g_chartHandle, mousePos);
    
    // Panel pozisyonunu güncelle
    int deltaX = mousePos[0] - g_dragStartX;
    int deltaY = mousePos[1] - g_dragStartY;
    
    g_currentX += deltaX;
    g_currentY += deltaY;
    
    // Ekran sınırları kontrolü
    int chartWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
    int chartHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);
    
    if(g_currentX < 0) g_currentX = 0;
    if(g_currentY < 0) g_currentY = 0;
    if(g_currentX + PANEL_WIDTH > chartWidth) g_currentX = chartWidth - PANEL_WIDTH;
    if(g_currentY + PANEL_HEIGHT > chartHeight) g_currentY = chartHeight - PANEL_HEIGHT;
    
    // Panel pozisyonunu güncelle
    UpdatePanelPosition();
    
    // Yeni başlangıç noktası
    g_dragStartX = mousePos[0];
    g_dragStartY = mousePos[1];
}

//+------------------------------------------------------------------+
//| Sürükleme durdur                                                |
//+------------------------------------------------------------------+
void StopDragging()
{
    g_isDragging = false;
    ReleaseCapture();
    
    Print("🖱️ WinAPI Sürükleme bitti: (", g_currentX, ",", g_currentY, ")");
}

//+------------------------------------------------------------------+
//| Mouse hareket işleyici                                          |
//+------------------------------------------------------------------+
void HandleMouseMove(int x, int y, int flags)
{
    // WinAPI ile daha hassas kontrol yapıldığı için burada basit kontrol
    if(g_isDragging)
    {
        // Sürükleme sırasında panel güncelleme
        ChartRedraw();
    }
}

//+------------------------------------------------------------------+
//| Mouse panel üzerinde mi kontrol et                              |
//+------------------------------------------------------------------+
bool IsMouseOverPanel(int mouseX, int mouseY)
{
    return (mouseX >= g_currentX && mouseX <= g_currentX + PANEL_WIDTH &&
            mouseY >= g_currentY && mouseY <= g_currentY + PANEL_HEIGHT);
}

//+------------------------------------------------------------------+
//| Panel pozisyonunu güncelle                                      |
//+------------------------------------------------------------------+
void UpdatePanelPosition()
{
    // Ana panel
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_XDISTANCE, g_currentX);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_YDISTANCE, g_currentY);
    
    // Header
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XDISTANCE, g_currentX);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YDISTANCE, g_currentY);
    
    // Butonları güncelle
    UpdateButtonPositions();
    
    // İçeriği güncelle
    UpdatePanelContent();
    
    // Chart'ı yeniden çiz
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| Panel oluştur                                                   |
//+------------------------------------------------------------------+
void CreatePanel()
{
    // Ana panel arka planı
    ObjectCreate(0, g_panelName + "_Background", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_XDISTANCE, g_currentX);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_YDISTANCE, g_currentY);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_YSIZE, PANEL_HEIGHT);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BGCOLOR, C'20,20,20');
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_COLOR, C'80,80,80');
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BACK, false);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_ZORDER, 1000);
    
    // Header - sürükleme alanı
    ObjectCreate(0, g_panelName + "_Header", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XDISTANCE, g_currentX);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YDISTANCE, g_currentY);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YSIZE, HEADER_HEIGHT);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BGCOLOR, C'0,90,160');
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BACK, false);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_ZORDER, 1001);
    
    CreateButtons();
}

//+------------------------------------------------------------------+
//| Butonları oluştur                                               |
//+------------------------------------------------------------------+
void CreateButtons()
{
    int btn_y = g_currentY + 160;
    int btn_width = (PANEL_WIDTH - 4 * MARGIN) / 3;
    
    // Karlı Kapat butonu
    CreateButton("BtnProfit", "💚 Karlı", g_currentX + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, C'0,100,0');
    
    // Zarar Kapat butonu  
    CreateButton("BtnLoss", "❤️ Zarar", g_currentX + MARGIN + btn_width + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, C'100,0,0');
    
    // Hepsini Kapat butonu
    CreateButton("BtnAll", "🚫 Hepsini Kapat", g_currentX + MARGIN, btn_y + BUTTON_HEIGHT + MARGIN, 2 * btn_width + MARGIN, BUTTON_HEIGHT, C'100,100,0');
}

//+------------------------------------------------------------------+
//| Buton pozisyonlarını güncelle                                   |
//+------------------------------------------------------------------+
void UpdateButtonPositions()
{
    int btn_y = g_currentY + 160;
    int btn_width = (PANEL_WIDTH - 4 * MARGIN) / 3;
    
    // Karlı Kapat butonu
    ObjectSetInteger(0, g_panelName + "_BtnProfit", OBJPROP_XDISTANCE, g_currentX + MARGIN);
    ObjectSetInteger(0, g_panelName + "_BtnProfit", OBJPROP_YDISTANCE, btn_y);
    ObjectSetInteger(0, g_panelName + "_BtnProfit_Text", OBJPROP_XDISTANCE, g_currentX + MARGIN + btn_width/2);
    ObjectSetInteger(0, g_panelName + "_BtnProfit_Text", OBJPROP_YDISTANCE, btn_y + BUTTON_HEIGHT/2 - 5);
    
    // Zarar Kapat butonu
    ObjectSetInteger(0, g_panelName + "_BtnLoss", OBJPROP_XDISTANCE, g_currentX + MARGIN + btn_width + MARGIN);
    ObjectSetInteger(0, g_panelName + "_BtnLoss", OBJPROP_YDISTANCE, btn_y);
    ObjectSetInteger(0, g_panelName + "_BtnLoss_Text", OBJPROP_XDISTANCE, g_currentX + MARGIN + btn_width + MARGIN + btn_width/2);
    ObjectSetInteger(0, g_panelName + "_BtnLoss_Text", OBJPROP_YDISTANCE, btn_y + BUTTON_HEIGHT/2 - 5);
    
    // Hepsini Kapat butonu
    ObjectSetInteger(0, g_panelName + "_BtnAll", OBJPROP_XDISTANCE, g_currentX + MARGIN);
    ObjectSetInteger(0, g_panelName + "_BtnAll", OBJPROP_YDISTANCE, btn_y + BUTTON_HEIGHT + MARGIN);
    ObjectSetInteger(0, g_panelName + "_BtnAll_Text", OBJPROP_XDISTANCE, g_currentX + MARGIN + (2 * btn_width + MARGIN)/2);
    ObjectSetInteger(0, g_panelName + "_BtnAll_Text", OBJPROP_YDISTANCE, btn_y + BUTTON_HEIGHT + MARGIN + BUTTON_HEIGHT/2 - 5);
}

//+------------------------------------------------------------------+
//| Buton oluştur                                                   |
//+------------------------------------------------------------------+
void CreateButton(string name, string text, int x, int y, int width, int height, color bgColor)
{
    string objName = g_panelName + "_" + name;

    // Buton arka planı
    ObjectCreate(0, objName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, objName, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, objName, OBJPROP_YSIZE, height);
    ObjectSetInteger(0, objName, OBJPROP_BGCOLOR, bgColor);
    ObjectSetInteger(0, objName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, C'50,50,50');
    ObjectSetInteger(0, objName, OBJPROP_BACK, false);
    ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName, OBJPROP_ZORDER, 1002);

    // Buton metni
    ObjectCreate(0, objName + "_Text", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_XDISTANCE, x + width/2);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_YDISTANCE, y + height/2 - 5);
    ObjectSetString(0, objName + "_Text", OBJPROP_TEXT, text);
    ObjectSetString(0, objName + "_Text", OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, objName + "_Text", OBJPROP_FONTSIZE, 8);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_ANCHOR, ANCHOR_CENTER);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_BACK, false);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_ZORDER, 1003);
}

//+------------------------------------------------------------------+
//| Panel içeriğini güncelle                                        |
//+------------------------------------------------------------------+
void UpdatePanelContent()
{
    // Panel verilerini hesapla
    int positionCount = 0;
    double totalVolume = 0;
    double totalProfit = 0;
    int profitableCount = 0;
    int losingCount = 0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);

        positionCount++;
        totalVolume += volume;
        totalProfit += profit;

        if(profit > 0)
            profitableCount++;
        else if(profit < 0)
            losingCount++;
    }

    // Günlük P&L hesapla
    double dailyPL = CalculateDailyPL(totalProfit);

    // Panel içeriğini çiz
    DrawPanelContent(positionCount, totalVolume, totalProfit, profitableCount, losingCount, dailyPL);
}

//+------------------------------------------------------------------+
//| Panel içeriğini çiz                                             |
//+------------------------------------------------------------------+
void DrawPanelContent(int posCount, double totalVol, double totalPL, int profitCount, int lossCount, double dailyPL)
{
    int y_pos = g_currentY + HEADER_HEIGHT + MARGIN;

    // Başlık
    CreateLabel("Title", "📊 WinAPI İşlem Paneli", g_currentX + PANEL_WIDTH/2, g_currentY + 12, clrWhite, 9, ANCHOR_CENTER);

    // Günlük P&L
    string dailyText = "💰 Günlük: $" + DoubleToString(dailyPL, 2);
    color dailyColor = (dailyPL >= 0) ? clrLimeGreen : clrTomato;
    CreateLabel("DailyPL", dailyText, g_currentX + MARGIN, y_pos, dailyColor, 8);
    y_pos += 16;

    // Açık pozisyon sayısı
    string posText = "📈 Pozisyon: " + IntegerToString(posCount);
    CreateLabel("PositionCount", posText, g_currentX + MARGIN, y_pos, clrWhite, 8);
    y_pos += 15;

    // Toplam hacim
    string volText = "📊 Hacim: " + DoubleToString(totalVol, 2);
    CreateLabel("TotalVolume", volText, g_currentX + MARGIN, y_pos, clrWhite, 8);
    y_pos += 15;

    // Toplam P&L
    string plText = "💵 P&L: $" + DoubleToString(totalPL, 2);
    color plColor = (totalPL >= 0) ? clrLimeGreen : clrTomato;
    CreateLabel("TotalPL", plText, g_currentX + MARGIN, y_pos, plColor, 8);
    y_pos += 15;

    // Karlı/Zararlı sayısı
    string countText = "📊 Karlı: " + IntegerToString(profitCount) + " | Zararlı: " + IntegerToString(lossCount);
    CreateLabel("ProfitLossCount", countText, g_currentX + MARGIN, y_pos, clrWhite, 8);
    y_pos += 18;

    // Pozisyon listesi
    DrawPositionList(y_pos);
}

//+------------------------------------------------------------------+
//| Pozisyon listesini çiz                                          |
//+------------------------------------------------------------------+
void DrawPositionList(int start_y)
{
    // Pozisyon listesi başlığı
    CreateLabel("ListTitle", "📋 Açık Pozisyonlar:", g_currentX + MARGIN, start_y, clrYellow, 8);

    int y_pos = start_y + 15;
    int maxRows = 3;
    int rowCount = 0;

    // Önceki pozisyon label'larını temizle
    for(int i = 0; i < 10; i++) // Increased to 10 to be safe, or make it dynamic based on maxRows
    {
        ObjectDelete(0, g_panelName + "_Pos" + IntegerToString(i));
    }

    // Açık pozisyonları listele
    for(int i = 0; i < PositionsTotal() && rowCount < maxRows; i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);

        string typeStr = (type == POSITION_TYPE_BUY) ? "BUY" : "SELL";
        string profitStr = (profit >= 0) ? "+" + DoubleToString(profit, 1) : DoubleToString(profit, 1);
        color profitColor = (profit >= 0) ? clrLimeGreen : clrTomato;

        string posText = StringFormat("%s %s %.2f | $%s",
                                      typeStr,
                                      symbol,
                                      volume,
                                      profitStr);

        CreateLabel("Pos" + IntegerToString(rowCount), posText, g_currentX + MARGIN, y_pos, profitColor, 7);
        y_pos += ROW_HEIGHT;
        rowCount++;
    }

    // Eğer çok fazla pozisyon varsa "..." göster
    if(PositionsTotal() > maxRows)
    {
        string moreText = "... +" + IntegerToString(PositionsTotal() - maxRows) + " daha";
        CreateLabel("MorePositions", moreText, g_currentX + MARGIN, y_pos, clrGray, 7);
    }
}

//+------------------------------------------------------------------+
//| Label oluştur                                                   |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int x, int y, color clr, int fontSize, ENUM_ANCHOR_POINT anchor = ANCHOR_LEFT_UPPER)
{
    string objName = g_panelName + "_" + name;

    ObjectDelete(0, objName);
    ObjectCreate(0, objName, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetString(0, objName, OBJPROP_TEXT, text);
    ObjectSetString(0, objName, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, fontSize);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, objName, OBJPROP_ANCHOR, anchor);
    ObjectSetInteger(0, objName, OBJPROP_BACK, false);
    ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName, OBJPROP_ZORDER, 1002);
}

//+------------------------------------------------------------------+
//| Günlük P&L hesapla                                              |
//+------------------------------------------------------------------+
double CalculateDailyPL(double currentPL)
{
    double dailyPL = 0;
    datetime todayStart = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));

    HistorySelect(todayStart, TimeCurrent());

    for(int i = 0; i < HistoryDealsTotal(); i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket > 0)
        {
            ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);
            if(dealType == DEAL_TYPE_BUY || dealType == DEAL_TYPE_SELL)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                double commission = HistoryDealGetDouble(ticket, DEAL_COMMISSION);
                double swap = HistoryDealGetDouble(ticket, DEAL_SWAP);

                dailyPL += (profit + commission + swap);
            }
        }
    }

    dailyPL += currentPL;
    return dailyPL;
}

//+------------------------------------------------------------------+
//| Buton tıklama işleyici                                          |
//+------------------------------------------------------------------+
void HandleButtonClick(string objName)
{
    if(StringFind(objName, "BtnProfit") >= 0)
    {
        CloseProfitablePositions();
    }
    else if(StringFind(objName, "BtnLoss") >= 0)
    {
        CloseLosingPositions();
    }
    else if(StringFind(objName, "BtnAll") >= 0)
    {
        CloseAllPositions();
    }
}

//+------------------------------------------------------------------+
//| İşlem kapatma fonksiyonları                                     |
//+------------------------------------------------------------------+
void CloseProfitablePositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "" || PositionGetDouble(POSITION_PROFIT) <= 0)
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
            closedCount++;
    }

    Print("💚 ", closedCount, " karlı pozisyon kapatıldı");
    UpdatePanelContent();
}

void CloseLosingPositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "" || PositionGetDouble(POSITION_PROFIT) >= 0)
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
            closedCount++;
    }

    Print("❤️ ", closedCount, " zararlı pozisyon kapatıldı");
    UpdatePanelContent();
}

void CloseAllPositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
            closedCount++;
    }

    Print("🚫 ", closedCount, " pozisyon kapatıldı");
    UpdatePanelContent();
}