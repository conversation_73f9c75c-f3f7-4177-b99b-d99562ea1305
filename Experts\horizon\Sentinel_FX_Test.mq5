//+------------------------------------------------------------------+
//| Sentinel FX Test - Parametre Test EA                            |
//+------------------------------------------------------------------+
#property copyright "Horizon Trading"
#property version   "1.00"

//--- Input parametreleri
input group "=== 🤖 İŞLEM AYARLARI ==="
input bool                  Enable_AutoTrade = true;             // ✅ Otomatik İşlem Yapma
input bool                  Signal_Only_Mode = false;            // 📡 Sadece Sinyal Modu

input group "=== 🎯 DIP/TEPE YAKALAMA ==="
input double                RSI_Oversold = 40.0;                 // RSI Aşırı Satım Seviyesi
input double                RSI_Overbought = 60.0;               // RSI Aşırı Alım Se<PERSON>i

input group "=== 💰 RİSK YÖNETİMİ ==="
input double                Risk_Percent = 2.0;                  // Risk Yüzdesi
input double                Fixed_Lot_Size = 0.01;               // Sabit Lot Büyüklüğü

input group "=== 📱 TELEGRAM ==="
input bool                  Enable_Telegram = true;              // Telegram Bildirimleri
input string                Telegram_Token = "";                 // Bot Token
input string                Telegram_ChatID = "";                // Chat ID

input group "=== ⚙️ GENEL ==="
input bool                  Enable_Debug = true;                 // Debug Logları

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== SENTINEL FX TEST PARAMETRELERI ===");
    Print("🤖 AutoTrade: ", Enable_AutoTrade);
    Print("📡 Signal Only: ", Signal_Only_Mode);
    Print("🎯 RSI Oversold: ", RSI_Oversold);
    Print("🎯 RSI Overbought: ", RSI_Overbought);
    Print("💰 Risk: ", Risk_Percent, "%");
    Print("💰 Fixed Lot: ", Fixed_Lot_Size);
    Print("📱 Telegram: ", Enable_Telegram);
    Print("📱 Token Length: ", StringLen(Telegram_Token));
    Print("📱 Chat ID: ", Telegram_ChatID);
    Print("⚙️ Debug: ", Enable_Debug);
    Print("=====================================");
    
    if(Enable_AutoTrade)
    {
        Print("✅ AUTOTRADE AÇIK - İşlemler otomatik açılacak");
    }
    else if(Signal_Only_Mode)
    {
        Print("📡 SADECE SİNYAL MODU - İşlem açılmayacak, sadece sinyal");
    }
    else
    {
        Print("⏸️ AUTOTRADE KAPALI - Hiçbir işlem yapılmayacak");
    }
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("Sentinel FX Test durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    static datetime last_report = 0;
    
    // 10 saniyede bir durum raporu
    if(TimeCurrent() - last_report >= 10)
    {
        Print("📊 Durum Raporu:");
        Print("   AutoTrade: ", Enable_AutoTrade ? "AÇIK" : "KAPALI");
        Print("   Signal Only: ", Signal_Only_Mode ? "AÇIK" : "KAPALI");
        Print("   Telegram: ", Enable_Telegram ? "AÇIK" : "KAPALI");
        Print("   Risk: ", Risk_Percent, "%");
        
        last_report = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_KEYDOWN)
    {
        if(lparam == 65) // A tuşu - AutoTrade toggle
        {
            Print("🔄 A tuşuna basıldı - AutoTrade durumu kontrol ediliyor");
            Print("   Mevcut AutoTrade: ", Enable_AutoTrade);
            Print("   Mevcut Signal Only: ", Signal_Only_Mode);
            
            if(Enable_AutoTrade)
                Print("   ✅ AutoTrade AÇIK - Otomatik işlemler yapılıyor");
            else if(Signal_Only_Mode)
                Print("   📡 Signal Only AÇIK - Sadece sinyaller veriliyor");
            else
                Print("   ⏸️ Her ikisi de KAPALI - Hiçbir işlem yapılmıyor");
        }
        
        if(lparam == 83) // S tuşu - Signal test
        {
            Print("📡 S tuşuna basıldı - Sinyal testi");
            
            if(Enable_AutoTrade)
            {
                Print("   🤖 AutoTrade açık - İşlem açılacak");
            }
            else if(Signal_Only_Mode)
            {
                Print("   📡 Signal Only açık - Sadece sinyal verilecek");
            }
            else
            {
                Print("   ⏸️ Her ikisi de kapalı - Hiçbir şey yapılmayacak");
            }
        }
    }
}
