//+------------------------------------------------------------------+
//|                                    XAUUSD_MA_Crossover_EA.mq5 |
//|                                                    Emre <PERSON>un |
//|                                                                      |
//+------------------------------------------------------------------+
#property copyright "Emre Usun"
#property link      ""
#property version   "1.00"

//--- Input parameters
input group "=== MA Ayarları ==="
input int                   MA_Period_Long = 50;                    // Uzun MA Periyodu (MA 50)
input int                   MA_Period_Mid = 14;                     // Orta MA Periyodu (MA 14)
input int                   MA_Period_Short = 7;                    // Kısa MA Periyodu (MA 7)
input ENUM_MA_METHOD        MA_Method = MODE_SMA;                   // MA Hesaplama Metodu
input ENUM_APPLIED_PRICE    MA_Applied_Price = PRICE_CLOSE;         // MA Uygulanan Fiyat

input group "=== Sinyal <PERSON>ları ==="
input int                   Confirmation_Bars = 5;                  // MA50 Onay Bekleme Bar Sayısı
input double                Min_Cross_Distance = 0.05;              // Minimum Kesişim Mesafesi (pip)
input double                MA50_Safety_Points = 10.0;              // MA50'den güvenlik mesafesi (point)

input group "=== Çıkış Ayarları ==="
input double                MA7_Exit_Points = 20.0;                 // MA7 düşüş/yükseliş çıkış eşiği (point)

input group "=== Williams %R Çıkış Ayarları ==="
input int                   WPR_Period = 14;                       // Williams %R Periyodu

input group "=== BUY Pozisyon WPR Çıkış ==="
input double                WPR_Buy_Start_Level = -20.0;           // BUY Çıkış Takip Başlangıç Seviyesi (WPR < -20)
input double                WPR_Buy_Exit_Level = -20.0;            // BUY Çıkış Tetik Seviyesi (WPR >= -20)

input group "=== SELL Pozisyon WPR Çıkış ==="
input double                WPR_Sell_Start_Level = -80.0;          // SELL Çıkış Takip Başlangıç Seviyesi (WPR > -80)
input double                WPR_Sell_Exit_Level = -80.0;           // SELL Çıkış Tetik Seviyesi (WPR <= -80)

input group "=== Risk Yönetimi ==="
input double                Lot_Size = 0.01;                       // Lot Büyüklüğü
input double                ATR_Multiplier = 2.2;                  // ATR Çarpanı (SL için)
input int                   ATR_Period = 14;                       // ATR Periyodu
input double                Risk_Percent = 2.0;                    // Risk Yüzdesi (% bakiye)
input bool                  Use_Fixed_Lot = true;                  // Sabit Lot Kullan
input int                   Magic_Number = 12345;                  // Magic Number

input group "=== Bildirim Ayarları ==="
input bool                  Enable_Alerts = true;                  // Uyarıları Etkinleştir
input bool                  Enable_Sound = true;                   // Ses Uyarısı
input bool                  Enable_Popup = true;                   // Popup Uyarısı
input bool                  Enable_Email = false;                  // Email Uyarısı
input bool                  Enable_Push = false;                   // Push Bildirimi

//--- Handles
int ma_long_handle;   // MA50
int ma_mid_handle;    // MA14
int ma_short_handle;  // MA7
int wpr_handle;       // Williams %R
int atr_handle;

//--- Buffers
double MA_Long_Buffer[];   // MA50
double MA_Mid_Buffer[];    // MA14
double MA_Short_Buffer[];  // MA7
double WPR_Buffer[];       // Williams %R
double ATR_Buffer[];

//--- Global variables
datetime last_alert_time = 0;
int debug_log_number = 0;

// WPR çıkış takip değişkenleri
bool buy_wpr_tracking = false;    // BUY pozisyonu WPR takip durumu
bool sell_wpr_tracking = false;   // SELL pozisyonu WPR takip durumu

//--- Bekleyen sinyal yapısı
struct PendingSignal
{
    int signal_type;        // 1=BUY, -1=SELL, 0=YOK
    int signal_bar;         // Sinyalin oluştuğu bar index'i
    int bars_waiting;       // Kaç bardır bekliyor
    double signal_price;    // Sinyal fiyatı
    double signal_high;     // Sinyal barının high'ı
    double signal_low;      // Sinyal barının low'ı
};

PendingSignal pending_signal;

//--- Açık pozisyon takibi
struct OpenPosition
{
    int position_type;      // 1=BUY, -1=SELL, 0=YOK
    int entry_bar;          // Giriş barı
    double entry_price;     // Giriş fiyatı
    bool exit_ready;        // Çıkış hazır mı? (RSI seviyeyi geçti mi?)
    ulong ticket;           // Pozisyon ticket'ı
};

OpenPosition open_position;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Handle'ları oluştur
    ma_long_handle = iMA(_Symbol, _Period, MA_Period_Long, 0, MA_Method, MA_Applied_Price);
    ma_mid_handle = iMA(_Symbol, _Period, MA_Period_Mid, 0, MA_Method, MA_Applied_Price);
    ma_short_handle = iMA(_Symbol, _Period, MA_Period_Short, 0, MA_Method, MA_Applied_Price);
    wpr_handle = iWPR(_Symbol, _Period, WPR_Period);
    atr_handle = iATR(_Symbol, _Period, ATR_Period);

    // Handle kontrolü
    if(ma_long_handle == INVALID_HANDLE || ma_mid_handle == INVALID_HANDLE ||
       ma_short_handle == INVALID_HANDLE || wpr_handle == INVALID_HANDLE ||
       atr_handle == INVALID_HANDLE)
    {
        Print("❌ EA handle'ları oluşturulamadı!");
        return INIT_FAILED;
    }

    // Buffer'ları time series olarak hazırla (Bar 0 = en son)
    ArraySetAsSeries(MA_Long_Buffer, true);
    ArraySetAsSeries(MA_Mid_Buffer, true);
    ArraySetAsSeries(MA_Short_Buffer, true);
    ArraySetAsSeries(WPR_Buffer, true);
    ArraySetAsSeries(ATR_Buffer, true);

    // Yapıları başlat
    pending_signal.signal_type = 0;
    pending_signal.signal_bar = 0;
    pending_signal.bars_waiting = 0;
    debug_log_number = 0;

    // WPR takip değişkenlerini sıfırla
    buy_wpr_tracking = false;
    sell_wpr_tracking = false;
    
    open_position.position_type = 0;
    open_position.entry_bar = 0;
    open_position.entry_price = 0;
    open_position.exit_ready = false;
    open_position.ticket = 0;

    // Strategy Tester için timer başlat
    EventSetTimer(1); // Her saniye kontrol et

    Print("✅ XAUUSD MA Crossover EA başlatıldı - Magic: ", Magic_Number);
    Print("📊 Strategy Tester modu aktif - Timer başlatıldı");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    EventKillTimer(); // Timer'ı durdur
    Print("🔄 XAUUSD MA Crossover EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Pozisyon durumu kontrol et
    CheckPositionStatus();

    // Yeterli bar var mı kontrol et
    if(Bars(_Symbol, _Period) < MA_Period_Long + 10) return;
    
    // Handle'lar hazır mı kontrol et
    if(BarsCalculated(ma_long_handle) < 10 ||
       BarsCalculated(ma_mid_handle) < 10 ||
       BarsCalculated(ma_short_handle) < 10 ||
       BarsCalculated(wpr_handle) < 10 ||
       BarsCalculated(atr_handle) < 10)
        return;

    // Verileri kopyala
    if(CopyBuffer(ma_long_handle, 0, 0, 10, MA_Long_Buffer) <= 0) return;
    if(CopyBuffer(ma_mid_handle, 0, 0, 10, MA_Mid_Buffer) <= 0) return;
    if(CopyBuffer(ma_short_handle, 0, 0, 10, MA_Short_Buffer) <= 0) return;
    if(CopyBuffer(wpr_handle, 0, 0, 10, WPR_Buffer) <= 0) return;
    if(CopyBuffer(atr_handle, 0, 0, 10, ATR_Buffer) <= 0) return;

    // Fiyat verileri
    MqlRates rates[];
    ArraySetAsSeries(rates, true);  // Time series
    if(CopyRates(_Symbol, _Period, 0, 10, rates) <= 0) return;

    // Son tamamlanmış bar analizi (Bar 1) - DÜZELTME: Bar 0 kullan
    int current_bar = 0; // En son bar (canlı bar değil, son tamamlanmış)
    AnalyzeSignals(current_bar, rates);
}

//+------------------------------------------------------------------+
//| Timer function (Strategy Tester için)                           |
//+------------------------------------------------------------------+
void OnTimer()
{
    // OnTick ile aynı mantık ama timer bazlı
    OnTick();
}

//+------------------------------------------------------------------+
//| Sinyal analiz fonksiyonu                                        |
//+------------------------------------------------------------------+
void AnalyzeSignals(int bar_index, MqlRates &rates[])
{
    // MA değerleri - Time Series indexleme (Bar 0 = en son)
    double ma50_current = MA_Long_Buffer[bar_index];      // MA50 (En son bar)
    double ma14_current = MA_Mid_Buffer[bar_index];       // MA14 (En son bar)
    double ma7_current = MA_Short_Buffer[bar_index];      // MA7 (En son bar)

    // WPR ve ATR değerleri
    double wpr_current = WPR_Buffer[bar_index];           // Williams %R
    double atr_current = ATR_Buffer[bar_index];           // ATR

    // Fiyat değerleri
    double close_current = rates[bar_index].close;
    double high_current = rates[bar_index].high;
    double low_current = rates[bar_index].low;
    double open_current = rates[bar_index].open;

    // Mevcut pozisyon kontrolü
    bool has_buy_position = HasOpenPosition(1); // 1 = BUY
    bool has_sell_position = HasOpenPosition(0); // 0 = SELL

    // 🎯 YENİ KARAR MATRİSİ

    // 📊 GİRİŞ SİNYALLERİ
    // BUY: MA50 > MA14 > MA7 (Üçlü MA sıralaması)
    bool buy_signal = (ma50_current > ma14_current) && (ma14_current > ma7_current);

    // SELL: MA50 < MA14 < MA7 (Ters üçlü MA sıralaması)
    bool sell_signal = (ma50_current < ma14_current) && (ma14_current < ma7_current);

    // 🚪 ÇIKIŞ SİNYALLERİ
    // BUY Çıkış: WPR < -20 (takip başlat) → WPR >= -20 (çıkış)
    if(has_buy_position)
    {
        if(wpr_current < WPR_Buy_Start_Level && !buy_wpr_tracking)
        {
            buy_wpr_tracking = true;
            debug_log_number++;
            Print("🔍 [", debug_log_number, "] BUY WPR takip başladı - WPR: ", DoubleToString(wpr_current, 2), " < ", WPR_Buy_Start_Level);
        }

        if(buy_wpr_tracking && wpr_current >= WPR_Buy_Exit_Level)
        {
            CloseBuyPosition(rates[bar_index].time);
            buy_wpr_tracking = false;
            debug_log_number++;
            Print("🔵 [", debug_log_number, "] BUY pozisyonu kapatıldı (WPR çıkış) - WPR: ", DoubleToString(wpr_current, 2), " >= ", WPR_Buy_Exit_Level);
        }
    }

    // SELL Çıkış: WPR > -80 (takip başlat) → WPR <= -80 (çıkış)
    if(has_sell_position)
    {
        if(wpr_current > WPR_Sell_Start_Level && !sell_wpr_tracking)
        {
            sell_wpr_tracking = true;
            debug_log_number++;
            Print("🔍 [", debug_log_number, "] SELL WPR takip başladı - WPR: ", DoubleToString(wpr_current, 2), " > ", WPR_Sell_Start_Level);
        }

        if(sell_wpr_tracking && wpr_current <= WPR_Sell_Exit_Level)
        {
            CloseSellPosition(rates[bar_index].time);
            sell_wpr_tracking = false;
            debug_log_number++;
            Print("🔵 [", debug_log_number, "] SELL pozisyonu kapatıldı (WPR çıkış) - WPR: ", DoubleToString(wpr_current, 2), " <= ", WPR_Sell_Exit_Level);
        }

    // 📊 GİRİŞ İŞLEMLERİ
    // Debug - sadece sinyal olduğunda
    if(buy_signal || sell_signal)
    {
        debug_log_number++;
        Print("🔍 [", debug_log_number, "] YENİ KARAR MATRİSİ SİNYALİ:");
        Print("   MA50: ", DoubleToString(ma50_current, 5), " MA14: ", DoubleToString(ma14_current, 5), " MA7: ", DoubleToString(ma7_current, 5));
        Print("   WPR: ", DoubleToString(wpr_current, 2));
        Print("   BUY Koşul (MA50>MA14>MA7): ", buy_signal);
        Print("   SELL Koşul (MA50<MA14<MA7): ", sell_signal);
        Print("   Pozisyon - BUY: ", has_buy_position, " SELL: ", has_sell_position);
    }

    // BUY sinyali işlemi
    if(buy_signal && !has_buy_position)
    {
        // Varsa SELL pozisyonunu kapat
        if(has_sell_position)
        {
            CloseSellPosition(rates[bar_index].time);
            sell_wpr_tracking = false;
            debug_log_number++;
            Print("🔵 [", debug_log_number, "] SELL pozisyonu kapatıldı (BUY sinyali)");
        }

        OpenBuyOrder(atr_current, close_current, high_current, low_current, rates[bar_index].time);
        buy_wpr_tracking = false; // Yeni pozisyon için takip sıfırla
    }

    // SELL sinyali işlemi
    if(sell_signal && !has_sell_position)
    {
        // Varsa BUY pozisyonunu kapat
        if(has_buy_position)
        {
            CloseBuyPosition(rates[bar_index].time);
            buy_wpr_tracking = false;
            debug_log_number++;
            Print("🔵 [", debug_log_number, "] BUY pozisyonu kapatıldı (SELL sinyali)");
        }

        OpenSellOrder(atr_current, close_current, high_current, low_current, rates[bar_index].time);
        sell_wpr_tracking = false; // Yeni pozisyon için takip sıfırla
    }

}

//+------------------------------------------------------------------+
//| Pozisyon var mı kontrol et                                      |
//+------------------------------------------------------------------+
bool HasOpenPosition(int position_type)
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
                   PositionGetInteger(POSITION_TYPE) == position_type)
                {
                    return true;
                }
            }
        }
    }
    return false;
}





//+------------------------------------------------------------------+
//| BUY emri aç                                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder(double atr_value, double close_price, double high_price, double low_price, datetime bar_time)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = 0, tp = 0;

    if(Use_ATR_SL)
    {
        sl = ask - (atr_value * ATR_Multiplier);
    }

    double lot = CalculateLotSize(sl > 0 ? MathAbs(ask - sl) : atr_value * ATR_Multiplier);

    trade.SetExpertMagicNumber(Magic_Number);

    if(trade.Buy(lot, _Symbol, ask, sl, tp, Comment_Prefix + "_BUY"))
    {
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(ask, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   MA50>MA14>MA7 sıralaması ile BUY açıldı");
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] BUY EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder(double atr_value, double close_price, double high_price, double low_price, datetime bar_time)
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = 0, tp = 0;

    if(Use_ATR_SL)
    {
        sl = bid + (atr_value * ATR_Multiplier);
    }

    double lot = CalculateLotSize(sl > 0 ? MathAbs(bid - sl) : atr_value * ATR_Multiplier);

    trade.SetExpertMagicNumber(Magic_Number);

    if(trade.Sell(lot, _Symbol, bid, sl, tp, Comment_Prefix + "_SELL"))
    {
        debug_log_number++;
        Print("� [", debug_log_number, "] SELL EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(bid, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   MA50<MA14<MA7 sıralaması ile SELL açıldı");
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] SELL EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| Lot büyüklüğü hesapla                                           |
//+------------------------------------------------------------------+
double CalculateLotSize(double sl_distance)
{
    if(Risk_Percent <= 0) return Lot_Size;

    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * Risk_Percent / 100.0;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    double sl_points = sl_distance / point;
    double sl_ticks = sl_points * tick_size / point;
    double lot = risk_amount / (sl_ticks * tick_value);

    // Minimum ve maksimum lot kontrolü
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot = MathMax(lot, min_lot);
    lot = MathMin(lot, max_lot);
    lot = MathFloor(lot / lot_step) * lot_step;

    return lot;
    {
        if(result.retcode == TRADE_RETCODE_DONE)
        {
            // Pozisyon bilgilerini kaydet
            open_position.position_type = 1;
            open_position.entry_bar = 0; // Canlı bar
            open_position.entry_price = result.price;
            open_position.exit_ready = false;
            open_position.ticket = result.order;

            debug_log_number++;
            Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI! Ticket: ", result.order,
                  " Fiyat: ", result.price, " SL: ", price - sl_price, " TP: RSI Çıkış",
                  " Lot: ", lot_size, " ATR SL: ", DoubleToString(sl_price/_Point, 1), " pip");

            // Alert gönder
            SendAlert("BUY EMRİ AÇILDI", result.price, bar_time);
        }
        else
        {
            Print("❌ BUY emri açılamadı! Hata: ", result.retcode, " - ", result.comment);
        }
    }
    else
    {
        Print("❌ BUY emri gönderilemedi!");
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder(double price, double atr_value, datetime bar_time)
{
    // Zaten açık pozisyon varsa çık
    if(open_position.position_type != 0) return;

    // Lot büyüklüğünü hesapla
    double lot_size = CalculateLotSize(atr_value);

    // Sadece SL hesapla (TP yok - RSI ile çıkış)
    double sl_distance = atr_value * ATR_Multiplier;
    double sl_price = sl_distance;

    // Emir gönder
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lot_size;
    request.type = ORDER_TYPE_SELL;
    request.price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    request.sl = price + sl_price; // SL = Giriş + ATR mesafesi
    request.tp = 0; // TP yok - RSI ile çıkış
    request.magic = Magic_Number;
    request.comment = "MA Cross SELL (RSI Exit)";
    request.deviation = 10;
    request.type_filling = GetFillingMode(); // Doğru filling mode

    if(OrderSend(request, result))
    {
        if(result.retcode == TRADE_RETCODE_DONE)
        {
            // Pozisyon bilgilerini kaydet
            open_position.position_type = -1;
            open_position.entry_bar = 0; // Canlı bar
            open_position.entry_price = result.price;
            open_position.exit_ready = false;
            open_position.ticket = result.order;

            debug_log_number++;
            Print("🔴 [", debug_log_number, "] SELL EMRİ AÇILDI! Ticket: ", result.order,
                  " Fiyat: ", result.price, " SL: ", price + sl_price, " TP: RSI Çıkış",
                  " Lot: ", lot_size, " ATR SL: ", DoubleToString(sl_price/_Point, 1), " pip");

            // Alert gönder
            SendAlert("SELL EMRİ AÇILDI", result.price, bar_time);
        }
        else
        {
            Print("❌ SELL emri açılamadı! Hata: ", result.retcode, " - ", result.comment);
        }
    }
    else
    {
        Print("❌ SELL emri gönderilemedi!");
    }
}

//+------------------------------------------------------------------+
//| BUY pozisyonu kapat                                              |
//+------------------------------------------------------------------+
void CloseBuyPosition(datetime bar_time)
{
    if(open_position.position_type != 1 || open_position.ticket == 0) return;

    // Pozisyonu seç ve kontrol et
    if(!PositionSelectByTicket(open_position.ticket))
    {
        Print("⚠️ BUY pozisyonu bulunamadı - Ticket: ", open_position.ticket);
        ResetPosition();
        return;
    }

    // Pozisyonu kapat
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = PositionGetDouble(POSITION_VOLUME);
    request.type = ORDER_TYPE_SELL;
    request.price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    request.magic = Magic_Number;
    request.comment = "MA Cross BUY Close";
    request.deviation = 10;
    request.position = open_position.ticket;
    request.type_filling = GetFillingMode(); // Doğru filling mode

    if(OrderSend(request, result))
    {
        if(result.retcode == TRADE_RETCODE_DONE)
        {
            // Kar hesapla
            double profit_pips = (result.price - open_position.entry_price) / _Point;
            if(_Digits == 5 || _Digits == 3) profit_pips /= 10;

            debug_log_number++;
            Print("🔵 [", debug_log_number, "] BUY POZİSYONU KAPATILDI! Ticket: ", open_position.ticket,
                  " Giriş: ", open_position.entry_price, " Çıkış: ", result.price,
                  " Kar: ", DoubleToString(profit_pips, 1), " pip");

            // Alert gönder
            string exit_message = StringFormat("BUY POZİSYON KAPATILDI - Kar: %.1f pip", profit_pips);
            SendAlert(exit_message, result.price, bar_time);

            // Pozisyon bilgilerini sıfırla
            ResetPosition();
        }
        else
        {
            Print("❌ BUY pozisyonu kapatılamadı! Hata: ", result.retcode, " - ", result.comment);
        }
    }
    else
    {
        Print("❌ BUY kapatma emri gönderilemedi!");
    }
}

//+------------------------------------------------------------------+
//| SELL pozisyonu kapat                                             |
//+------------------------------------------------------------------+
void CloseSellPosition(datetime bar_time)
{
    if(open_position.position_type != -1 || open_position.ticket == 0) return;

    // Pozisyonu seç ve kontrol et
    if(!PositionSelectByTicket(open_position.ticket))
    {
        Print("⚠️ SELL pozisyonu bulunamadı - Ticket: ", open_position.ticket);
        ResetPosition();
        return;
    }

    // Pozisyonu kapat
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = PositionGetDouble(POSITION_VOLUME);
    request.type = ORDER_TYPE_BUY;
    request.price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    request.magic = Magic_Number;
    request.comment = "MA Cross SELL Close";
    request.deviation = 10;
    request.position = open_position.ticket;
    request.type_filling = GetFillingMode(); // Doğru filling mode

    if(OrderSend(request, result))
    {
        if(result.retcode == TRADE_RETCODE_DONE)
        {
            // Kar hesapla
            double profit_pips = (open_position.entry_price - result.price) / _Point;
            if(_Digits == 5 || _Digits == 3) profit_pips /= 10;

            debug_log_number++;
            Print("🟣 [", debug_log_number, "] SELL POZİSYONU KAPATILDI! Ticket: ", open_position.ticket,
                  " Giriş: ", open_position.entry_price, " Çıkış: ", result.price,
                  " Kar: ", DoubleToString(profit_pips, 1), " pip");

            // Alert gönder
            string exit_message = StringFormat("SELL POZİSYON KAPATILDI - Kar: %.1f pip", profit_pips);
            SendAlert(exit_message, result.price, bar_time);

            // Pozisyon bilgilerini sıfırla
            ResetPosition();
        }
        else
        {
            Print("❌ SELL pozisyonu kapatılamadı! Hata: ", result.retcode, " - ", result.comment);
        }
    }
    else
    {
        Print("❌ SELL kapatma emri gönderilemedi!");
    }
}

//+------------------------------------------------------------------+
//| Pozisyon bilgilerini sıfırla                                    |
//+------------------------------------------------------------------+
void ResetPosition()
{
    open_position.position_type = 0;
    open_position.entry_bar = 0;
    open_position.entry_price = 0;
    open_position.exit_ready = false;
    open_position.ticket = 0;

    // Bekleyen sinyal sistemini de sıfırla
    pending_signal.signal_type = 0;
    pending_signal.signal_bar = 0;
    pending_signal.bars_waiting = 0;
    pending_signal.signal_price = 0;
    pending_signal.signal_high = 0;
    pending_signal.signal_low = 0;

    debug_log_number++;
    Print("🔄 [", debug_log_number, "] Sistem sıfırlandı, yeni sinyaller için hazır");
}

//+------------------------------------------------------------------+
//| Lot büyüklüğü hesapla                                           |
//+------------------------------------------------------------------+
double CalculateLotSize(double atr_value)
{
    if(Use_Fixed_Lot)
        return Lot_Size;

    // Risk yönetimi ile lot hesapla
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * (Risk_Percent / 100.0);

    double sl_distance = atr_value * ATR_Multiplier;
    double pip_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);

    if(_Digits == 5 || _Digits == 3)
        pip_value *= 10;

    double lot_size = risk_amount / (sl_distance / _Point * pip_value);

    // Minimum ve maksimum lot kontrolü
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot_size = MathMax(lot_size, min_lot);
    lot_size = MathMin(lot_size, max_lot);
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

    return lot_size;
}

//+------------------------------------------------------------------+
//| Alert gönderme fonksiyonu                                        |
//+------------------------------------------------------------------+
void SendAlert(string signal_type, double price, datetime time)
{
    if(!Enable_Alerts) return;

    string message = StringFormat("%s - %s: %.5f (%s)",
                                signal_type, _Symbol, price, TimeToString(time));

    if(Enable_Popup) Alert(message);
    if(Enable_Sound) PlaySound("alert.wav");
    if(Enable_Email) SendMail("XAUUSD MA Crossover EA", message);
    if(Enable_Push) SendNotification(message);

    Print("🔔 ", message);
}

//+------------------------------------------------------------------+
//| Doğru filling mode'u al                                         |
//+------------------------------------------------------------------+
ENUM_ORDER_TYPE_FILLING GetFillingMode()
{
    // Sembol için desteklenen filling mode'ları kontrol et
    int filling_mode = (int)SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE);

    // FOK (Fill or Kill) destekleniyorsa
    if((filling_mode & SYMBOL_FILLING_FOK) == SYMBOL_FILLING_FOK)
        return ORDER_FILLING_FOK;

    // IOC (Immediate or Cancel) destekleniyorsa
    if((filling_mode & SYMBOL_FILLING_IOC) == SYMBOL_FILLING_IOC)
        return ORDER_FILLING_IOC;

    // Return (varsayılan) destekleniyorsa
    return ORDER_FILLING_RETURN;
}

//+------------------------------------------------------------------+
//| Pozisyon seçme fonksiyonu                                       |
//+------------------------------------------------------------------+
bool SelectPosition()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetTicket(i) > 0)
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
               PositionGetInteger(POSITION_MAGIC) == Magic_Number)
            {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Pozisyon durumu kontrol et                                      |
//+------------------------------------------------------------------+
void CheckPositionStatus()
{
    // Açık pozisyon takibimiz var ama gerçekte pozisyon kapalıysa sıfırla
    if(open_position.position_type != 0)
    {
        if(!SelectPosition())
        {
            Print("⚠️ Pozisyon takibi senkronize edildi - Pozisyon kapalı");
            ResetPosition();
        }
    }
}
