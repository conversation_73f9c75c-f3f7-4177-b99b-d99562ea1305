//+------------------------------------------------------------------+
//|                                    XAUUSD_MA_Crossover_EA.mq5 |
//|                                                    Emre <PERSON>un |
//|                                                                      |
//+------------------------------------------------------------------+
#property copyright "Emre Usun"
#property link      ""
#property version   "1.00"

//--- Input parameters
input group "=== MA Ayarları ==="
input int                   MA_Period_Long = 50;                    // Uzun MA Periyodu (MA 50)
input int                   MA_Period_Mid = 14;                     // Orta MA Periyodu (MA 14)
input int                   MA_Period_Short = 7;                    // Kısa MA Periyodu (MA 7)
input ENUM_MA_METHOD        MA_Method = MODE_SMA;                   // MA Hesaplama Metodu
input ENUM_APPLIED_PRICE    MA_Applied_Price = PRICE_CLOSE;         // MA Uygulanan Fiyat

input group "=== Sinyal A<PERSON>ları ==="
input int                   Confirmation_Bars = 5;                  // MA50 Onay Bekleme Bar Sayısı
input double                Min_Cross_Distance = 0.05;              // Minimum Kesişim Mesafesi (pip)
input double                MA50_Safety_Points = 10.0;              // MA50'den güvenlik mesafesi (point)

input group "=== RSI Çıkış Ayarları ==="
input int                   RSI_Period = 14;                       // RSI Periyodu

input group "=== BUY Pozisyon RSI Çıkış ==="
input double                RSI_Buy_Start_Level = 72.0;            // BUY Çıkış Takip Başlangıç Seviyesi
input double                RSI_Buy_Exit_Level = 70.0;             // BUY Çıkış Tetik Seviyesi

input group "=== SELL Pozisyon RSI Çıkış ==="
input double                RSI_Sell_Start_Level = 28.0;           // SELL Çıkış Takip Başlangıç Seviyesi
input double                RSI_Sell_Exit_Level = 30.0;            // SELL Çıkış Tetik Seviyesi

input group "=== Risk Yönetimi ==="
input double                Lot_Size = 0.01;                       // Lot Büyüklüğü
input double                ATR_Multiplier = 2.2;                  // ATR Çarpanı (SL için)
input int                   ATR_Period = 14;                       // ATR Periyodu
input double                Risk_Percent = 2.0;                    // Risk Yüzdesi (% bakiye)
input bool                  Use_Fixed_Lot = true;                  // Sabit Lot Kullan
input int                   Magic_Number = 12345;                  // Magic Number

input group "=== Bildirim Ayarları ==="
input bool                  Enable_Alerts = true;                  // Uyarıları Etkinleştir
input bool                  Enable_Sound = true;                   // Ses Uyarısı
input bool                  Enable_Popup = true;                   // Popup Uyarısı
input bool                  Enable_Email = false;                  // Email Uyarısı
input bool                  Enable_Push = false;                   // Push Bildirimi

//--- Handles
int ma_long_handle;
int ma_mid_handle;
int ma_short_handle;
int rsi_handle;
int atr_handle;

//--- Buffers
double MA_Long_Buffer[];
double MA_Mid_Buffer[];
double MA_Short_Buffer[];

//--- Global variables
datetime last_alert_time = 0;
int debug_log_number = 0;

//--- Bekleyen sinyal yapısı
struct PendingSignal
{
    int signal_type;        // 1=BUY, -1=SELL, 0=YOK
    int signal_bar;         // Sinyalin oluştuğu bar index'i
    int bars_waiting;       // Kaç bardır bekliyor
    double signal_price;    // Sinyal fiyatı
    double signal_high;     // Sinyal barının high'ı
    double signal_low;      // Sinyal barının low'ı
};

PendingSignal pending_signal;

//--- Açık pozisyon takibi
struct OpenPosition
{
    int position_type;      // 1=BUY, -1=SELL, 0=YOK
    int entry_bar;          // Giriş barı
    double entry_price;     // Giriş fiyatı
    bool exit_ready;        // Çıkış hazır mı? (RSI seviyeyi geçti mi?)
    ulong ticket;           // Pozisyon ticket'ı
};

OpenPosition open_position;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Handle'ları oluştur
    ma_long_handle = iMA(_Symbol, _Period, MA_Period_Long, 0, MA_Method, MA_Applied_Price);
    ma_mid_handle = iMA(_Symbol, _Period, MA_Period_Mid, 0, MA_Method, MA_Applied_Price);
    ma_short_handle = iMA(_Symbol, _Period, MA_Period_Short, 0, MA_Method, MA_Applied_Price);
    rsi_handle = iRSI(_Symbol, _Period, RSI_Period, PRICE_CLOSE);
    atr_handle = iATR(_Symbol, _Period, ATR_Period);

    // Handle kontrolü
    if(ma_long_handle == INVALID_HANDLE || ma_mid_handle == INVALID_HANDLE ||
       ma_short_handle == INVALID_HANDLE || rsi_handle == INVALID_HANDLE ||
       atr_handle == INVALID_HANDLE)
    {
        Print("❌ EA handle'ları oluşturulamadı!");
        return INIT_FAILED;
    }

    // Buffer'ları time series olarak hazırla (Bar 0 = en son)
    ArraySetAsSeries(MA_Long_Buffer, true);
    ArraySetAsSeries(MA_Mid_Buffer, true);
    ArraySetAsSeries(MA_Short_Buffer, true);

    // Yapıları başlat
    pending_signal.signal_type = 0;
    pending_signal.signal_bar = 0;
    pending_signal.bars_waiting = 0;
    debug_log_number = 0;
    
    open_position.position_type = 0;
    open_position.entry_bar = 0;
    open_position.entry_price = 0;
    open_position.exit_ready = false;
    open_position.ticket = 0;

    // Strategy Tester için timer başlat
    EventSetTimer(1); // Her saniye kontrol et

    Print("✅ XAUUSD MA Crossover EA başlatıldı - Magic: ", Magic_Number);
    Print("📊 Strategy Tester modu aktif - Timer başlatıldı");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    EventKillTimer(); // Timer'ı durdur
    Print("🔄 XAUUSD MA Crossover EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Pozisyon durumu kontrol et
    CheckPositionStatus();

    // Yeterli bar var mı kontrol et
    if(Bars(_Symbol, _Period) < MA_Period_Long + 10) return;
    
    // Handle'lar hazır mı kontrol et
    if(BarsCalculated(ma_long_handle) < 10 ||
       BarsCalculated(ma_mid_handle) < 10 ||
       BarsCalculated(ma_short_handle) < 10 ||
       BarsCalculated(rsi_handle) < 10 ||
       BarsCalculated(atr_handle) < 10)
        return;

    // Verileri kopyala
    if(CopyBuffer(ma_long_handle, 0, 0, 10, MA_Long_Buffer) <= 0) return;
    if(CopyBuffer(ma_mid_handle, 0, 0, 10, MA_Mid_Buffer) <= 0) return;
    if(CopyBuffer(ma_short_handle, 0, 0, 10, MA_Short_Buffer) <= 0) return;

    // RSI ve ATR değerleri
    double rsi_values[];
    double atr_values[];
    ArraySetAsSeries(rsi_values, true);  // Time series
    ArraySetAsSeries(atr_values, true);  // Time series
    
    if(CopyBuffer(rsi_handle, 0, 0, 10, rsi_values) <= 0) return;
    if(CopyBuffer(atr_handle, 0, 0, 10, atr_values) <= 0) return;

    // Fiyat verileri
    MqlRates rates[];
    ArraySetAsSeries(rates, true);  // Time series
    if(CopyRates(_Symbol, _Period, 0, 10, rates) <= 0) return;

    // Son tamamlanmış bar analizi (Bar 1) - DÜZELTME: Bar 0 kullan
    int current_bar = 0; // En son bar (canlı bar değil, son tamamlanmış)
    AnalyzeSignals(current_bar, rates, rsi_values, atr_values);
}

//+------------------------------------------------------------------+
//| Timer function (Strategy Tester için)                           |
//+------------------------------------------------------------------+
void OnTimer()
{
    // OnTick ile aynı mantık ama timer bazlı
    OnTick();
}

//+------------------------------------------------------------------+
//| Sinyal analiz fonksiyonu                                        |
//+------------------------------------------------------------------+
void AnalyzeSignals(int bar_index, MqlRates &rates[], double &rsi_values[], double &atr_values[])
{
    // MA değerleri - Time Series indexleme (Bar 0 = en son)
    double ma_long_current = MA_Long_Buffer[bar_index];      // En son bar (0)
    double ma_long_prev = MA_Long_Buffer[bar_index+1];       // Önceki bar (1)
    double ma_mid_current = MA_Mid_Buffer[bar_index];        // En son bar (0)
    double ma_short_current = MA_Short_Buffer[bar_index];    // En son bar (0)
    double ma_short_prev = MA_Short_Buffer[bar_index+1];     // Önceki bar (1)

    // Fiyat ve RSI değerleri
    double close_current = rates[bar_index].close;
    double high_current = rates[bar_index].high;
    double low_current = rates[bar_index].low;
    double open_current = rates[bar_index].open;
    double rsi_current = rsi_values[bar_index];
    double atr_current = atr_values[bar_index];

    // Bar debug sadece pending signal varsa
    if(pending_signal.signal_type != 0)
    {
        Print("🔍 BAR DETAY - Bar[", bar_index, "] Open: ", DoubleToString(open_current, 5),
              " High: ", DoubleToString(high_current, 5), " Low: ", DoubleToString(low_current, 5),
              " Close: ", DoubleToString(close_current, 5), " MA50: ", DoubleToString(ma_long_current, 5));
    }

    // 1. ADIM: Yeni MA7-MA14 kesişimi var mı kontrol et
    double ma_mid_prev = MA_Mid_Buffer[bar_index+1]; // MA14 önceki değer (Bar 1)

    // Minimum kesişim mesafesi kontrolü (pip cinsinden)
    double min_distance = Min_Cross_Distance * _Point;
    if(_Digits == 5 || _Digits == 3) min_distance *= 10; // 5 haneli broker için

    // Kesişim koşulları (minimum mesafe ile)
    bool buy_crossover = (ma_short_prev < ma_mid_prev - min_distance) &&
                         (ma_short_current > ma_mid_current + min_distance);
    bool sell_crossover = (ma_short_prev > ma_mid_prev + min_distance) &&
                          (ma_short_current < ma_mid_current - min_distance);

    // Kesişim debug - sadece tamamlanmış barlar için
    if(bar_index > 0 && (buy_crossover || sell_crossover))
    {
        Print("🔍 KESİŞİM DETAY ANALIZ - Bar[", bar_index, "]:");
        Print("   MA7[", bar_index+1, "] (önceki) = ", DoubleToString(ma_short_prev, 6));
        Print("   MA7[", bar_index, "] (şimdi) = ", DoubleToString(ma_short_current, 6));
        Print("   MA14[", bar_index+1, "] (önceki) = ", DoubleToString(ma_mid_prev, 6));
        Print("   MA14[", bar_index, "] (şimdi) = ", DoubleToString(ma_mid_current, 6));
        Print("   Fark[önceki] = ", DoubleToString(ma_short_prev - ma_mid_prev, 6));
        Print("   Fark[şimdi] = ", DoubleToString(ma_short_current - ma_mid_current, 6));
        Print("   Min Mesafe: ", DoubleToString(min_distance, 6), " (", Min_Cross_Distance, " pip)");
        Print("   BUY Koşul: (", DoubleToString(ma_short_prev, 6), " < ", DoubleToString(ma_mid_prev - min_distance, 6), ") = ", (ma_short_prev < ma_mid_prev - min_distance),
              " VE (", DoubleToString(ma_short_current, 6), " > ", DoubleToString(ma_mid_current + min_distance, 6), ") = ", (ma_short_current > ma_mid_current + min_distance));
        Print("   SELL Koşul: (", DoubleToString(ma_short_prev, 6), " > ", DoubleToString(ma_mid_prev + min_distance, 6), ") = ", (ma_short_prev > ma_mid_prev + min_distance),
              " VE (", DoubleToString(ma_short_current, 6), " < ", DoubleToString(ma_mid_current - min_distance, 6), ") = ", (ma_short_current < ma_mid_current - min_distance));
        Print("   SONUÇ: BUY=", buy_crossover, " SELL=", sell_crossover);
    }

    // MA değerleri debug (sadece tamamlanmış barlar için)
    static datetime last_ma_debug = 0;
    if(bar_index > 0 && rates[bar_index].time != last_ma_debug)
    {
        Print("📈 MA Debug - Bar[", bar_index, "] MA7: ", DoubleToString(ma_short_current, 6),
              " MA14: ", DoubleToString(ma_mid_current, 6), " MA50: ", DoubleToString(ma_long_current, 6),
              " Fark(MA7-MA14): ", DoubleToString(ma_short_current - ma_mid_current, 6));
        last_ma_debug = rates[bar_index].time;
    }

    // Durum raporu sadece önemli durumlarda
    static datetime last_report_time = 0;
    static int last_pending = 0;
    static int last_position = 0;

    // Sadece durum değiştiğinde veya 30 dakikada bir rapor
    if(pending_signal.signal_type != last_pending || open_position.position_type != last_position ||
       TimeCurrent() - last_report_time > 1800) // 30 dakikada bir
    {
        last_report_time = TimeCurrent();
        Print("📊 EA Durum Raporu - Pending: ", pending_signal.signal_type,
              " OpenPos: ", open_position.position_type, " RSI: ", DoubleToString(rsi_current, 1));
        last_pending = pending_signal.signal_type;
        last_position = open_position.position_type;
    }

    // 2. ADIM: Yeni kesişim varsa bekleyen sinyal oluştur (sadece yeni bar açıldığında)
    if(bar_index > 0) // Sadece tamamlanmış barlar için kesişim kontrolü
    {
        ProcessCrossoverSignals(buy_crossover, sell_crossover, bar_index, close_current, high_current, low_current);
    }

    // 3. ADIM: Bekleyen sinyal varsa onay kontrolü yap
    ProcessPendingSignals(bar_index, ma_long_current, close_current, high_current, low_current, atr_current, rates[bar_index].time);

    // 4. ADIM: Açık pozisyon varsa çıkış kontrolü yap
    ProcessExitSignals(bar_index, rsi_current, close_current, high_current, low_current, rates[bar_index].time);
}

//+------------------------------------------------------------------+
//| Kesişim sinyallerini işle                                       |
//+------------------------------------------------------------------+
void ProcessCrossoverSignals(bool buy_crossover, bool sell_crossover, int bar_index, double close_price, double high_price, double low_price)
{
    // BUY KESİŞİMİ KONTROLÜ
    if(buy_crossover)
    {
        if(pending_signal.signal_type != 0)
        {
            // Eski bekleyen sinyali iptal et
            debug_log_number++;
            if(pending_signal.signal_type == -1)
                Print("🔄 [", debug_log_number, "] SELL Sinyal iptal edildi (BUY kesişimi geldi) - Kesişim Bar[", pending_signal.signal_bar, "]");
            else
                Print("🔄 [", debug_log_number, "] BUY Sinyal iptal edildi (yeni BUY kesişimi geldi) - Kesişim Bar[", pending_signal.signal_bar, "]");
        }
        
        // Yeni BUY sinyali başlat
        pending_signal.signal_type = 1;
        pending_signal.signal_bar = bar_index;
        pending_signal.bars_waiting = 0;
        pending_signal.signal_price = close_price;
        pending_signal.signal_high = high_price;
        pending_signal.signal_low = low_price;

        debug_log_number++;
        Print("⏳ [", debug_log_number, "] BUY Sinyal beklemede - Bar[", bar_index, "]");
    }

    // SELL KESİŞİMİ KONTROLÜ
    if(sell_crossover)
    {
        if(pending_signal.signal_type != 0)
        {
            // Eski bekleyen sinyali iptal et
            debug_log_number++;
            if(pending_signal.signal_type == 1)
                Print("🔄 [", debug_log_number, "] BUY Sinyal iptal edildi (SELL kesişimi geldi) - Kesişim Bar[", pending_signal.signal_bar, "]");
            else
                Print("🔄 [", debug_log_number, "] SELL Sinyal iptal edildi (yeni SELL kesişimi geldi) - Kesişim Bar[", pending_signal.signal_bar, "]");
        }
        
        // Yeni SELL sinyali başlat
        pending_signal.signal_type = -1;
        pending_signal.signal_bar = bar_index;
        pending_signal.bars_waiting = 0;
        pending_signal.signal_price = close_price;
        pending_signal.signal_high = high_price;
        pending_signal.signal_low = low_price;

        debug_log_number++;
        Print("⏳ [", debug_log_number, "] SELL Sinyal beklemede - Bar[", bar_index, "]");
    }
}

//+------------------------------------------------------------------+
//| Bekleyen sinyalleri işle                                        |
//+------------------------------------------------------------------+
void ProcessPendingSignals(int bar_index, double ma_long_current, double close_price, double high_price, double low_price, double atr_current, datetime bar_time)
{
    if(pending_signal.signal_type == 0) return; // Bekleyen sinyal yok

    pending_signal.bars_waiting = bar_index - pending_signal.signal_bar;

    // BUY sinyali onay kontrolü
    if(pending_signal.signal_type == 1)
    {
        // BUY onayı: Sadece tamamlanmış bar için kontrol et (bar_index > 0)
        if(bar_index > 0) // Sadece tamamlanmış barlar için onay
        {
            double ma50_buy_level = ma_long_current + (MA50_Safety_Points * _Point);
            // Bar MA50 + güvenlik mesafesi üzerinde kapanmalı
            if(close_price > ma50_buy_level) // Close fiyatı güvenlik seviyesi üzerinde
            {
                debug_log_number++;
                Print("✅ [", debug_log_number, "] BUY Sinyal onaylandı! Kesişim Bar[", pending_signal.signal_bar,
                      "] Onay Bar[", bar_index, "] Bekleme: ", pending_signal.bars_waiting, " bar",
                      " Close: ", DoubleToString(close_price, 5), " > MA50+", MA50_Safety_Points, "pt: ", DoubleToString(ma50_buy_level, 5),
                      " (Bar kapandı)");

                // BUY emri aç
                OpenBuyOrder(close_price, atr_current, bar_time);

                // Bekleyen sinyali temizle
                pending_signal.signal_type = 0;
            }
        }
        else if(pending_signal.bars_waiting >= Confirmation_Bars) // Süre doldu
        {
            double ma50_buy_level = ma_long_current + (MA50_Safety_Points * _Point);
            debug_log_number++;
            Print("❌ [", debug_log_number, "] BUY Sinyal iptal - Onay gelmedi. Bekleme: ", pending_signal.bars_waiting,
                  " bar, Kesişim Bar[", pending_signal.signal_bar, "] Son Close: ", DoubleToString(close_price, 5),
                  " MA50+", MA50_Safety_Points, "pt: ", DoubleToString(ma50_buy_level, 5), " (Bar kapanış bekleniyor)");
            pending_signal.signal_type = 0;
        }
        // Onay beklerken debug kaldırıldı (çok fazla log)
    }
    // SELL sinyali onay kontrolü
    else if(pending_signal.signal_type == -1)
    {
        // SELL onayı: Sadece tamamlanmış bar için kontrol et (bar_index > 0)
        if(bar_index > 0) // Sadece tamamlanmış barlar için onay
        {
            double ma50_sell_level = ma_long_current - (MA50_Safety_Points * _Point);
            // Bar MA50 - güvenlik mesafesi altında kapanmalı
            if(close_price < ma50_sell_level) // Close fiyatı güvenlik seviyesi altında
            {
                debug_log_number++;
                Print("✅ [", debug_log_number, "] SELL Sinyal onaylandı! Kesişim Bar[", pending_signal.signal_bar,
                      "] Onay Bar[", bar_index, "] Bekleme: ", pending_signal.bars_waiting, " bar",
                      " Close: ", DoubleToString(close_price, 5), " < MA50-", MA50_Safety_Points, "pt: ", DoubleToString(ma50_sell_level, 5),
                      " (Bar kapandı)");

                // SELL emri aç
                OpenSellOrder(close_price, atr_current, bar_time);

                // Bekleyen sinyali temizle
                pending_signal.signal_type = 0;
            }
        }
        else if(pending_signal.bars_waiting >= Confirmation_Bars) // Süre doldu
        {
            double ma50_sell_level = ma_long_current - (MA50_Safety_Points * _Point);
            debug_log_number++;
            Print("❌ [", debug_log_number, "] SELL Sinyal iptal - Onay gelmedi. Bekleme: ", pending_signal.bars_waiting,
                  " bar, Kesişim Bar[", pending_signal.signal_bar, "] Son Close: ", DoubleToString(close_price, 5),
                  " MA50-", MA50_Safety_Points, "pt: ", DoubleToString(ma50_sell_level, 5), " (Bar kapanış bekleniyor)");
            pending_signal.signal_type = 0;
        }
        // Onay beklerken debug kaldırıldı (çok fazla log)
    }
}

//+------------------------------------------------------------------+
//| Çıkış sinyallerini işle                                         |
//+------------------------------------------------------------------+
void ProcessExitSignals(int bar_index, double rsi_current, double close_price, double high_price, double low_price, datetime bar_time)
{
    if(open_position.position_type == 0) return; // Açık pozisyon yok

    // RSI debug sadece pozisyon varken ve önemli değişimlerde
    static double last_rsi = 0;
    if(open_position.position_type != 0 && MathAbs(rsi_current - last_rsi) > 2.0) // Sadece pozisyon varken ve 2+ puan değişimde
    {
        string position_info = "";
        if(open_position.position_type == 1)
            position_info = StringFormat("BUY (Takip: %.1f→%.1f)", RSI_Buy_Start_Level, RSI_Buy_Exit_Level);
        else if(open_position.position_type == -1)
            position_info = StringFormat("SELL (Takip: %.1f→%.1f)", RSI_Sell_Start_Level, RSI_Sell_Exit_Level);

        Print("📈 RSI Takip - Değer: ", DoubleToString(rsi_current, 1),
              " Pozisyon: ", position_info,
              " ExitReady: ", open_position.exit_ready);
        last_rsi = rsi_current;
    }

    // BUY pozisyonu için RSI çıkış kontrolü
    if(open_position.position_type == 1)
    {
        if(!open_position.exit_ready && rsi_current >= RSI_Buy_Start_Level)
        {
            // 1. AŞAMA: RSI başlangıç seviyesine ulaştı - Çıkış takibi başlat
            open_position.exit_ready = true;
            debug_log_number++;
            Print("⚠️ [", debug_log_number, "] BUY Çıkış Takibi Başladı - Bar[", bar_index, "] RSI ", DoubleToString(rsi_current, 1),
                  " seviyesi ", RSI_Buy_Start_Level, " üzerine çıktı (Takip: ", RSI_Buy_Start_Level, "→", RSI_Buy_Exit_Level, ")");
        }
        else if(open_position.exit_ready && rsi_current <= RSI_Buy_Exit_Level)
        {
            // 2. AŞAMA: RSI çıkış seviyesine indi - ŞİMDİ ÇIK!
            debug_log_number++;
            Print("🔵 [", debug_log_number, "] BUY Çıkış Tamamlandı! Bar[", bar_index, "] RSI ", DoubleToString(rsi_current, 1),
                  " seviyesi ", RSI_Buy_Exit_Level, " seviyesine indi (", RSI_Buy_Start_Level, "→", RSI_Buy_Exit_Level, ")");

            // Pozisyonu kapat
            CloseBuyPosition(bar_time);
        }
    }
    // SELL pozisyonu için RSI çıkış kontrolü
    else if(open_position.position_type == -1)
    {
        if(!open_position.exit_ready && rsi_current <= RSI_Sell_Start_Level)
        {
            // 1. AŞAMA: RSI başlangıç seviyesine ulaştı - Çıkış takibi başlat
            open_position.exit_ready = true;
            debug_log_number++;
            Print("⚠️ [", debug_log_number, "] SELL Çıkış Takibi Başladı - Bar[", bar_index, "] RSI ", DoubleToString(rsi_current, 1),
                  " seviyesi ", RSI_Sell_Start_Level, " altına indi (Takip: ", RSI_Sell_Start_Level, "→", RSI_Sell_Exit_Level, ")");
        }
        else if(open_position.exit_ready && rsi_current >= RSI_Sell_Exit_Level)
        {
            // 2. AŞAMA: RSI çıkış seviyesine çıktı - ŞİMDİ ÇIK!
            debug_log_number++;
            Print("🟣 [", debug_log_number, "] SELL Çıkış Tamamlandı! Bar[", bar_index, "] RSI ", DoubleToString(rsi_current, 1),
                  " seviyesi ", RSI_Sell_Exit_Level, " seviyesine çıktı (", RSI_Sell_Start_Level, "→", RSI_Sell_Exit_Level, ")");

            // Pozisyonu kapat
            CloseSellPosition(bar_time);
        }
    }
}

//+------------------------------------------------------------------+
//| BUY emri aç                                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder(double price, double atr_value, datetime bar_time)
{
    // Zaten açık pozisyon varsa çık
    if(open_position.position_type != 0) return;

    // Lot büyüklüğünü hesapla
    double lot_size = CalculateLotSize(atr_value);

    // Sadece SL hesapla (TP yok - RSI ile çıkış)
    double sl_distance = atr_value * ATR_Multiplier;
    double sl_price = sl_distance;

    // Emir gönder
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lot_size;
    request.type = ORDER_TYPE_BUY;
    request.price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    request.sl = price - sl_price; // SL = Giriş - ATR mesafesi
    request.tp = 0; // TP yok - RSI ile çıkış
    request.magic = Magic_Number;
    request.comment = "MA Cross BUY (RSI Exit)";
    request.deviation = 10;
    request.type_filling = GetFillingMode(); // Doğru filling mode

    if(OrderSend(request, result))
    {
        if(result.retcode == TRADE_RETCODE_DONE)
        {
            // Pozisyon bilgilerini kaydet
            open_position.position_type = 1;
            open_position.entry_bar = 0; // Canlı bar
            open_position.entry_price = result.price;
            open_position.exit_ready = false;
            open_position.ticket = result.order;

            debug_log_number++;
            Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI! Ticket: ", result.order,
                  " Fiyat: ", result.price, " SL: ", price - sl_price, " TP: RSI Çıkış",
                  " Lot: ", lot_size, " ATR SL: ", DoubleToString(sl_price/_Point, 1), " pip");

            // Alert gönder
            SendAlert("BUY EMRİ AÇILDI", result.price, bar_time);
        }
        else
        {
            Print("❌ BUY emri açılamadı! Hata: ", result.retcode, " - ", result.comment);
        }
    }
    else
    {
        Print("❌ BUY emri gönderilemedi!");
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder(double price, double atr_value, datetime bar_time)
{
    // Zaten açık pozisyon varsa çık
    if(open_position.position_type != 0) return;

    // Lot büyüklüğünü hesapla
    double lot_size = CalculateLotSize(atr_value);

    // Sadece SL hesapla (TP yok - RSI ile çıkış)
    double sl_distance = atr_value * ATR_Multiplier;
    double sl_price = sl_distance;

    // Emir gönder
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lot_size;
    request.type = ORDER_TYPE_SELL;
    request.price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    request.sl = price + sl_price; // SL = Giriş + ATR mesafesi
    request.tp = 0; // TP yok - RSI ile çıkış
    request.magic = Magic_Number;
    request.comment = "MA Cross SELL (RSI Exit)";
    request.deviation = 10;
    request.type_filling = GetFillingMode(); // Doğru filling mode

    if(OrderSend(request, result))
    {
        if(result.retcode == TRADE_RETCODE_DONE)
        {
            // Pozisyon bilgilerini kaydet
            open_position.position_type = -1;
            open_position.entry_bar = 0; // Canlı bar
            open_position.entry_price = result.price;
            open_position.exit_ready = false;
            open_position.ticket = result.order;

            debug_log_number++;
            Print("🔴 [", debug_log_number, "] SELL EMRİ AÇILDI! Ticket: ", result.order,
                  " Fiyat: ", result.price, " SL: ", price + sl_price, " TP: RSI Çıkış",
                  " Lot: ", lot_size, " ATR SL: ", DoubleToString(sl_price/_Point, 1), " pip");

            // Alert gönder
            SendAlert("SELL EMRİ AÇILDI", result.price, bar_time);
        }
        else
        {
            Print("❌ SELL emri açılamadı! Hata: ", result.retcode, " - ", result.comment);
        }
    }
    else
    {
        Print("❌ SELL emri gönderilemedi!");
    }
}

//+------------------------------------------------------------------+
//| BUY pozisyonu kapat                                              |
//+------------------------------------------------------------------+
void CloseBuyPosition(datetime bar_time)
{
    if(open_position.position_type != 1 || open_position.ticket == 0) return;

    // Pozisyonu seç ve kontrol et
    if(!PositionSelectByTicket(open_position.ticket))
    {
        Print("⚠️ BUY pozisyonu bulunamadı - Ticket: ", open_position.ticket);
        ResetPosition();
        return;
    }

    // Pozisyonu kapat
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = PositionGetDouble(POSITION_VOLUME);
    request.type = ORDER_TYPE_SELL;
    request.price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    request.magic = Magic_Number;
    request.comment = "MA Cross BUY Close";
    request.deviation = 10;
    request.position = open_position.ticket;
    request.type_filling = GetFillingMode(); // Doğru filling mode

    if(OrderSend(request, result))
    {
        if(result.retcode == TRADE_RETCODE_DONE)
        {
            // Kar hesapla
            double profit_pips = (result.price - open_position.entry_price) / _Point;
            if(_Digits == 5 || _Digits == 3) profit_pips /= 10;

            debug_log_number++;
            Print("🔵 [", debug_log_number, "] BUY POZİSYONU KAPATILDI! Ticket: ", open_position.ticket,
                  " Giriş: ", open_position.entry_price, " Çıkış: ", result.price,
                  " Kar: ", DoubleToString(profit_pips, 1), " pip");

            // Alert gönder
            string exit_message = StringFormat("BUY POZİSYON KAPATILDI - Kar: %.1f pip", profit_pips);
            SendAlert(exit_message, result.price, bar_time);

            // Pozisyon bilgilerini sıfırla
            ResetPosition();
        }
        else
        {
            Print("❌ BUY pozisyonu kapatılamadı! Hata: ", result.retcode, " - ", result.comment);
        }
    }
    else
    {
        Print("❌ BUY kapatma emri gönderilemedi!");
    }
}

//+------------------------------------------------------------------+
//| SELL pozisyonu kapat                                             |
//+------------------------------------------------------------------+
void CloseSellPosition(datetime bar_time)
{
    if(open_position.position_type != -1 || open_position.ticket == 0) return;

    // Pozisyonu seç ve kontrol et
    if(!PositionSelectByTicket(open_position.ticket))
    {
        Print("⚠️ SELL pozisyonu bulunamadı - Ticket: ", open_position.ticket);
        ResetPosition();
        return;
    }

    // Pozisyonu kapat
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = PositionGetDouble(POSITION_VOLUME);
    request.type = ORDER_TYPE_BUY;
    request.price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    request.magic = Magic_Number;
    request.comment = "MA Cross SELL Close";
    request.deviation = 10;
    request.position = open_position.ticket;
    request.type_filling = GetFillingMode(); // Doğru filling mode

    if(OrderSend(request, result))
    {
        if(result.retcode == TRADE_RETCODE_DONE)
        {
            // Kar hesapla
            double profit_pips = (open_position.entry_price - result.price) / _Point;
            if(_Digits == 5 || _Digits == 3) profit_pips /= 10;

            debug_log_number++;
            Print("🟣 [", debug_log_number, "] SELL POZİSYONU KAPATILDI! Ticket: ", open_position.ticket,
                  " Giriş: ", open_position.entry_price, " Çıkış: ", result.price,
                  " Kar: ", DoubleToString(profit_pips, 1), " pip");

            // Alert gönder
            string exit_message = StringFormat("SELL POZİSYON KAPATILDI - Kar: %.1f pip", profit_pips);
            SendAlert(exit_message, result.price, bar_time);

            // Pozisyon bilgilerini sıfırla
            ResetPosition();
        }
        else
        {
            Print("❌ SELL pozisyonu kapatılamadı! Hata: ", result.retcode, " - ", result.comment);
        }
    }
    else
    {
        Print("❌ SELL kapatma emri gönderilemedi!");
    }
}

//+------------------------------------------------------------------+
//| Pozisyon bilgilerini sıfırla                                    |
//+------------------------------------------------------------------+
void ResetPosition()
{
    open_position.position_type = 0;
    open_position.entry_bar = 0;
    open_position.entry_price = 0;
    open_position.exit_ready = false;
    open_position.ticket = 0;

    // Bekleyen sinyal sistemini de sıfırla
    pending_signal.signal_type = 0;
    pending_signal.signal_bar = 0;
    pending_signal.bars_waiting = 0;
    pending_signal.signal_price = 0;
    pending_signal.signal_high = 0;
    pending_signal.signal_low = 0;

    debug_log_number++;
    Print("🔄 [", debug_log_number, "] Sistem sıfırlandı, yeni sinyaller için hazır");
}

//+------------------------------------------------------------------+
//| Lot büyüklüğü hesapla                                           |
//+------------------------------------------------------------------+
double CalculateLotSize(double atr_value)
{
    if(Use_Fixed_Lot)
        return Lot_Size;

    // Risk yönetimi ile lot hesapla
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * (Risk_Percent / 100.0);

    double sl_distance = atr_value * ATR_Multiplier;
    double pip_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);

    if(_Digits == 5 || _Digits == 3)
        pip_value *= 10;

    double lot_size = risk_amount / (sl_distance / _Point * pip_value);

    // Minimum ve maksimum lot kontrolü
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot_size = MathMax(lot_size, min_lot);
    lot_size = MathMin(lot_size, max_lot);
    lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

    return lot_size;
}

//+------------------------------------------------------------------+
//| Alert gönderme fonksiyonu                                        |
//+------------------------------------------------------------------+
void SendAlert(string signal_type, double price, datetime time)
{
    if(!Enable_Alerts) return;

    string message = StringFormat("%s - %s: %.5f (%s)",
                                signal_type, _Symbol, price, TimeToString(time));

    if(Enable_Popup) Alert(message);
    if(Enable_Sound) PlaySound("alert.wav");
    if(Enable_Email) SendMail("XAUUSD MA Crossover EA", message);
    if(Enable_Push) SendNotification(message);

    Print("🔔 ", message);
}

//+------------------------------------------------------------------+
//| Doğru filling mode'u al                                         |
//+------------------------------------------------------------------+
ENUM_ORDER_TYPE_FILLING GetFillingMode()
{
    // Sembol için desteklenen filling mode'ları kontrol et
    int filling_mode = (int)SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE);

    // FOK (Fill or Kill) destekleniyorsa
    if((filling_mode & SYMBOL_FILLING_FOK) == SYMBOL_FILLING_FOK)
        return ORDER_FILLING_FOK;

    // IOC (Immediate or Cancel) destekleniyorsa
    if((filling_mode & SYMBOL_FILLING_IOC) == SYMBOL_FILLING_IOC)
        return ORDER_FILLING_IOC;

    // Return (varsayılan) destekleniyorsa
    return ORDER_FILLING_RETURN;
}

//+------------------------------------------------------------------+
//| Pozisyon seçme fonksiyonu                                       |
//+------------------------------------------------------------------+
bool SelectPosition()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetTicket(i) > 0)
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
               PositionGetInteger(POSITION_MAGIC) == Magic_Number)
            {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Pozisyon durumu kontrol et                                      |
//+------------------------------------------------------------------+
void CheckPositionStatus()
{
    // Açık pozisyon takibimiz var ama gerçekte pozisyon kapalıysa sıfırla
    if(open_position.position_type != 0)
    {
        if(!SelectPosition())
        {
            Print("⚠️ Pozisyon takibi senkronize edildi - Pozisyon kapalı");
            ResetPosition();
        }
    }
}
