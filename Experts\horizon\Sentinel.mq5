//+------------------------------------------------------------------+
//| Sentinel EA                                                      |
//| RSI + MA50 bazlı basit işlem sistemi                            |
//+------------------------------------------------------------------+
#property copyright "Horizon Trading"
#property version   "1.00"

#include <Trade\Trade.mqh>
CTrade trade;

//--- Input parametreleri
input group "=== RSI Ayarları ==="
input int                   RSI_Period = 14;                     // RSI Periyodu
input double                RSI_Buy_Level = 70.0;                // BUY için RSI seviyesi (>=)
input double                RSI_Sell_Level = 30.0;               // SELL için RSI seviyesi (<=)

input group "=== MA50 Ayarları ==="
input int                   MA50_Period = 50;                    // MA50 Periyodu
input ENUM_MA_METHOD        MA50_Method = MODE_EMA;              // MA50 Metodu
input ENUM_APPLIED_PRICE    MA50_Applied_Price = PRICE_CLOSE;    // MA50 Uygulanan Fiyat

input group "=== Risk Yönetimi ==="
input double                Lot_Size = 0.01;                     // Lot Büyüklüğü
input double                ATR_Multiplier = 2.0;                // ATR Çarpanı (SL için)
input int                   ATR_Period = 14;                     // ATR Periyodu
input double                Risk_Percent = 2.0;                  // Risk Yüzdesi (% bakiye)

input group "=== Genel Ayarlar ==="
input int                   Magic_Number = 7777;                 // Magic Number
input string                Comment_Prefix = "SENTINEL";         // Yorum Ön Eki
input bool                  Use_ATR_SL = true;                   // ATR bazlı SL kullan
input bool                  Enable_Debug = true;                 // Debug Logları

//--- Global değişkenler
int rsi_handle, ma50_handle, atr_handle;
double rsi_buffer[], ma50_buffer[], atr_buffer[];
int debug_log_number = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Indicator handle'larını oluştur
    rsi_handle = iRSI(_Symbol, _Period, RSI_Period, PRICE_CLOSE);
    ma50_handle = iMA(_Symbol, _Period, MA50_Period, 0, MA50_Method, MA50_Applied_Price);
    atr_handle = iATR(_Symbol, _Period, ATR_Period);
    
    if(rsi_handle == INVALID_HANDLE || ma50_handle == INVALID_HANDLE || atr_handle == INVALID_HANDLE)
    {
        Print("❌ Indicator handle oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Array ayarları
    ArraySetAsSeries(rsi_buffer, true);
    ArraySetAsSeries(ma50_buffer, true);
    ArraySetAsSeries(atr_buffer, true);
    
    // Değişkenleri sıfırla
    debug_log_number = 0;
    
    Print("✅ Sentinel EA başlatıldı - Magic: ", Magic_Number);
    Print("📊 RSI(", RSI_Period, ") + MA50(", MA50_Period, ") sistemi aktif");
    Print("🎯 BUY: RSI>=", RSI_Buy_Level, " & Price>MA50 | Çıkış: Price=MA50");
    Print("🎯 SELL: RSI<=", RSI_Sell_Level, " & Price<MA50 | Çıkış: Price=MA50");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("🔄 Sentinel EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Yeterli bar kontrolü
    if(Bars(_Symbol, _Period) < MathMax(RSI_Period, MA50_Period) + 5)
        return;
    
    // Indicator verilerini al
    if(CopyBuffer(rsi_handle, 0, 0, 2, rsi_buffer) < 2 ||
       CopyBuffer(ma50_handle, 0, 0, 2, ma50_buffer) < 2 ||
       CopyBuffer(atr_handle, 0, 0, 2, atr_buffer) < 2)
    {
        return;
    }
    
    // Mevcut değerler
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double current_rsi = rsi_buffer[0];
    double current_ma50 = ma50_buffer[0];
    double atr_current = atr_buffer[0];
    
    // Mevcut pozisyon kontrolü
    bool has_buy_position = HasPosition(ORDER_TYPE_BUY);
    bool has_sell_position = HasPosition(ORDER_TYPE_SELL);
    
    // Giriş koşulları
    bool buy_condition = (current_rsi >= RSI_Buy_Level) && (current_price > current_ma50);
    bool sell_condition = (current_rsi <= RSI_Sell_Level) && (current_price < current_ma50);
    
    // Çıkış koşulları (fiyat MA50'ye değdiğinde)
    bool buy_exit_condition = has_buy_position && (current_price <= current_ma50);
    bool sell_exit_condition = has_sell_position && (current_price >= current_ma50);
    
    // Debug - sadece sinyal olduğunda
    if(buy_condition || sell_condition || buy_exit_condition || sell_exit_condition)
    {
        debug_log_number++;
        Print("🔍 [", debug_log_number, "] SENTINEL SİNYALİ:");
        Print("   Fiyat: ", DoubleToString(current_price, 5), " MA50: ", DoubleToString(current_ma50, 5));
        Print("   RSI: ", DoubleToString(current_rsi, 2), " (BUY>=", RSI_Buy_Level, " SELL<=", RSI_Sell_Level, ")");
        Print("   Fiyat vs MA50: ", (current_price > current_ma50 ? "ÜZERINDE" : "ALTINDA"));
        Print("   Giriş - BUY: ", buy_condition, " SELL: ", sell_condition);
        Print("   Çıkış - BUY Exit: ", buy_exit_condition, " SELL Exit: ", sell_exit_condition);
        Print("   Pozisyon - BUY: ", has_buy_position, " SELL: ", has_sell_position);
    }
    
    // Çıkış sinyalleri (önce çıkış kontrol et)
    if(buy_exit_condition)
    {
        CloseAllPositions(ORDER_TYPE_BUY);
        debug_log_number++;
        Print("🔵 [", debug_log_number, "] BUY pozisyonu kapatıldı (Fiyat MA50'ye değdi)");
        Print("   Çıkış Fiyatı: ", DoubleToString(current_price, 5), " MA50: ", DoubleToString(current_ma50, 5));
    }
    
    if(sell_exit_condition)
    {
        CloseAllPositions(ORDER_TYPE_SELL);
        debug_log_number++;
        Print("🔵 [", debug_log_number, "] SELL pozisyonu kapatıldı (Fiyat MA50'ye değdi)");
        Print("   Çıkış Fiyatı: ", DoubleToString(current_price, 5), " MA50: ", DoubleToString(current_ma50, 5));
    }
    
    // Giriş sinyalleri
    if(buy_condition && !has_buy_position)
    {
        OpenBuyOrder(atr_current);
    }
    
    if(sell_condition && !has_sell_position)
    {
        OpenSellOrder(atr_current);
    }
}

//+------------------------------------------------------------------+
//| BUY emri aç                                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder(double atr_value)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = 0, tp = 0;
    
    if(Use_ATR_SL)
    {
        sl = ask - (atr_value * ATR_Multiplier);
    }
    
    double lot = CalculateLotSize(sl > 0 ? MathAbs(ask - sl) : atr_value * ATR_Multiplier);
    
    trade.SetExpertMagicNumber(Magic_Number);
    
    if(trade.Buy(lot, _Symbol, ask, sl, tp, Comment_Prefix + "_BUY"))
    {
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(ask, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   RSI: ", DoubleToString(rsi_buffer[0], 2), " MA50: ", DoubleToString(ma50_buffer[0], 5));
        Print("   Koşul: RSI>=", RSI_Buy_Level, " & Price>MA50");
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] BUY EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder(double atr_value)
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = 0, tp = 0;
    
    if(Use_ATR_SL)
    {
        sl = bid + (atr_value * ATR_Multiplier);
    }
    
    double lot = CalculateLotSize(sl > 0 ? MathAbs(bid - sl) : atr_value * ATR_Multiplier);
    
    trade.SetExpertMagicNumber(Magic_Number);
    
    if(trade.Sell(lot, _Symbol, bid, sl, tp, Comment_Prefix + "_SELL"))
    {
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] SELL EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(bid, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   RSI: ", DoubleToString(rsi_buffer[0], 2), " MA50: ", DoubleToString(ma50_buffer[0], 5));
        Print("   Koşul: RSI<=", RSI_Sell_Level, " & Price<MA50");
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] SELL EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| Lot büyüklüğü hesapla                                           |
//+------------------------------------------------------------------+
double CalculateLotSize(double sl_distance)
{
    if(Risk_Percent <= 0) return Lot_Size;
    
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * Risk_Percent / 100.0;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    
    double sl_points = sl_distance / point;
    double sl_ticks = sl_points * tick_size / point;
    double lot = risk_amount / (sl_ticks * tick_value);
    
    // Minimum ve maksimum lot kontrolü
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lot = MathMax(lot, min_lot);
    lot = MathMin(lot, max_lot);
    lot = MathFloor(lot / lot_step) * lot_step;
    
    return lot;
}

//+------------------------------------------------------------------+
//| Pozisyon var mı kontrol et                                      |
//+------------------------------------------------------------------+
bool HasPosition(ENUM_ORDER_TYPE type)
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
                   PositionGetInteger(POSITION_TYPE) == type)
                {
                    return true;
                }
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void CloseAllPositions(ENUM_ORDER_TYPE type)
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
                   PositionGetInteger(POSITION_TYPE) == type)
                {
                    if(!trade.PositionClose(ticket))
                    {
                        debug_log_number++;
                        Print("❌ [", debug_log_number, "] Pozisyon kapatma hatası: ", trade.ResultRetcode());
                    }
                }
            }
        }
    }
}
