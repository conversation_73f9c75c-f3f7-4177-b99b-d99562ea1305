//+------------------------------------------------------------------+
//| SimpleDragPanel.mq5 - Basit ve Garantili Sürüklenebilir Panel  |
//| En basit yöntemle %100 çalışan sürükleme                        |
//+------------------------------------------------------------------+
#property copyright "Horizon Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Basit ve Garantili Sürüklenebilir Panel"

#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| Panel ayarları                                                  |
//+------------------------------------------------------------------+
input int PanelX = 50;              // Panel X pozisyonu
input int PanelY = 50;              // Panel Y pozisyonu

//+------------------------------------------------------------------+
//| Panel boyutları                                                 |
//+------------------------------------------------------------------+
#define PANEL_WIDTH     320
#define PANEL_HEIGHT    250
#define BUTTON_HEIGHT   20
#define ROW_HEIGHT      14
#define MARGIN          6

//+------------------------------------------------------------------+
//| Global değişkenler                                              |
//+------------------------------------------------------------------+
string g_panelName = "SimpleDragPanel";
datetime g_lastUpdate = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    CreatePanel();
    UpdatePanel();
    
    Print("✅ Simple Drag Panel başlatıldı");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    ObjectsDeleteAll(0, g_panelName);
    Print("🔄 Simple Drag Panel kapatıldı");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(TimeCurrent() - g_lastUpdate >= 3)
    {
        UpdatePanel();
        g_lastUpdate = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == g_panelName + "_BtnProfit")
        {
            CloseProfitablePositions();
        }
        else if(sparam == g_panelName + "_BtnLoss")
        {
            CloseLosingPositions();
        }
        else if(sparam == g_panelName + "_BtnAll")
        {
            CloseAllPositions();
        }
    }
    else if(id == CHARTEVENT_OBJECT_DRAG)
    {
        if(StringFind(sparam, g_panelName + "_Background") >= 0)
        {
            // Panel sürüklendiğinde pozisyonu güncelle
            UpdatePanelAfterDrag();
        }
    }
}

//+------------------------------------------------------------------+
//| Panel oluştur                                                   |
//+------------------------------------------------------------------+
void CreatePanel()
{
    // Ana panel arka planı - sürüklenebilir
    ObjectCreate(0, g_panelName + "_Background", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_XDISTANCE, PanelX);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_YDISTANCE, PanelY);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_YSIZE, PANEL_HEIGHT);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BGCOLOR, C'25,25,25');
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_COLOR, C'80,80,80');
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BACK, false);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_SELECTABLE, true);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_SELECTED, false);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_ZORDER, 1000);
    
    // Header
    ObjectCreate(0, g_panelName + "_Header", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XDISTANCE, PanelX);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YDISTANCE, PanelY);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YSIZE, 22);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BGCOLOR, C'0,100,180');
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BACK, false);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_ZORDER, 1001);
    
    CreateButtons();
}

//+------------------------------------------------------------------+
//| Panel sürükleme sonrası güncelle                                |
//+------------------------------------------------------------------+
void UpdatePanelAfterDrag()
{
    // Yeni pozisyonu al
    int newX = (int)ObjectGetInteger(0, g_panelName + "_Background", OBJPROP_XDISTANCE);
    int newY = (int)ObjectGetInteger(0, g_panelName + "_Background", OBJPROP_YDISTANCE);
    
    // Header pozisyonunu güncelle
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XDISTANCE, newX);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YDISTANCE, newY);
    
    // Tüm elementleri yeniden oluştur
    DeleteAllElements();
    CreatePanelAtPosition(newX, newY);
    UpdatePanel();
}

//+------------------------------------------------------------------+
//| Belirli pozisyonda panel oluştur                                |
//+------------------------------------------------------------------+
void CreatePanelAtPosition(int x, int y)
{
    // Ana panel arka planı
    ObjectCreate(0, g_panelName + "_Background", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_YSIZE, PANEL_HEIGHT);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BGCOLOR, C'25,25,25');
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_COLOR, C'80,80,80');
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BACK, false);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_SELECTABLE, true);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_ZORDER, 1000);
    
    // Header
    ObjectCreate(0, g_panelName + "_Header", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YSIZE, 22);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BGCOLOR, C'0,100,180');
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BACK, false);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_ZORDER, 1001);
    
    CreateButtonsAtPosition(x, y);
}

//+------------------------------------------------------------------+
//| Butonları oluştur                                               |
//+------------------------------------------------------------------+
void CreateButtons()
{
    CreateButtonsAtPosition(PanelX, PanelY);
}

//+------------------------------------------------------------------+
//| Belirli pozisyonda butonları oluştur                            |
//+------------------------------------------------------------------+
void CreateButtonsAtPosition(int panelX, int panelY)
{
    int btn_y = panelY + 140;
    int btn_width = (PANEL_WIDTH - 4 * MARGIN) / 3;
    
    // Karlı Kapat butonu
    CreateButton("BtnProfit", "💚 Karlı", panelX + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, C'0,100,0');
    
    // Zarar Kapat butonu
    CreateButton("BtnLoss", "❤️ Zarar", panelX + MARGIN + btn_width + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, C'100,0,0');
    
    // Hepsini Kapat butonu
    CreateButton("BtnAll", "🚫 Hepsini Kapat", panelX + MARGIN, btn_y + BUTTON_HEIGHT + MARGIN, 2 * btn_width + MARGIN, BUTTON_HEIGHT, C'100,100,0');
}

//+------------------------------------------------------------------+
//| Buton oluştur                                                   |
//+------------------------------------------------------------------+
void CreateButton(string name, string text, int x, int y, int width, int height, color bgColor)
{
    string objName = g_panelName + "_" + name;
    
    // Buton arka planı
    ObjectCreate(0, objName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, objName, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, objName, OBJPROP_YSIZE, height);
    ObjectSetInteger(0, objName, OBJPROP_BGCOLOR, bgColor);
    ObjectSetInteger(0, objName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, C'60,60,60');
    ObjectSetInteger(0, objName, OBJPROP_BACK, false);
    ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName, OBJPROP_ZORDER, 1002);
    
    // Buton metni
    ObjectCreate(0, objName + "_Text", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_XDISTANCE, x + width/2);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_YDISTANCE, y + height/2 - 5);
    ObjectSetString(0, objName + "_Text", OBJPROP_TEXT, text);
    ObjectSetString(0, objName + "_Text", OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, objName + "_Text", OBJPROP_FONTSIZE, 8);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_ANCHOR, ANCHOR_CENTER);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_BACK, false);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_ZORDER, 1003);
}

//+------------------------------------------------------------------+
//| Panel güncelle                                                  |
//+------------------------------------------------------------------+
void UpdatePanel()
{
    // Mevcut panel pozisyonunu al
    int currentX = (int)ObjectGetInteger(0, g_panelName + "_Background", OBJPROP_XDISTANCE);
    int currentY = (int)ObjectGetInteger(0, g_panelName + "_Background", OBJPROP_YDISTANCE);
    
    // Panel verilerini hesapla
    int positionCount = 0;
    double totalVolume = 0;
    double totalProfit = 0;
    int profitableCount = 0;
    int losingCount = 0;
    
    for(int i = 0; i < PositionsTotal(); i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;
            
        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);
        
        positionCount++;
        totalVolume += volume;
        totalProfit += profit;
        
        if(profit > 0)
            profitableCount++;
        else if(profit < 0)
            losingCount++;
    }
    
    // Günlük P&L hesapla
    double dailyPL = CalculateDailyPL(totalProfit);
    
    // Panel içeriğini güncelle
    DrawPanelContent(currentX, currentY, positionCount, totalVolume, totalProfit, profitableCount, losingCount, dailyPL);
}
