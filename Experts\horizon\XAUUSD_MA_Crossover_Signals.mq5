//+------------------------------------------------------------------+
//|                                    XAUUSD_MA_Crossover_Signals.mq5 |
//|                                                    Emre Usun |
//|                                                                      |
//+------------------------------------------------------------------+
#property copyright "Emre Usun"
#property link      ""
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 7
#property indicator_plots   7

//--- Plot ayarları
// MA Long (MA 50)
#property indicator_label1  "MA Long"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

// MA Mid (MA 14)
#property indicator_label2  "MA Mid"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrOrange
#property indicator_style2  STYLE_SOLID
#property indicator_width2  1

// MA Short (MA 7)
#property indicator_label3  "MA Short"
#property indicator_type3   DRAW_LINE
#property indicator_color3  clrYellow
#property indicator_style3  STYLE_SOLID
#property indicator_width3  1

// BUY Sinyalleri
#property indicator_label4  "BUY"
#property indicator_type4   DRAW_ARROW
#property indicator_color4  clrYellow
#property indicator_style4  STYLE_SOLID
#property indicator_width4  3

// SELL Sinyalleri
#property indicator_label5  "SELL"
#property indicator_type5   DRAW_ARROW
#property indicator_color5  clrRed
#property indicator_style5  STYLE_SOLID
#property indicator_width5  3

// BUY Çıkış Sinyalleri (TP)
#property indicator_label6  "BUY EXIT"
#property indicator_type6   DRAW_ARROW
#property indicator_color6  clrBlue
#property indicator_style6  STYLE_SOLID
#property indicator_width6  2

// SELL Çıkış Sinyalleri (TP)
#property indicator_label7  "SELL EXIT"
#property indicator_type7   DRAW_ARROW
#property indicator_color7  clrMagenta
#property indicator_style7  STYLE_SOLID
#property indicator_width7  2

//--- Input parameters
input group "=== MA Ayarları ==="
input int                   MA_Period_Long = 50;                    // Uzun MA Periyodu (MA 50)
input int                   MA_Period_Mid = 14;                     // Orta MA Periyodu (MA 14)
input int                   MA_Period_Short = 7;                    // Kısa MA Periyodu (MA 7)
input ENUM_MA_METHOD        MA_Method = MODE_SMA;                   // MA Hesaplama Metodu
input ENUM_APPLIED_PRICE    MA_Applied_Price = PRICE_CLOSE;         // MA Uygulanan Fiyat

input group "=== Sinyal Ayarları ==="
input color                 Buy_Signal_Color = clrLimeGreen;        // Alış Sinyali Rengi
input color                 Sell_Signal_Color = clrRed;             // Satış Sinyali Rengi
input int                   Signal_Arrow_Code_Buy = 233;            // Alış Sinyali Oku (Yukarı Ok)
input int                   Signal_Arrow_Code_Sell = 234;           // Satış Sinyali Oku (Aşağı Ok)
input int                   Arrow_Offset_Pips = 5;                  // Ok Mesafesi (Pip)
input int                   Confirmation_Bars = 5;                  // MA50 Onay Bekleme Bar Sayısı

input group "=== RSI Çıkış Ayarları ==="
input int                   RSI_Period = 14;                       // RSI Periyodu
input double                RSI_Overbought = 70.0;                 // RSI Aşırı Alım Seviyesi
input double                RSI_Oversold = 30.0;                   // RSI Aşırı Satım Seviyesi
input int                   Exit_Arrow_Code_Buy = 251;             // BUY Çıkış Oku (X işareti)
input int                   Exit_Arrow_Code_Sell = 252;            // SELL Çıkış Oku (X işareti)

input group "=== Bildirim Ayarları ==="
input bool                  Enable_Alerts = true;                  // Uyarıları Etkinleştir
input bool                  Enable_Sound = true;                   // Ses Uyarısı
input bool                  Enable_Popup = true;                   // Popup Uyarısı
input bool                  Enable_Email = false;                  // Email Uyarısı
input bool                  Enable_Push = false;                   // Push Bildirimi

//--- Indicator buffers
double MA_Long_Buffer[];
double MA_Mid_Buffer[];
double MA_Short_Buffer[];
double Buy_Signals_Buffer[];
double Sell_Signals_Buffer[];
double Buy_Exit_Buffer[];
double Sell_Exit_Buffer[];

//--- Handles
int ma_long_handle;
int ma_mid_handle;
int ma_short_handle;
int rsi_handle;

//--- Global variables
datetime last_alert_time = 0;

//--- Bekleyen sinyal yapısı
struct PendingSignal
{
    int signal_type;        // 1=BUY, -1=SELL, 0=YOK
    int signal_bar;         // Sinyalin oluştuğu bar index'i
    int bars_waiting;       // Kaç bardır bekliyor
    double signal_price;    // Sinyal fiyatı
    double signal_high;     // Sinyal barının high'ı
    double signal_low;      // Sinyal barının low'ı
};

PendingSignal pending_signal;

//--- Açık pozisyon takibi
struct OpenPosition
{
    int position_type;      // 1=BUY, -1=SELL, 0=YOK
    int entry_bar;          // Giriş barı
    double entry_price;     // Giriş fiyatı
};

OpenPosition open_position;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // Buffer ayarları
    SetIndexBuffer(0, MA_Long_Buffer, INDICATOR_DATA);
    SetIndexBuffer(1, MA_Mid_Buffer, INDICATOR_DATA);
    SetIndexBuffer(2, MA_Short_Buffer, INDICATOR_DATA);
    SetIndexBuffer(3, Buy_Signals_Buffer, INDICATOR_DATA);
    SetIndexBuffer(4, Sell_Signals_Buffer, INDICATOR_DATA);
    SetIndexBuffer(5, Buy_Exit_Buffer, INDICATOR_DATA);
    SetIndexBuffer(6, Sell_Exit_Buffer, INDICATOR_DATA);

    // Arrow kodları
    PlotIndexSetInteger(3, PLOT_ARROW, Signal_Arrow_Code_Buy);
    PlotIndexSetInteger(4, PLOT_ARROW, Signal_Arrow_Code_Sell);
    PlotIndexSetInteger(5, PLOT_ARROW, Exit_Arrow_Code_Buy);
    PlotIndexSetInteger(6, PLOT_ARROW, Exit_Arrow_Code_Sell);

    // Renk ayarları
    PlotIndexSetInteger(3, PLOT_LINE_COLOR, Buy_Signal_Color);
    PlotIndexSetInteger(4, PLOT_LINE_COLOR, Sell_Signal_Color);
    PlotIndexSetInteger(5, PLOT_LINE_COLOR, clrBlue);
    PlotIndexSetInteger(6, PLOT_LINE_COLOR, clrMagenta);

    // Buffer'ları normal indexleme olarak ayarla (time series değil)
    ArraySetAsSeries(MA_Long_Buffer, false);
    ArraySetAsSeries(MA_Mid_Buffer, false);
    ArraySetAsSeries(MA_Short_Buffer, false);
    ArraySetAsSeries(Buy_Signals_Buffer, false);
    ArraySetAsSeries(Sell_Signals_Buffer, false);
    ArraySetAsSeries(Buy_Exit_Buffer, false);
    ArraySetAsSeries(Sell_Exit_Buffer, false);

    // Handle'ları oluştur
    ma_long_handle = iMA(_Symbol, _Period, MA_Period_Long, 0, MA_Method, MA_Applied_Price);
    ma_mid_handle = iMA(_Symbol, _Period, MA_Period_Mid, 0, MA_Method, MA_Applied_Price);
    ma_short_handle = iMA(_Symbol, _Period, MA_Period_Short, 0, MA_Method, MA_Applied_Price);
    rsi_handle = iRSI(_Symbol, _Period, RSI_Period, PRICE_CLOSE);

    // Handle kontrolü
    if(ma_long_handle == INVALID_HANDLE || ma_mid_handle == INVALID_HANDLE ||
       ma_short_handle == INVALID_HANDLE || rsi_handle == INVALID_HANDLE)
    {
        Print("❌ Indicator handle'ları oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Indicator ismi
    string indicator_name = StringFormat("XAUUSD MA Cross (%d/%d) + MA%d Filter",
                                       MA_Period_Short, MA_Period_Mid, MA_Period_Long);
    IndicatorSetString(INDICATOR_SHORTNAME, indicator_name);

    // Precision ayarı
    IndicatorSetInteger(INDICATOR_DIGITS, _Digits);

    // Yapıları başlat
    pending_signal.signal_type = 0;
    pending_signal.signal_bar = 0;
    pending_signal.bars_waiting = 0;
    pending_signal.signal_price = 0;
    pending_signal.signal_high = 0;
    pending_signal.signal_low = 0;

    open_position.position_type = 0;
    open_position.entry_bar = 0;
    open_position.entry_price = 0;

    Print("📊 XAUUSD MA Crossover + RSI Exit Signals indikatörü başlatıldı");
    Print("📈 Giriş: MA", MA_Period_Short, " x MA", MA_Period_Mid, " kesişimi + MA", MA_Period_Long, " onayı");
    Print("📉 Çıkış: RSI ", RSI_Overbought, "/", RSI_Oversold, " seviyeleri");
    Print("🔧 MA Metodu: ", EnumToString(MA_Method), " | RSI Periyodu: ", RSI_Period);
    Print("💡 BUY: MA7>MA14 kesişimi → Fiyat>MA50 onayı → RSI>70 çıkış");
    Print("💡 SELL: MA7<MA14 kesişimi → Fiyat<MA50 onayı → RSI<30 çıkış");

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Handle'ları temizle
    if(ma_long_handle != INVALID_HANDLE) IndicatorRelease(ma_long_handle);
    if(ma_mid_handle != INVALID_HANDLE) IndicatorRelease(ma_mid_handle);
    if(ma_short_handle != INVALID_HANDLE) IndicatorRelease(ma_short_handle);
    if(rsi_handle != INVALID_HANDLE) IndicatorRelease(rsi_handle);

    Print("📊 XAUUSD MA Crossover + RSI Exit Signals indikatörü kapatıldı");
}

//+------------------------------------------------------------------+
//| Pip mesafesi hesaplama fonksiyonu                                |
//+------------------------------------------------------------------+
double GetPipDistance()
{
    double pip_size = _Point;
    if(_Digits == 5 || _Digits == 3) pip_size *= 10;
    return Arrow_Offset_Pips * pip_size;
}

//+------------------------------------------------------------------+
//| Alert gönderme fonksiyonu                                        |
//+------------------------------------------------------------------+
void SendAlert(string signal_type, double price, datetime time)
{
    if(!Enable_Alerts) return;
    
    string message = StringFormat("%s Sinyal - %s: %.5f (%s)", 
                                signal_type, _Symbol, price, TimeToString(time));
    
    if(Enable_Popup) Alert(message);
    if(Enable_Sound) PlaySound("alert.wav");
    if(Enable_Email) SendMail("XAUUSD MA Crossover Sinyal", message);
    if(Enable_Push) SendNotification(message);
    
    Print("🔔 ", message);
}

//+------------------------------------------------------------------+
//| MA 14 temas kontrolü fonksiyonu                                  |
//+------------------------------------------------------------------+
bool CheckMA14Contact(double high, double low, double ma14_value)
{
    // Basit kontrol: mum çubuğu MA 14'ü kesiyor mu?
    // Temas varsa true, yoksa false döndür

    if(high >= ma14_value && low <= ma14_value)
        return true; // Temas var

    // Kapanış fiyatının MA14'e çok yakın olup olmadığını kontrol et
    double tolerance = _Point * 10; // 10 point tolerans
    if(MathAbs(high - ma14_value) <= tolerance || MathAbs(low - ma14_value) <= tolerance)
        return true; // Çok yakın, temas sayılır

    return false; // Temas yok
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    // Yeterli veri var mı kontrol et
    if(rates_total < MA_Period_Long + 10) return 0;
    
    // Handle'lar hazır mı kontrol et
    if(BarsCalculated(ma_long_handle) < rates_total ||
       BarsCalculated(ma_mid_handle) < rates_total ||
       BarsCalculated(ma_short_handle) < rates_total ||
       BarsCalculated(rsi_handle) < rates_total)
        return 0;

    // Veri kopyalama için gerekli bar sayısını hesapla
    int copy_bars = rates_total - prev_calculated + MA_Period_Long;
    if(prev_calculated == 0) copy_bars = rates_total;

    // Buffer'ları hazırla
    double rsi_values[];
    ArraySetAsSeries(rsi_values, false);

    // Verileri kopyala
    if(CopyBuffer(ma_long_handle, 0, 0, copy_bars, MA_Long_Buffer) <= 0) return 0;
    if(CopyBuffer(ma_mid_handle, 0, 0, copy_bars, MA_Mid_Buffer) <= 0) return 0;
    if(CopyBuffer(ma_short_handle, 0, 0, copy_bars, MA_Short_Buffer) <= 0) return 0;
    if(CopyBuffer(rsi_handle, 0, 0, copy_bars, rsi_values) <= 0) return 0;
    
    // Hesaplama başlangıç noktası
    int start = prev_calculated;
    if(start == 0) start = MA_Period_Long;
    
    // Ana hesaplama döngüsü - Normal indexleme kullan
    for(int i = start; i < rates_total - 1; i++)
    {
        // Buffer'ları temizle
        Buy_Signals_Buffer[i] = EMPTY_VALUE;
        Sell_Signals_Buffer[i] = EMPTY_VALUE;
        Buy_Exit_Buffer[i] = EMPTY_VALUE;
        Sell_Exit_Buffer[i] = EMPTY_VALUE;

        // Önceki bar kontrolü için yeterli veri var mı?
        if(i == 0) continue; // İlk bar için önceki bar yok

        // MA değerleri (normal indexleme)
        double ma_long_current = MA_Long_Buffer[i];
        double ma_long_prev = MA_Long_Buffer[i-1];
        double ma_mid_current = MA_Mid_Buffer[i];
        double ma_short_current = MA_Short_Buffer[i];
        double ma_short_prev = MA_Short_Buffer[i-1];

        // Fiyat ve RSI değerleri
        double close_current = close[i];
        double high_current = high[i];
        double low_current = low[i];
        double rsi_current = rsi_values[i];

        // YENİ BEKLETMELİ ONAY SİSTEMİ

        // 1. ADIM: Yeni MA7-MA14 kesişimi var mı kontrol et
        bool buy_crossover = (ma_short_prev < MA_Mid_Buffer[i-1]) && (ma_short_current > ma_mid_current); // MA7, MA14'ü yukarı kesti
        bool sell_crossover = (ma_short_prev > MA_Mid_Buffer[i-1]) && (ma_short_current < ma_mid_current); // MA7, MA14'ü aşağı kesti

        // 2. ADIM: Yeni kesişim varsa bekleyen sinyal oluştur
        if(buy_crossover && pending_signal.signal_type == 0)
        {
            pending_signal.signal_type = 1; // BUY sinyali beklemede
            pending_signal.signal_bar = i;
            pending_signal.bars_waiting = 0;
            pending_signal.signal_price = close_current;
            pending_signal.signal_high = high_current;
            pending_signal.signal_low = low_current;

            Print("⏳ BUY Sinyal beklemede - Bar: ", i, " Fiyat: ", close_current,
                  " MA7: ", ma_short_current, " MA14: ", ma_mid_current, " MA50: ", ma_long_current);
        }
        else if(sell_crossover && pending_signal.signal_type == 0)
        {
            pending_signal.signal_type = -1; // SELL sinyali beklemede
            pending_signal.signal_bar = i;
            pending_signal.bars_waiting = 0;
            pending_signal.signal_price = close_current;
            pending_signal.signal_high = high_current;
            pending_signal.signal_low = low_current;

            Print("⏳ SELL Sinyal beklemede - Bar: ", i, " Fiyat: ", close_current,
                  " MA7: ", ma_short_current, " MA14: ", ma_mid_current, " MA50: ", ma_long_current);
        }

        // 3. ADIM: Bekleyen sinyal varsa onay kontrolü yap
        if(pending_signal.signal_type != 0)
        {
            pending_signal.bars_waiting = i - pending_signal.signal_bar;

            // BUY sinyali onay kontrolü
            if(pending_signal.signal_type == 1)
            {
                if(close_current > ma_long_current) // MA50 onayı geldi
                {
                    // Sinyal onaylandı - ONAY BARINDA ok çiz (şimdiki bar)
                    Buy_Signals_Buffer[i] = low_current - GetPipDistance();

                    // Açık pozisyon kaydı
                    open_position.position_type = 1; // BUY pozisyonu
                    open_position.entry_bar = i;
                    open_position.entry_price = close_current;

                    Print("✅ BUY Sinyal onaylandı! Kesişim Bar: ", pending_signal.signal_bar,
                          " Onay Bar: ", i, " Bekleme: ", pending_signal.bars_waiting, " bar");

                    // Alert gönder
                    if(i == rates_total - 2)
                    {
                        SendAlert("BUY", close_current, time[i]); // Onay barının fiyatı ve zamanı
                    }

                    // Bekleyen sinyali temizle
                    pending_signal.signal_type = 0;
                }
                else if(pending_signal.bars_waiting >= Confirmation_Bars) // Süre doldu
                {
                    Print("❌ BUY Sinyal iptal - Onay gelmedi. Bekleme: ", pending_signal.bars_waiting, " bar");
                    pending_signal.signal_type = 0; // Sinyali iptal et
                }
            }
            // SELL sinyali onay kontrolü
            else if(pending_signal.signal_type == -1)
            {
                if(close_current < ma_long_current) // MA50 onayı geldi
                {
                    // Sinyal onaylandı - ONAY BARINDA ok çiz (şimdiki bar)
                    Sell_Signals_Buffer[i] = high_current + GetPipDistance();

                    // Açık pozisyon kaydı
                    open_position.position_type = -1; // SELL pozisyonu
                    open_position.entry_bar = i;
                    open_position.entry_price = close_current;

                    Print("✅ SELL Sinyal onaylandı! Kesişim Bar: ", pending_signal.signal_bar,
                          " Onay Bar: ", i, " Bekleme: ", pending_signal.bars_waiting, " bar");

                    // Alert gönder
                    if(i == rates_total - 2)
                    {
                        SendAlert("SELL", close_current, time[i]); // Onay barının fiyatı ve zamanı
                    }

                    // Bekleyen sinyali temizle
                    pending_signal.signal_type = 0;
                }
                else if(pending_signal.bars_waiting >= Confirmation_Bars) // Süre doldu
                {
                    Print("❌ SELL Sinyal iptal - Onay gelmedi. Bekleme: ", pending_signal.bars_waiting, " bar");
                    pending_signal.signal_type = 0; // Sinyali iptal et
                }
            }
        }

        // RSI ÇIKIŞ SİNYALLERİ KONTROLÜ
        if(open_position.position_type != 0) // Açık pozisyon varsa
        {
            // BUY pozisyonu için RSI çıkış kontrolü
            if(open_position.position_type == 1 && rsi_current >= RSI_Overbought)
            {
                // BUY çıkış sinyali - RSI aşırı alım seviyesinde
                Buy_Exit_Buffer[i] = high_current + GetPipDistance();

                double profit_pips = (close_current - open_position.entry_price) / _Point;
                if(_Digits == 5 || _Digits == 3) profit_pips /= 10;

                Print("🔵 BUY Çıkış (RSI>", RSI_Overbought, ") - Bar: ", i,
                      " Giriş: ", open_position.entry_price, " Çıkış: ", close_current,
                      " Kar: ", DoubleToString(profit_pips, 1), " pip");

                // Alert gönder
                if(i == rates_total - 2)
                {
                    string exit_message = StringFormat("BUY Çıkış (RSI %.1f) - Kar: %.1f pip",
                                                     rsi_current, profit_pips);
                    SendAlert(exit_message, close_current, time[i]);
                }

                // Pozisyonu kapat
                open_position.position_type = 0;
            }
            // SELL pozisyonu için RSI çıkış kontrolü
            else if(open_position.position_type == -1 && rsi_current <= RSI_Oversold)
            {
                // SELL çıkış sinyali - RSI aşırı satım seviyesinde
                Sell_Exit_Buffer[i] = low_current - GetPipDistance();

                double profit_pips = (open_position.entry_price - close_current) / _Point;
                if(_Digits == 5 || _Digits == 3) profit_pips /= 10;

                Print("🟣 SELL Çıkış (RSI<", RSI_Oversold, ") - Bar: ", i,
                      " Giriş: ", open_position.entry_price, " Çıkış: ", close_current,
                      " Kar: ", DoubleToString(profit_pips, 1), " pip");

                // Alert gönder
                if(i == rates_total - 2)
                {
                    string exit_message = StringFormat("SELL Çıkış (RSI %.1f) - Kar: %.1f pip",
                                                      rsi_current, profit_pips);
                    SendAlert(exit_message, close_current, time[i]);
                }

                // Pozisyonu kapat
                open_position.position_type = 0;
            }
        }
    }

    return rates_total;
}
