//+------------------------------------------------------------------+
//| New MA WPR EA                                                    |
//| Ye<PERSON>: MA50>MA14>MA7 BUY, WPR çıkış sistemi       |
//+------------------------------------------------------------------+
#property copyright "Horizon Trading"
#property version   "1.00"

#include <Trade\Trade.mqh>
CTrade trade;

//--- Input parametreleri
input group "=== MA Ayarları ==="
input int                   MA_Period_Long = 50;                  // MA50 Periyodu
input int                   MA_Period_Mid = 14;                   // MA14 Periyodu
input int                   MA_Period_Short = 7;                  // MA7 Periyodu
input ENUM_MA_METHOD        MA_Method = MODE_EMA;                 // MA Metodu
input ENUM_APPLIED_PRICE    MA_Applied_Price = PRICE_CLOSE;       // MA Uygulanan Fiyat

input group "=== Williams %R Çıkış Ayarları ==="
input int                   WPR_Period = 14;                     // Williams %R Periyodu
input double                WPR_Buy_Start_Level = -20.0;         // BUY Çıkış Takip Başlangıç Seviyesi
input double                WPR_Buy_Exit_Level = -20.0;          // BUY Çıkış Tetik Seviyesi
input double                WPR_Sell_Start_Level = -30.0;        // SELL Çıkış Takip Başlangıç Seviyesi
input double                WPR_Sell_Exit_Level = -30.0;         // SELL Çıkış Tetik Seviyesi

input group "=== Risk Yönetimi ==="
input double                Lot_Size = 0.01;                     // Lot Büyüklüğü
input double                ATR_Multiplier = 2.0;                // ATR Çarpanı (SL için)
input int                   ATR_Period = 14;                     // ATR Periyodu
input double                Risk_Percent = 2.0;                  // Risk Yüzdesi (% bakiye)

input group "=== Genel Ayarlar ==="
input int                   Magic_Number = 9999;                 // Magic Number
input string                Comment_Prefix = "MA_WPR";           // Yorum Ön Eki
input bool                  Use_ATR_SL = true;                   // ATR bazlı SL kullan
input bool                  Enable_Debug = true;                 // Debug Logları

//--- Global değişkenler
int ma_long_handle, ma_mid_handle, ma_short_handle, wpr_handle, atr_handle;
double ma_long_buffer[], ma_mid_buffer[], ma_short_buffer[], wpr_buffer[], atr_buffer[];
int debug_log_number = 0;

// WPR çıkış takip değişkenleri
bool buy_wpr_tracking = false;    // BUY pozisyonu WPR takip durumu
bool sell_wpr_tracking = false;   // SELL pozisyonu WPR takip durumu

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // MA ve WPR handle'larını oluştur
    ma_long_handle = iMA(_Symbol, _Period, MA_Period_Long, 0, MA_Method, MA_Applied_Price);
    ma_mid_handle = iMA(_Symbol, _Period, MA_Period_Mid, 0, MA_Method, MA_Applied_Price);
    ma_short_handle = iMA(_Symbol, _Period, MA_Period_Short, 0, MA_Method, MA_Applied_Price);
    wpr_handle = iWPR(_Symbol, _Period, WPR_Period);
    atr_handle = iATR(_Symbol, _Period, ATR_Period);
    
    if(ma_long_handle == INVALID_HANDLE || ma_mid_handle == INVALID_HANDLE ||
       ma_short_handle == INVALID_HANDLE || wpr_handle == INVALID_HANDLE ||
       atr_handle == INVALID_HANDLE)
    {
        Print("❌ Indicator handle oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Array ayarları
    ArraySetAsSeries(ma_long_buffer, true);
    ArraySetAsSeries(ma_mid_buffer, true);
    ArraySetAsSeries(ma_short_buffer, true);
    ArraySetAsSeries(wpr_buffer, true);
    ArraySetAsSeries(atr_buffer, true);
    
    // Değişkenleri sıfırla
    debug_log_number = 0;
    buy_wpr_tracking = false;
    sell_wpr_tracking = false;
    
    Print("✅ New MA WPR EA başlatıldı - Magic: ", Magic_Number);
    Print("📊 Giriş: MA7>MA14>MA50 (BUY) | MA7<MA14<MA50 (SELL)");
    Print("🎯 Çıkış: WPR aşırı alım/satım takip sistemi aktif");
    Print("🔍 BUY Çıkış: WPR>=", WPR_Buy_Start_Level, " (takip) → WPR<", WPR_Buy_Exit_Level, " (çıkış)");
    Print("🔍 SELL Çıkış: WPR<=", WPR_Sell_Start_Level, " (takip) → WPR>", WPR_Sell_Exit_Level, " (çıkış)");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("🔄 New MA WPR EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Yeterli bar kontrolü
    if(Bars(_Symbol, _Period) < MathMax(MA_Period_Long, WPR_Period) + 5)
        return;
    
    // Indicator verilerini al
    if(CopyBuffer(ma_long_handle, 0, 0, 3, ma_long_buffer) < 3 ||
       CopyBuffer(ma_mid_handle, 0, 0, 3, ma_mid_buffer) < 3 ||
       CopyBuffer(ma_short_handle, 0, 0, 3, ma_short_buffer) < 3 ||
       CopyBuffer(wpr_handle, 0, 0, 3, wpr_buffer) < 3 ||
       CopyBuffer(atr_handle, 0, 0, 2, atr_buffer) < 2)
    {
        return;
    }
    
    // Mevcut değerler
    double ma50_current = ma_long_buffer[0];      // MA50
    double ma14_current = ma_mid_buffer[0];       // MA14
    double ma7_current = ma_short_buffer[0];      // MA7
    double wpr_current = wpr_buffer[0];           // Williams %R
    double atr_current = atr_buffer[0];           // ATR
    
    // Mevcut pozisyon kontrolü
    bool has_buy_position = HasOpenPosition(POSITION_TYPE_BUY);   // 0 = BUY
    bool has_sell_position = HasOpenPosition(POSITION_TYPE_SELL); // 1 = SELL
    
    // 🎯 YENİ KARAR MATRİSİ
    
    // 📊 GİRİŞ SİNYALLERİ
    // BUY: MA7 > MA14 > MA50 (Yükseliş trendi - hızlı MA en üstte)
    bool buy_signal = (ma7_current > ma14_current) && (ma14_current > ma50_current);

    // SELL: MA7 < MA14 < MA50 (Düşüş trendi - hızlı MA en altta)
    bool sell_signal = (ma7_current < ma14_current) && (ma14_current < ma50_current);
    
    // 🚪 ÇIKIŞ SİNYALLERİ
    // BUY Çıkış: WPR >= -20 (takip başlat - aşırı alım) → WPR < -20 (çıkış - aşırı alımdan çıkış)
    if(has_buy_position)
    {
        // Debug: BUY WPR durumu - sürekli göster
        debug_log_number++;
        Print("🎯 [", debug_log_number, "] BUY WPR TAKİP - WPR: ", DoubleToString(wpr_current, 2),
              " | Takip Aktif: ", buy_wpr_tracking,
              " | Takip Koşulu (>=", WPR_Buy_Start_Level, "): ", (wpr_current >= WPR_Buy_Start_Level),
              " | Çıkış Koşulu (<", WPR_Buy_Exit_Level, "): ", (wpr_current < WPR_Buy_Exit_Level));

        if(wpr_current >= WPR_Buy_Start_Level && !buy_wpr_tracking)
        {
            buy_wpr_tracking = true;
            debug_log_number++;
            Print("🔍 [", debug_log_number, "] BUY WPR takip başladı (aşırı alım) - WPR: ", DoubleToString(wpr_current, 2), " >= ", WPR_Buy_Start_Level);
        }

        if(buy_wpr_tracking && wpr_current < WPR_Buy_Exit_Level)
        {
            CloseAllPositions(POSITION_TYPE_BUY); // BUY kapat
            buy_wpr_tracking = false;
            debug_log_number++;
            Print("🔵 [", debug_log_number, "] BUY pozisyonu kapatıldı (aşırı alımdan çıkış) - WPR: ", DoubleToString(wpr_current, 2), " < ", WPR_Buy_Exit_Level);
        }
    }

    // SELL Çıkış: WPR <= -80 (takip başlat - aşırı satım) → WPR > -80 (çıkış - aşırı satımdan çıkış)
    if(has_sell_position)
    {
        // Debug: SELL WPR durumu - sürekli göster
        debug_log_number++;
        Print("🎯 [", debug_log_number, "] SELL WPR TAKİP - WPR: ", DoubleToString(wpr_current, 2),
              " | Takip Aktif: ", sell_wpr_tracking,
              " | Takip Koşulu (<=", WPR_Sell_Start_Level, "): ", (wpr_current <= WPR_Sell_Start_Level),
              " | Çıkış Koşulu (>", WPR_Sell_Exit_Level, "): ", (wpr_current > WPR_Sell_Exit_Level));

        if(wpr_current <= WPR_Sell_Start_Level && !sell_wpr_tracking)
        {
            sell_wpr_tracking = true;
            debug_log_number++;
            Print("🔍 [", debug_log_number, "] SELL WPR takip başladı (aşırı satım) - WPR: ", DoubleToString(wpr_current, 2), " <= ", WPR_Sell_Start_Level);
        }

        if(sell_wpr_tracking && wpr_current > WPR_Sell_Exit_Level)
        {
            CloseAllPositions(POSITION_TYPE_SELL); // SELL kapat
            sell_wpr_tracking = false;
            debug_log_number++;
            Print("🔵 [", debug_log_number, "] SELL pozisyonu kapatıldı (aşırı satımdan çıkış) - WPR: ", DoubleToString(wpr_current, 2), " > ", WPR_Sell_Exit_Level);
        }
    }
    
    // 📊 DURUM RAPORU (Anlık)
    static datetime last_status_time = 0;
    if(TimeCurrent() - last_status_time >= 5) // 5 saniyede bir durum raporu
    {
        debug_log_number++;
        Print("📊 [", debug_log_number, "] DURUM RAPORU:");
        Print("   MA7: ", DoubleToString(ma7_current, 5), " MA14: ", DoubleToString(ma14_current, 5), " MA50: ", DoubleToString(ma50_current, 5));
        Print("   WPR: ", DoubleToString(wpr_current, 2));
        Print("   Pozisyon - BUY: ", has_buy_position, " SELL: ", has_sell_position);
        if(has_buy_position) Print("   BUY WPR Takip: ", buy_wpr_tracking);
        if(has_sell_position) Print("   SELL WPR Takip: ", sell_wpr_tracking);
        last_status_time = TimeCurrent();
    }

    // 📊 GİRİŞ İŞLEMLERİ - SADECE POZİSYON YOKSA SİNYAL ARA
    if(!has_buy_position && !has_sell_position)
    {
        // Debug - sadece sinyal olduğunda
        if(buy_signal || sell_signal)
        {
            debug_log_number++;
            Print("🔍 [", debug_log_number, "] SİNYAL BULUNDU (Pozisyon yok):");
            Print("   BUY Koşul (MA7>MA14>MA50): ", buy_signal);
            Print("   SELL Koşul (MA7<MA14<MA50): ", sell_signal);
        }

        // BUY sinyali işlemi
        if(buy_signal)
        {
            OpenBuyOrder(atr_current);
            buy_wpr_tracking = false; // Yeni pozisyon için takip sıfırla
        }

        // SELL sinyali işlemi
        if(sell_signal)
        {
            OpenSellOrder(atr_current);
            sell_wpr_tracking = false; // Yeni pozisyon için takip sıfırla
        }
    }
    else
    {
        // Pozisyon varken sinyal arama YOK - sadece WPR takip
        static datetime last_no_signal_time = 0;
        if(TimeCurrent() - last_no_signal_time >= 10) // 10 saniyede bir hatırlatma
        {
            debug_log_number++;
            string pos_type = has_buy_position ? "BUY" : "SELL";
            Print("⏸️ [", debug_log_number, "] ", pos_type, " pozisyonu açık - Sinyal arama DURDURULDU, sadece WPR takip aktif");
            last_no_signal_time = TimeCurrent();
        }
    }
}

//+------------------------------------------------------------------+
//| BUY emri aç                                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder(double atr_value)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = 0, tp = 0;
    
    if(Use_ATR_SL)
    {
        sl = ask - (atr_value * ATR_Multiplier);
    }
    
    double lot = CalculateLotSize(sl > 0 ? MathAbs(ask - sl) : atr_value * ATR_Multiplier);
    
    trade.SetExpertMagicNumber(Magic_Number);
    
    if(trade.Buy(lot, _Symbol, ask, sl, tp, Comment_Prefix + "_BUY"))
    {
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(ask, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   MA7>MA14>MA50 sıralaması ile BUY açıldı");
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] BUY EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder(double atr_value)
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = 0, tp = 0;
    
    if(Use_ATR_SL)
    {
        sl = bid + (atr_value * ATR_Multiplier);
    }
    
    double lot = CalculateLotSize(sl > 0 ? MathAbs(bid - sl) : atr_value * ATR_Multiplier);
    
    trade.SetExpertMagicNumber(Magic_Number);
    
    if(trade.Sell(lot, _Symbol, bid, sl, tp, Comment_Prefix + "_SELL"))
    {
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] SELL EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(bid, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   MA7<MA14<MA50 sıralaması ile SELL açıldı");
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] SELL EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| Lot büyüklüğü hesapla                                           |
//+------------------------------------------------------------------+
double CalculateLotSize(double sl_distance)
{
    if(Risk_Percent <= 0) return Lot_Size;

    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * Risk_Percent / 100.0;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

    double sl_points = sl_distance / point;
    double sl_ticks = sl_points * tick_size / point;
    double lot = risk_amount / (sl_ticks * tick_value);

    // Minimum ve maksimum lot kontrolü
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot = MathMax(lot, min_lot);
    lot = MathMin(lot, max_lot);
    lot = MathFloor(lot / lot_step) * lot_step;

    return lot;
}

//+------------------------------------------------------------------+
//| Pozisyon var mı kontrol et                                      |
//+------------------------------------------------------------------+
bool HasOpenPosition(int position_type)
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
                   PositionGetInteger(POSITION_TYPE) == position_type)
                {
                    return true;
                }
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void CloseAllPositions(int position_type)
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
                   PositionGetInteger(POSITION_TYPE) == position_type)
                {
                    if(!trade.PositionClose(ticket))
                    {
                        debug_log_number++;
                        Print("❌ [", debug_log_number, "] Pozisyon kapatma hatası: ", trade.ResultRetcode());
                    }
                }
            }
        }
    }
}
