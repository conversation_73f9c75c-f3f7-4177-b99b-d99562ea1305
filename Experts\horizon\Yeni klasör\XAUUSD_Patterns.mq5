
//+------------------------------------------------------------------+
//| XAUUSD M5 Pattern-Based Trading Functions                       |
//| Gerçek verilerden çıkarılan pattern'ler - Include Header        |
//+------------------------------------------------------------------+
#ifndef XAUUSD_PATTERNS_MQH
#define XAUUSD_PATTERNS_MQH

// Yardımcı fonksiyonlar
double GetBarSize(int bar) { return (iHigh(_Symbol, _Period, bar) - iLow(_Symbol, _Period, bar)) / _Point; }
double GetBodySize(int bar) { return MathAbs(iClose(_Symbol, _Period, bar) - iOpen(_Symbol, _Period, bar)) / _Point; }
double GetUpperShadow(int bar) { 
    double high = iHigh(_Symbol, _Period, bar);
    double open = iOpen(_Symbol, _Period, bar);
    double close = iClose(_Symbol, _Period, bar);
    return (high - MathMax(open, close)) / _Point;
}
double GetLowerShadow(int bar) {
    double low = iLow(_Symbol, _Period, bar);
    double open = iOpen(_Symbol, _Period, bar);
    double close = iClose(_Symbol, _Period, bar);
    return (MathMin(open, close) - low) / _Point;
}
double GetClosePosition(int bar) {
    double high = iHigh(_Symbol, _Period, bar);
    double low = iLow(_Symbol, _Period, bar);
    double close = iClose(_Symbol, _Period, bar);
    return (close - low) / (high - low) * 100;
}
bool IsBullish(int bar) { return iClose(_Symbol, _Period, bar) > iOpen(_Symbol, _Period, bar); }
bool IsBearish(int bar) { return iClose(_Symbol, _Period, bar) < iOpen(_Symbol, _Period, bar); }
bool IsDoji(int bar) { return GetBodySize(bar) < 2; }


//+------------------------------------------------------------------+
//| BUY_Reversal_After_2Bearish - 50.5% başarı
//| 2 ardışık düşüş + alt kapanış → BUY
//+------------------------------------------------------------------+
bool Check_BUY_Reversal_After_2Bearish(int bar = 1)
{
    // Başarı oranı: 50.5%
    // Ortalama hareket: 0.8 pip
    // Test sayısı: 2859
    
    // 2 ardışık bearish + alt kapanış
    if(!IsBearish(bar) || !IsBearish(bar + 1)) return false;
    if(GetClosePosition(bar) >= 30) return false;
    if(GetBarSize(bar) < 8 || GetBarSize(bar) > 30) return false;
    return true;
}

//+------------------------------------------------------------------+
//| BUY_London_Momentum - 49.7% başarı
//| London açılış momentum (güçlü bullish) → BUY
//+------------------------------------------------------------------+
bool Check_BUY_London_Momentum(int bar = 1)
{
    // Başarı oranı: 49.7%
    // Ortalama hareket: 0.4 pip
    // Test sayısı: 1058
    
    // Pattern koşulları buraya eklenecek
    return false;
}

//+------------------------------------------------------------------+
//| BUY_Doji_After_StrongDown - 48.1% başarı
//| Güçlü düşüş sonrası büyük doji → BUY
//+------------------------------------------------------------------+
bool Check_BUY_Doji_After_StrongDown(int bar = 1)
{
    // Başarı oranı: 48.1%
    // Ortalama hareket: 1.1 pip
    // Test sayısı: 1319
    
    // Pattern koşulları buraya eklenecek
    return false;
}

//+------------------------------------------------------------------+
//| SELL_NY_Momentum - 47.9% başarı
//| NY açılış momentum (güçlü bearish) → SELL
//+------------------------------------------------------------------+
bool Check_SELL_NY_Momentum(int bar = 1)
{
    // Başarı oranı: 47.9%
    // Ortalama hareket: -0.9 pip
    // Test sayısı: 1020
    
    // Pattern koşulları buraya eklenecek
    return false;
}

//+------------------------------------------------------------------+
//| BUY_Hammer_Pattern - 46.4% başarı
//| Hammer pattern (uzun alt gölge) → BUY
//+------------------------------------------------------------------+
bool Check_BUY_Hammer_Pattern(int bar = 1)
{
    // Başarı oranı: 46.4%
    // Ortalama hareket: -0.3 pip
    // Test sayısı: 4198
    
    // Hammer pattern
    if(GetLowerShadow(bar) <= GetBodySize(bar) * 2) return false;
    if(GetLowerShadow(bar) < 5) return false;
    if(GetClosePosition(bar) <= 60) return false;
    if(GetBarSize(bar) < 8) return false;
    return true;
}

//+------------------------------------------------------------------+
//| Ana pattern kontrol fonksiyonu                                  |
//+------------------------------------------------------------------+
string CheckAllPatterns(int bar = 1)
{
    if(Check_BUY_Reversal_After_2Bearish(bar)) return "BUY";
    if(Check_BUY_London_Momentum(bar)) return "BUY";
    if(Check_BUY_Doji_After_StrongDown(bar)) return "BUY";
    if(Check_SELL_NY_Momentum(bar)) return "SELL";
    if(Check_BUY_Hammer_Pattern(bar)) return "BUY";
    return "NONE";
}

#endif // XAUUSD_PATTERNS_MQH
