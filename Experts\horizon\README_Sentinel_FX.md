# 🚀 Sentinel FX - Advanced Dip/Peak Detection EA

## 📋 Genel Bakış

**Sentinel FX**, gelişmiş dip ve tepe yakalama algoritması kullanan profesyonel bir Expert Advisor'dır. RSI ve Williams %R indikatörlerini kullanarak piyasadaki aşırı alım/satım seviyelerini tespit eder ve optimal giriş noktalarında işlem açar.

### ✨ Temel Özellikler

- 🎯 **Dip/Tepe Yakalama**: RSI + Williams %R kombinasyonu
- 📱 **Telegram Entegrasyonu**: Anlık bildirimler ve durum raporları
- ⚡ **ATR Bazlı TP/SL**: Dinamik volatilite uyumu
- 💰 **Risk Yönetimi**: Bakiye bazlı lot hesaplama
- ⏰ **Zaman Filtresi**: Belirli saatlerde işlem yapma
- 📈 **Trailing Stop**: Kar koruma sistemi

---

## 🎯 Strateji Mantığı

### 🟢 DIP YAKALAMA (BUY Sinyali)
```
Koşullar:
✅ RSI ≤ 40 (Aşırı satım)
✅ Williams %R ≤ -60 (Aşırı satım)
✅ Momentum artışı (opsiyonel)
✅ Fiyat MA altında (opsiyonel)

Sonuç: BUY pozisyonu açılır
```

### 🔴 TEPE YAKALAMA (SELL Sinyali)
```
Koşullar:
✅ RSI ≥ 60 (Aşırı alım)
✅ Williams %R ≥ -40 (Aşırı alım)
✅ Momentum azalışı (opsiyonel)
✅ Fiyat MA üstünde (opsiyonel)

Sonuç: SELL pozisyonu açılır
```

### 🚪 Çıkış Sistemi
- **ATR Bazlı TP/SL**: TP = ATR × 3.0, SL = ATR × 1.5
- **Trailing Stop**: Kar koruma için dinamik SL
- **Risk/Reward**: 1:2 oranı (varsayılan)

---

## ⚙️ Parametre Ayarları

### 📊 Dip/Tepe Yakalama
| Parametre | Varsayılan | Açıklama |
|-----------|------------|----------|
| `RSI_Period` | 14 | RSI periyodu |
| `RSI_Oversold` | 40.0 | RSI aşırı satım seviyesi |
| `RSI_Overbought` | 60.0 | RSI aşırı alım seviyesi |
| `WPR_Period` | 14 | Williams %R periyodu |
| `WPR_Oversold` | -60.0 | WPR aşırı satım seviyesi |
| `WPR_Overbought` | -40.0 | WPR aşırı alım seviyesi |

### 🎯 Çıkış Ayarları
| Parametre | Varsayılan | Açıklama |
|-----------|------------|----------|
| `Use_ATR_TP_SL` | true | ATR bazlı TP/SL kullan |
| `ATR_TP_Multiplier` | 3.0 | ATR TP çarpanı |
| `ATR_SL_Multiplier` | 1.5 | ATR SL çarpanı |
| `ATR_Period` | 14 | ATR periyodu |
| `Use_Trailing_Stop` | true | Trailing stop kullan |
| `Trailing_Stop_Pips` | 15.0 | Trailing stop mesafesi |

### 💰 Risk Yönetimi
| Parametre | Varsayılan | Açıklama |
|-----------|------------|----------|
| `Use_Risk_Management` | true | Risk yönetimi kullan |
| `Risk_Percent` | 2.0 | Risk yüzdesi (% bakiye) |
| `Fixed_Lot_Size` | 0.01 | Sabit lot büyüklüğü |
| `Max_Lot_Size` | 1.0 | Maksimum lot |
| `Min_Lot_Size` | 0.01 | Minimum lot |

### ⏰ Zaman Filtresi
| Parametre | Varsayılan | Açıklama |
|-----------|------------|----------|
| `Use_Time_Filter` | true | Zaman filtresi kullan |
| `Start_Hour` | 8 | Başlangıç saati |
| `End_Hour` | 18 | Bitiş saati |
| `Trade_Monday` | true | Pazartesi işlem yap |
| `Trade_Friday` | true | Cuma işlem yap |

---

## 📱 Telegram Kurulumu

### 1. Bot Oluşturma
1. Telegram'da **@BotFather**'a mesaj gönderin
2. `/newbot` komutunu gönderin
3. Bot adını belirleyin (örn: "Sentinel FX Bot")
4. Bot kullanıcı adını belirleyin (örn: "sentinel_fx_bot")
5. **Bot Token**'ını kaydedin

### 2. Chat ID Alma
1. Oluşturduğunuz bot'a `/start` mesajı gönderin
2. Tarayıcıda şu adresi açın:
   ```
   https://api.telegram.org/bot[BOT_TOKEN]/getUpdates
   ```
3. **Chat ID**'yi bulun ve kaydedin

### 3. EA'da Ayarlama
```mql5
Enable_Telegram = true
Telegram_Token = "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"
Telegram_ChatID = "123456789"
```

### 4. Bildirim Türleri
- ✅ **Sinyal Bildirimleri**: Dip/tepe yakalandığında
- 📊 **İşlem Bildirimleri**: Pozisyon açma/kapama
- 📈 **Durum Raporları**: Belirli aralıklarla

---

## 🔧 Kurulum ve Kullanım

### 1. Dosya Yerleştirme
```
MetaTrader 5/MQL5/Experts/horizon/Sentinel_FX.mq5
```

### 2. Derleme
1. MetaEditor'de dosyayı açın
2. **F7** tuşuna basarak derleyin
3. Hata olmadığından emin olun

### 3. Grafik Ekleme
1. MetaTrader 5'te istediğiniz sembolü açın
2. **Navigator** → **Expert Advisors** → **Sentinel_FX**
3. Grafiğe sürükleyip bırakın

### 4. Parametre Ayarlama
1. **Inputs** sekmesinde parametreleri ayarlayın
2. Telegram bilgilerini girin
3. **OK** butonuna basın

---

## 📊 Performans Optimizasyonu

### 🎯 Konservatif Ayarlar (Az Sinyal, Yüksek Kalite)
```mql5
RSI_Oversold = 35.0
RSI_Overbought = 65.0
WPR_Oversold = -70.0
WPR_Overbought = -30.0
Use_MA_Filter = true
Use_Momentum_Filter = true
```

### ⚡ Agresif Ayarlar (Çok Sinyal, Orta Kalite)
```mql5
RSI_Oversold = 45.0
RSI_Overbought = 55.0
WPR_Oversold = -55.0
WPR_Overbought = -45.0
Use_MA_Filter = false
Use_Momentum_Filter = false
```

### 🏃 Scalping Ayarları
```mql5
ATR_TP_Multiplier = 1.5
ATR_SL_Multiplier = 0.8
Trailing_Stop_Pips = 8.0
Risk_Percent = 1.0
```

### 📈 Swing Trading Ayarları
```mql5
ATR_TP_Multiplier = 5.0
ATR_SL_Multiplier = 2.5
ATR_Period = 20
Risk_Percent = 3.0
```

---

## 🚨 Önemli Notlar

### ⚠️ Risk Uyarıları
- **Forex ticareti yüksek risk içerir**
- **Demo hesapta test edin**
- **Sadece kaybetmeyi göze alabileceğiniz para ile işlem yapın**
- **Geçmiş performans gelecek sonuçları garanti etmez**

### 🔒 Güvenlik
- **Telegram Token'ınızı kimseyle paylaşmayın**
- **VPS kullanıyorsanız güvenli bağlantı sağlayın**
- **Düzenli olarak hesap durumunuzu kontrol edin**

### 📞 Destek
- **Sorunlar için log dosyalarını kontrol edin**
- **Debug modunu açarak detaylı bilgi alın**
- **Telegram bildirimleri çalışmıyorsa Token/ChatID'yi kontrol edin**

---

## 📈 Beklenen Sonuçlar

### 📊 Tipik Performans Metrikleri
- **Win Rate**: %60-70 (optimal ayarlarla)
- **Risk/Reward**: 1:2 (varsayılan)
- **Günlük Sinyal**: 2-5 (piyasa koşullarına bağlı)
- **Maksimum Drawdown**: <%10 (2% risk ile)

### 🎯 En İyi Çalışan Piyasalar
- **XAUUSD** (Altın): Yüksek volatilite
- **EURUSD**: Stabil trend
- **GBPUSD**: Güçlü momentum
- **USDJPY**: Net seviyeler

### ⏰ En İyi Zaman Dilimleri
- **M15**: Scalping için
- **H1**: Günlük işlemler için
- **H4**: Swing trading için

---

## 🔄 Güncelleme Geçmişi

### v2.00 (Mevcut)
- ✅ Telegram entegrasyonu eklendi
- ✅ ATR bazlı TP/SL sistemi
- ✅ Gelişmiş risk yönetimi
- ✅ Zaman filtresi eklendi
- ✅ Trailing stop sistemi

### v1.00 (İlk Sürüm)
- ✅ Temel dip/tepe yakalama
- ✅ RSI + Williams %R kombinasyonu
- ✅ Basit TP/SL sistemi

---

**© 2025 Horizon Trading - Sentinel FX Expert Advisor**

*Bu EA, profesyonel forex yatırımcıları için geliştirilmiştir. Kullanmadan önce demo hesapta test etmeniz önerilir.*
