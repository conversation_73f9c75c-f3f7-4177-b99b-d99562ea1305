//+------------------------------------------------------------------+
//| MouseDragPanel.mq5 - Mouse Event Tabanlı Sürükleme             |
//| Manuel mouse event handling ile tam kontrol                     |
//+------------------------------------------------------------------+
#property copyright "Horizon Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Mouse Event Tabanlı Sürüklenebilir Panel"

#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| Panel a<PERSON>                                                  |
//+------------------------------------------------------------------+
input int PanelX = 50;              // Panel X pozisyonu
input int PanelY = 50;              // Panel Y pozisyonu

//+------------------------------------------------------------------+
//| Panel boyutları                                                 |
//+------------------------------------------------------------------+
#define PANEL_WIDTH     350
#define PANEL_HEIGHT    300
#define HEADER_HEIGHT   25
#define BUTTON_HEIGHT   22
#define ROW_HEIGHT      16
#define MARGIN          6

//+------------------------------------------------------------------+
//| Global değişkenler                                              |
//+------------------------------------------------------------------+
string g_panelName = "MouseDragPanel";
datetime g_lastUpdate = 0;

// Sürükleme durumu
bool g_isDragging = false;
int g_dragStartX = 0;
int g_dragStartY = 0;
int g_panelStartX = 0;
int g_panelStartY = 0;
int g_currentX = 0;
int g_currentY = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    g_currentX = PanelX;
    g_currentY = PanelY;
    
    // Mouse event'lerini aktif et
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, true);
    
    CreatePanel();
    UpdatePanelContent();
    
    Print("✅ Mouse Drag Panel başlatıldı - Mouse events aktif");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    ObjectsDeleteAll(0, g_panelName);
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, false);
    Print("🔄 Mouse Drag Panel kapatıldı");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(TimeCurrent() - g_lastUpdate >= 2)
    {
        UpdatePanelContent();
        g_lastUpdate = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        HandleButtonClick(sparam);
    }
    else if(id == CHARTEVENT_MOUSE_MOVE)
    {
        HandleMouseMove((int)lparam, (int)dparam, (int)sparam);
    }
}

//+------------------------------------------------------------------+
//| Mouse hareket işleyici                                          |
//+------------------------------------------------------------------+
void HandleMouseMove(int x, int y, int flags)
{
    // Sol mouse tuşu basılı mı kontrol et
    bool leftButtonPressed = (flags & 1) != 0;
    
    if(leftButtonPressed)
    {
        // Mouse panel üzerinde mi kontrol et
        if(!g_isDragging && IsMouseOverPanel(x, y))
        {
            // Sürükleme başlat
            g_isDragging = true;
            g_dragStartX = x;
            g_dragStartY = y;
            g_panelStartX = g_currentX;
            g_panelStartY = g_currentY;
            
            Print("🖱️ Sürükleme başladı: (", x, ",", y, ")");
        }
        
        if(g_isDragging)
        {
            // Panel pozisyonunu güncelle
            int deltaX = x - g_dragStartX;
            int deltaY = y - g_dragStartY;
            
            g_currentX = g_panelStartX + deltaX;
            g_currentY = g_panelStartY + deltaY;
            
            // Ekran sınırları kontrolü
            int chartWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
            int chartHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);
            
            if(g_currentX < 0) g_currentX = 0;
            if(g_currentY < 0) g_currentY = 0;
            if(g_currentX + PANEL_WIDTH > chartWidth) g_currentX = chartWidth - PANEL_WIDTH;
            if(g_currentY + PANEL_HEIGHT > chartHeight) g_currentY = chartHeight - PANEL_HEIGHT;
            
            // Panel pozisyonunu güncelle
            UpdatePanelPosition();
        }
    }
    else
    {
        // Sol mouse tuşu bırakıldı
        if(g_isDragging)
        {
            g_isDragging = false;
            Print("🖱️ Sürükleme bitti: (", g_currentX, ",", g_currentY, ")");
        }
    }
}

//+------------------------------------------------------------------+
//| Mouse panel üzerinde mi kontrol et                              |
//+------------------------------------------------------------------+
bool IsMouseOverPanel(int mouseX, int mouseY)
{
    return (mouseX >= g_currentX && mouseX <= g_currentX + PANEL_WIDTH &&
            mouseY >= g_currentY && mouseY <= g_currentY + PANEL_HEIGHT);
}

//+------------------------------------------------------------------+
//| Panel pozisyonunu güncelle                                      |
//+------------------------------------------------------------------+
void UpdatePanelPosition()
{
    // Ana panel
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_XDISTANCE, g_currentX);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_YDISTANCE, g_currentY);
    
    // Header
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XDISTANCE, g_currentX);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YDISTANCE, g_currentY);
    
    // Butonları güncelle
    UpdateButtonPositions();
    
    // İçeriği güncelle
    UpdatePanelContent();
}

//+------------------------------------------------------------------+
//| Panel oluştur                                                   |
//+------------------------------------------------------------------+
void CreatePanel()
{
    // Ana panel arka planı
    ObjectCreate(0, g_panelName + "_Background", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_XDISTANCE, g_currentX);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_YDISTANCE, g_currentY);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_YSIZE, PANEL_HEIGHT);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BGCOLOR, C'25,25,25');
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_COLOR, C'100,100,100');
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_BACK, false);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, g_panelName + "_Background", OBJPROP_ZORDER, 1000);
    
    // Header - sürükleme alanı
    ObjectCreate(0, g_panelName + "_Header", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XDISTANCE, g_currentX);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YDISTANCE, g_currentY);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_XSIZE, PANEL_WIDTH);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_YSIZE, HEADER_HEIGHT);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BGCOLOR, C'0,100,180');
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_BACK, false);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, g_panelName + "_Header", OBJPROP_ZORDER, 1001);
    
    CreateButtons();
}

//+------------------------------------------------------------------+
//| Butonları oluştur                                               |
//+------------------------------------------------------------------+
void CreateButtons()
{
    int btn_y = g_currentY + 180;
    int btn_width = (PANEL_WIDTH - 4 * MARGIN) / 3;
    
    // Karlı Kapat butonu
    CreateButton("BtnProfit", "💚 Karlı", g_currentX + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, C'0,120,0');
    
    // Zarar Kapat butonu  
    CreateButton("BtnLoss", "❤️ Zarar", g_currentX + MARGIN + btn_width + MARGIN, btn_y, btn_width, BUTTON_HEIGHT, C'120,0,0');
    
    // Hepsini Kapat butonu
    CreateButton("BtnAll", "🚫 Hepsini Kapat", g_currentX + MARGIN, btn_y + BUTTON_HEIGHT + MARGIN, 2 * btn_width + MARGIN, BUTTON_HEIGHT, C'120,120,0');
}

//+------------------------------------------------------------------+
//| Buton pozisyonlarını güncelle                                   |
//+------------------------------------------------------------------+
void UpdateButtonPositions()
{
    int btn_y = g_currentY + 180;
    int btn_width = (PANEL_WIDTH - 4 * MARGIN) / 3;
    
    // Karlı Kapat butonu
    ObjectSetInteger(0, g_panelName + "_BtnProfit", OBJPROP_XDISTANCE, g_currentX + MARGIN);
    ObjectSetInteger(0, g_panelName + "_BtnProfit", OBJPROP_YDISTANCE, btn_y);
    ObjectSetInteger(0, g_panelName + "_BtnProfit_Text", OBJPROP_XDISTANCE, g_currentX + MARGIN + btn_width/2);
    ObjectSetInteger(0, g_panelName + "_BtnProfit_Text", OBJPROP_YDISTANCE, btn_y + BUTTON_HEIGHT/2 - 6);
    
    // Zarar Kapat butonu
    ObjectSetInteger(0, g_panelName + "_BtnLoss", OBJPROP_XDISTANCE, g_currentX + MARGIN + btn_width + MARGIN);
    ObjectSetInteger(0, g_panelName + "_BtnLoss", OBJPROP_YDISTANCE, btn_y);
    ObjectSetInteger(0, g_panelName + "_BtnLoss_Text", OBJPROP_XDISTANCE, g_currentX + MARGIN + btn_width + MARGIN + btn_width/2);
    ObjectSetInteger(0, g_panelName + "_BtnLoss_Text", OBJPROP_YDISTANCE, btn_y + BUTTON_HEIGHT/2 - 6);
    
    // Hepsini Kapat butonu
    ObjectSetInteger(0, g_panelName + "_BtnAll", OBJPROP_XDISTANCE, g_currentX + MARGIN);
    ObjectSetInteger(0, g_panelName + "_BtnAll", OBJPROP_YDISTANCE, btn_y + BUTTON_HEIGHT + MARGIN);
    ObjectSetInteger(0, g_panelName + "_BtnAll_Text", OBJPROP_XDISTANCE, g_currentX + MARGIN + (2 * btn_width + MARGIN)/2);
    ObjectSetInteger(0, g_panelName + "_BtnAll_Text", OBJPROP_YDISTANCE, btn_y + BUTTON_HEIGHT + MARGIN + BUTTON_HEIGHT/2 - 6);
}

//+------------------------------------------------------------------+
//| Buton oluştur                                                   |
//+------------------------------------------------------------------+
void CreateButton(string name, string text, int x, int y, int width, int height, color bgColor)
{
    string objName = g_panelName + "_" + name;
    
    // Buton arka planı
    ObjectCreate(0, objName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, objName, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, objName, OBJPROP_YSIZE, height);
    ObjectSetInteger(0, objName, OBJPROP_BGCOLOR, bgColor);
    ObjectSetInteger(0, objName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, C'60,60,60');
    ObjectSetInteger(0, objName, OBJPROP_BACK, false);
    ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName, OBJPROP_ZORDER, 1002);
    
    // Buton metni
    ObjectCreate(0, objName + "_Text", OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_XDISTANCE, x + width/2);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_YDISTANCE, y + height/2 - 6);
    ObjectSetString(0, objName + "_Text", OBJPROP_TEXT, text);
    ObjectSetString(0, objName + "_Text", OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, objName + "_Text", OBJPROP_FONTSIZE, 8);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_ANCHOR, ANCHOR_CENTER);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_BACK, false);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName + "_Text", OBJPROP_ZORDER, 1003);
}

//+------------------------------------------------------------------+
//| Panel içeriğini güncelle                                        |
//+------------------------------------------------------------------+
void UpdatePanelContent()
{
    // Panel verilerini hesapla
    int positionCount = 0;
    double totalVolume = 0;
    double totalProfit = 0;
    int profitableCount = 0;
    int losingCount = 0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);

        positionCount++;
        totalVolume += volume;
        totalProfit += profit;

        if(profit > 0)
            profitableCount++;
        else if(profit < 0)
            losingCount++;
    }

    // Günlük P&L hesapla
    double dailyPL = CalculateDailyPL(totalProfit);

    // Panel içeriğini çiz
    DrawPanelContent(positionCount, totalVolume, totalProfit, profitableCount, losingCount, dailyPL);
}

//+------------------------------------------------------------------+
//| Panel içeriğini çiz                                             |
//+------------------------------------------------------------------+
void DrawPanelContent(int posCount, double totalVol, double totalPL, int profitCount, int lossCount, double dailyPL)
{
    int y_pos = g_currentY + HEADER_HEIGHT + MARGIN;

    // Başlık
    CreateLabel("Title", "📊 İşlem Paneli", g_currentX + PANEL_WIDTH/2, g_currentY + 12, clrWhite, 10, ANCHOR_CENTER);

    // Günlük P&L
    string dailyText = "💰 Günlük: $" + DoubleToString(dailyPL, 2);
    color dailyColor = (dailyPL >= 0) ? clrLimeGreen : clrTomato;
    CreateLabel("DailyPL", dailyText, g_currentX + MARGIN, y_pos, dailyColor, 9);
    y_pos += 18;

    // Açık pozisyon sayısı
    string posText = "📈 Pozisyon: " + IntegerToString(posCount);
    CreateLabel("PositionCount", posText, g_currentX + MARGIN, y_pos, clrWhite, 8);
    y_pos += 16;

    // Toplam hacim
    string volText = "📊 Hacim: " + DoubleToString(totalVol, 2);
    CreateLabel("TotalVolume", volText, g_currentX + MARGIN, y_pos, clrWhite, 8);
    y_pos += 16;

    // Toplam P&L
    string plText = "💵 P&L: $" + DoubleToString(totalPL, 2);
    color plColor = (totalPL >= 0) ? clrLimeGreen : clrTomato;
    CreateLabel("TotalPL", plText, g_currentX + MARGIN, y_pos, plColor, 9);
    y_pos += 16;

    // Karlı/Zararlı sayısı
    string countText = "📊 Karlı: " + IntegerToString(profitCount) + " | Zararlı: " + IntegerToString(lossCount);
    CreateLabel("ProfitLossCount", countText, g_currentX + MARGIN, y_pos, clrWhite, 8);
    y_pos += 20;

    // Pozisyon listesi
    DrawPositionList(y_pos);
}

//+------------------------------------------------------------------+
//| Pozisyon listesini çiz                                          |
//+------------------------------------------------------------------+
void DrawPositionList(int start_y)
{
    // Pozisyon listesi başlığı
    CreateLabel("ListTitle", "📋 Açık Pozisyonlar:", g_currentX + MARGIN, start_y, clrYellow, 8);

    int y_pos = start_y + 16;
    int maxRows = 4;
    int rowCount = 0;

    // Önceki pozisyon label'larını temizle
    for(int i = 0; i < 10; i++)
    {
        ObjectDelete(0, g_panelName + "_Pos" + IntegerToString(i));
    }

    // Açık pozisyonları listele
    for(int i = 0; i < PositionsTotal() && rowCount < maxRows; i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);

        string typeStr = (type == POSITION_TYPE_BUY) ? "BUY" : "SELL";
        string profitStr = (profit >= 0) ? "+" + DoubleToString(profit, 1) : DoubleToString(profit, 1);
        color profitColor = (profit >= 0) ? clrLimeGreen : clrTomato;

        string posText = StringFormat("%s %s %.2f | $%s",
                                    typeStr,
                                    symbol,
                                    volume,
                                    profitStr);

        CreateLabel("Pos" + IntegerToString(rowCount), posText, g_currentX + MARGIN, y_pos, profitColor, 7);
        y_pos += ROW_HEIGHT;
        rowCount++;
    }

    // Eğer çok fazla pozisyon varsa "..." göster
    if(PositionsTotal() > maxRows)
    {
        string moreText = "... +" + IntegerToString(PositionsTotal() - maxRows) + " daha";
        CreateLabel("MorePositions", moreText, g_currentX + MARGIN, y_pos, clrGray, 7);
    }
}

//+------------------------------------------------------------------+
//| Label oluştur                                                   |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int x, int y, color clr, int fontSize, ENUM_ANCHOR_POINT anchor = ANCHOR_LEFT_UPPER)
{
    string objName = g_panelName + "_" + name;

    ObjectDelete(0, objName);
    ObjectCreate(0, objName, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetString(0, objName, OBJPROP_TEXT, text);
    ObjectSetString(0, objName, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, fontSize);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, objName, OBJPROP_ANCHOR, anchor);
    ObjectSetInteger(0, objName, OBJPROP_BACK, false);
    ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName, OBJPROP_ZORDER, 1002);
}

//+------------------------------------------------------------------+
//| Günlük P&L hesapla                                             |
//+------------------------------------------------------------------+
double CalculateDailyPL(double currentPL)
{
    double dailyPL = 0;
    datetime todayStart = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));

    HistorySelect(todayStart, TimeCurrent());

    for(int i = 0; i < HistoryDealsTotal(); i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket > 0)
        {
            ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);
            if(dealType == DEAL_TYPE_BUY || dealType == DEAL_TYPE_SELL)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                double commission = HistoryDealGetDouble(ticket, DEAL_COMMISSION);
                double swap = HistoryDealGetDouble(ticket, DEAL_SWAP);

                dailyPL += (profit + commission + swap);
            }
        }
    }

    dailyPL += currentPL;
    return dailyPL;
}

//+------------------------------------------------------------------+
//| Buton tıklama işleyici                                          |
//+------------------------------------------------------------------+
void HandleButtonClick(string objName)
{
    if(StringFind(objName, "BtnProfit") >= 0)
    {
        CloseProfitablePositions();
    }
    else if(StringFind(objName, "BtnLoss") >= 0)
    {
        CloseLosingPositions();
    }
    else if(StringFind(objName, "BtnAll") >= 0)
    {
        CloseAllPositions();
    }
}

//+------------------------------------------------------------------+
//| Karlı pozisyonları kapat                                        |
//+------------------------------------------------------------------+
void CloseProfitablePositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double profit = PositionGetDouble(POSITION_PROFIT);
        if(profit > 0)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(trade.PositionClose(ticket))
            {
                closedCount++;
            }
        }
    }

    Print("💚 ", closedCount, " karlı pozisyon kapatıldı");
    UpdatePanelContent();
}

//+------------------------------------------------------------------+
//| Zararlı pozisyonları kapat                                      |
//+------------------------------------------------------------------+
void CloseLosingPositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double profit = PositionGetDouble(POSITION_PROFIT);
        if(profit < 0)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(trade.PositionClose(ticket))
            {
                closedCount++;
            }
        }
    }

    Print("❤️ ", closedCount, " zararlı pozisyon kapatıldı");
    UpdatePanelContent();
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
        {
            closedCount++;
        }
    }

    Print("🚫 ", closedCount, " pozisyon kapatıldı");
    UpdatePanelContent();
}
