//+------------------------------------------------------------------+
//| WindowsStylePanel.mq5 - Windows Penceresi Gibi Sürüklenebilir  |
//| CDialog sınıfı ile gerçek Windows penceresi davranışı           |
//+------------------------------------------------------------------+
#property copyright "Horizon Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Windows Penceresi Gibi Sürüklenebilir İşlem Paneli"

#include <Controls\Dialog.mqh>
#include <Controls\Label.mqh>
#include <Controls\Button.mqh>
#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| Panel boyutları                                                 |
//+------------------------------------------------------------------+
#define PANEL_WIDTH     350
#define PANEL_HEIGHT    280
#define BUTTON_WIDTH    100
#define BUTTON_HEIGHT   25
#define MARGIN          10

//+------------------------------------------------------------------+
//| TradeManagerDialog sınıfı - Windows penceresi gibi             |
//+------------------------------------------------------------------+
class TradeManagerDialog : public CDialog
{
private:
    // UI Elementleri
    CLabel            m_lblTitle;
    CLabel            m_lblDailyPL;
    CLabel            m_lblPositions;
    CLabel            m_lblVolume;
    CLabel            m_lblTotalPL;
    CLabel            m_lblCounts;
    CLabel            m_lblPositionList[5];
    
    // Butonlar
    CButton           m_btnCloseProfitable;
    CButton           m_btnCloseLosing;
    CButton           m_btnCloseAll;
    
    // Veriler
    datetime          m_lastUpdate;
    
public:
    TradeManagerDialog();
    ~TradeManagerDialog();
    
    virtual bool      Create(const long chart, const string name, const int subwin, const int x1, const int y1);
    virtual void      OnTick();
    virtual bool      OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam);
    
    void              UpdateData();
    void              CreateElements();
    double            CalculateDailyPL();
    
    // İşlem yönetimi
    void              CloseProfitablePositions();
    void              CloseLosingPositions();
    void              CloseAllPositions();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
TradeManagerDialog::TradeManagerDialog()
{
    m_lastUpdate = 0;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
TradeManagerDialog::~TradeManagerDialog()
{
}

//+------------------------------------------------------------------+
//| Panel oluştur - Windows penceresi gibi                          |
//+------------------------------------------------------------------+
bool TradeManagerDialog::Create(const long chart, const string name, const int subwin, const int x1, const int y1)
{
    // CDialog ile Windows penceresi oluştur
    if(!CDialog::Create(chart, name, subwin, x1, y1, x1 + PANEL_WIDTH, y1 + PANEL_HEIGHT))
        return false;
    
    // Panel özelliklerini ayarla
    Background(C'40,40,40');                    // Koyu gri arka plan
    BorderType(BORDER_RAISED);                  // Yükseltilmiş kenarlık
    Color(C'200,200,200');                      // Açık gri kenarlık
    
    // Başlık çubuğunu aktif et - Bu sürükleme sağlar!
    Caption("📊 İşlem Yönetim Paneli");
    
    // Panel her zaman en üstte kalsın
    SetInteger(OBJPROP_ZORDER, 1000);
    
    // Minimize/maximize butonlarını gizle
    SetInteger(OBJPROP_CHART_OBJ_PERIOD, 0);
    
    CreateElements();
    UpdateData();
    
    return true;
}

//+------------------------------------------------------------------+
//| UI elementlerini oluştur                                        |
//+------------------------------------------------------------------+
void TradeManagerDialog::CreateElements()
{
    int y_pos = 10;
    
    // Günlük P&L
    m_lblDailyPL.Create(m_chart_id, "DailyPL", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 20);
    m_lblDailyPL.Text("💰 Günlük P&L: $0.00");
    m_lblDailyPL.Color(clrYellow);
    m_lblDailyPL.FontSize(10);
    Add(m_lblDailyPL);
    y_pos += 25;
    
    // Pozisyon sayısı
    m_lblPositions.Create(m_chart_id, "Positions", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 18);
    m_lblPositions.Text("📈 Açık Pozisyon: 0");
    m_lblPositions.Color(clrLightBlue);
    m_lblPositions.FontSize(9);
    Add(m_lblPositions);
    y_pos += 22;
    
    // Toplam hacim
    m_lblVolume.Create(m_chart_id, "Volume", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 18);
    m_lblVolume.Text("📊 Toplam Hacim: 0.00");
    m_lblVolume.Color(clrLightGray);
    m_lblVolume.FontSize(9);
    Add(m_lblVolume);
    y_pos += 22;
    
    // Toplam P&L
    m_lblTotalPL.Create(m_chart_id, "TotalPL", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 18);
    m_lblTotalPL.Text("💵 Toplam P&L: $0.00");
    m_lblTotalPL.Color(clrWhite);
    m_lblTotalPL.FontSize(10);
    Add(m_lblTotalPL);
    y_pos += 22;
    
    // Karlı/Zararlı sayısı
    m_lblCounts.Create(m_chart_id, "Counts", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 18);
    m_lblCounts.Text("📊 Karlı: 0 | Zararlı: 0");
    m_lblCounts.Color(clrWhite);
    m_lblCounts.FontSize(9);
    Add(m_lblCounts);
    y_pos += 30;
    
    // Butonlar
    int btn_y = y_pos;
    
    // Karlı Kapat butonu
    m_btnCloseProfitable.Create(m_chart_id, "BtnProfit", m_subwin, MARGIN, btn_y, MARGIN + BUTTON_WIDTH, btn_y + BUTTON_HEIGHT);
    m_btnCloseProfitable.Text("💚 Karlı Kapat");
    m_btnCloseProfitable.Color(clrWhite);
    m_btnCloseProfitable.ColorBackground(C'0,120,0');
    m_btnCloseProfitable.FontSize(9);
    Add(m_btnCloseProfitable);
    
    // Zarar Kapat butonu
    m_btnCloseLosing.Create(m_chart_id, "BtnLoss", m_subwin, MARGIN + BUTTON_WIDTH + 10, btn_y, MARGIN + 2*BUTTON_WIDTH + 10, btn_y + BUTTON_HEIGHT);
    m_btnCloseLosing.Text("❤️ Zarar Kapat");
    m_btnCloseLosing.Color(clrWhite);
    m_btnCloseLosing.ColorBackground(C'120,0,0');
    m_btnCloseLosing.FontSize(9);
    Add(m_btnCloseLosing);
    
    btn_y += BUTTON_HEIGHT + 10;
    
    // Hepsini Kapat butonu
    m_btnCloseAll.Create(m_chart_id, "BtnAll", m_subwin, MARGIN, btn_y, MARGIN + 2*BUTTON_WIDTH + 10, btn_y + BUTTON_HEIGHT);
    m_btnCloseAll.Text("🚫 Hepsini Kapat");
    m_btnCloseAll.Color(clrWhite);
    m_btnCloseAll.ColorBackground(C'120,120,0');
    m_btnCloseAll.FontSize(9);
    Add(m_btnCloseAll);
    
    y_pos = btn_y + BUTTON_HEIGHT + 15;
    
    // Pozisyon listesi
    for(int i = 0; i < 5; i++)
    {
        m_lblPositionList[i].Create(m_chart_id, "Pos" + IntegerToString(i), m_subwin, 
                                   MARGIN, y_pos + i * 16, PANEL_WIDTH - MARGIN, y_pos + (i + 1) * 16);
        m_lblPositionList[i].Text("");
        m_lblPositionList[i].Color(clrLightGray);
        m_lblPositionList[i].FontSize(8);
        Add(m_lblPositionList[i]);
    }
}

//+------------------------------------------------------------------+
//| Verileri güncelle                                               |
//+------------------------------------------------------------------+
void TradeManagerDialog::UpdateData()
{
    int positionCount = 0;
    double totalVolume = 0;
    double totalProfit = 0;
    int profitableCount = 0;
    int losingCount = 0;
    
    // Pozisyon listesini temizle
    for(int i = 0; i < 5; i++)
    {
        m_lblPositionList[i].Text("");
    }
    
    // Açık pozisyonları tara
    int displayCount = 0;
    for(int i = 0; i < PositionsTotal() && displayCount < 5; i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;
            
        ulong ticket = PositionGetInteger(POSITION_TICKET);
        ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);
        
        positionCount++;
        totalVolume += volume;
        totalProfit += profit;
        
        if(profit > 0)
            profitableCount++;
        else if(profit < 0)
            losingCount++;
        
        // Pozisyon bilgisini göster
        string typeStr = (type == POSITION_TYPE_BUY) ? "BUY" : "SELL";
        string profitStr = (profit >= 0) ? "+" + DoubleToString(profit, 2) : DoubleToString(profit, 2);
        color profitColor = (profit >= 0) ? clrLimeGreen : clrTomato;
        
        string posText = StringFormat("%s %s %.2f | $%s", 
                                    typeStr, 
                                    symbol, 
                                    volume, 
                                    profitStr);
        
        m_lblPositionList[displayCount].Text(posText);
        m_lblPositionList[displayCount].Color(profitColor);
        displayCount++;
    }
    
    // Eğer daha fazla pozisyon varsa göster
    if(PositionsTotal() > 5)
    {
        string moreText = "... ve " + IntegerToString(PositionsTotal() - 5) + " pozisyon daha";
        if(displayCount < 5)
        {
            m_lblPositionList[displayCount].Text(moreText);
            m_lblPositionList[displayCount].Color(clrGray);
        }
    }
    
    // Günlük P&L hesapla
    double dailyPL = CalculateDailyPL();
    
    // UI'yi güncelle
    string dailyText = "💰 Günlük P&L: $" + DoubleToString(dailyPL, 2);
    m_lblDailyPL.Text(dailyText);
    m_lblDailyPL.Color((dailyPL >= 0) ? clrLimeGreen : clrTomato);
    
    m_lblPositions.Text("📈 Açık Pozisyon: " + IntegerToString(positionCount));
    m_lblVolume.Text("📊 Toplam Hacim: " + DoubleToString(totalVolume, 2));
    
    string totalPLText = "💵 Toplam P&L: $" + DoubleToString(totalProfit, 2);
    m_lblTotalPL.Text(totalPLText);
    m_lblTotalPL.Color((totalProfit >= 0) ? clrLimeGreen : clrTomato);
    
    m_lblCounts.Text("📊 Karlı: " + IntegerToString(profitableCount) + " | Zararlı: " + IntegerToString(losingCount));
}

//+------------------------------------------------------------------+
//| Günlük P&L hesapla                                             |
//+------------------------------------------------------------------+
double TradeManagerDialog::CalculateDailyPL()
{
    double dailyPL = 0;
    datetime todayStart = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    
    HistorySelect(todayStart, TimeCurrent());
    
    for(int i = 0; i < HistoryDealsTotal(); i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket > 0)
        {
            ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);
            if(dealType == DEAL_TYPE_BUY || dealType == DEAL_TYPE_SELL)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                double commission = HistoryDealGetDouble(ticket, DEAL_COMMISSION);
                double swap = HistoryDealGetDouble(ticket, DEAL_SWAP);
                
                dailyPL += (profit + commission + swap);
            }
        }
    }
    
    // Açık pozisyonların kar/zararını da ekle
    for(int i = 0; i < PositionsTotal(); i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol != "")
        {
            dailyPL += PositionGetDouble(POSITION_PROFIT);
        }
    }
    
    return dailyPL;
}

//+------------------------------------------------------------------+
//| Tick olayı                                                      |
//+------------------------------------------------------------------+
void TradeManagerDialog::OnTick()
{
    // Her 3 saniyede bir güncelle
    if(TimeCurrent() - m_lastUpdate >= 3)
    {
        UpdateData();
        m_lastUpdate = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Olay işleyici                                                   |
//+------------------------------------------------------------------+
bool TradeManagerDialog::OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == "BtnProfit")
        {
            CloseProfitablePositions();
            return true;
        }
        else if(sparam == "BtnLoss")
        {
            CloseLosingPositions();
            return true;
        }
        else if(sparam == "BtnAll")
        {
            CloseAllPositions();
            return true;
        }
    }
    
    return CDialog::OnEvent(id, lparam, dparam, sparam);
}

//+------------------------------------------------------------------+
//| Karlı pozisyonları kapat                                        |
//+------------------------------------------------------------------+
void TradeManagerDialog::CloseProfitablePositions()
{
    CTrade trade;
    int closedCount = 0;

    Print("💚 Karlı pozisyonlar kapatılıyor...");

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double profit = PositionGetDouble(POSITION_PROFIT);
        if(profit > 0)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(trade.PositionClose(ticket))
            {
                closedCount++;
                Print("✅ Karlı pozisyon kapatıldı: ", ticket, " Kar: $", DoubleToString(profit, 2));
            }
            else
            {
                Print("❌ Pozisyon kapatma hatası: ", ticket, " Hata: ", GetLastError());
            }
        }
    }

    Print("💚 Toplam ", closedCount, " karlı pozisyon kapatıldı");
    UpdateData();
}

//+------------------------------------------------------------------+
//| Zararlı pozisyonları kapat                                      |
//+------------------------------------------------------------------+
void TradeManagerDialog::CloseLosingPositions()
{
    CTrade trade;
    int closedCount = 0;

    Print("❤️ Zararlı pozisyonlar kapatılıyor...");

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double profit = PositionGetDouble(POSITION_PROFIT);
        if(profit < 0)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(trade.PositionClose(ticket))
            {
                closedCount++;
                Print("✅ Zararlı pozisyon kapatıldı: ", ticket, " Zarar: $", DoubleToString(profit, 2));
            }
            else
            {
                Print("❌ Pozisyon kapatma hatası: ", ticket, " Hata: ", GetLastError());
            }
        }
    }

    Print("❤️ Toplam ", closedCount, " zararlı pozisyon kapatıldı");
    UpdateData();
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void TradeManagerDialog::CloseAllPositions()
{
    CTrade trade;
    int closedCount = 0;

    Print("🚫 Tüm pozisyonlar kapatılıyor...");

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        double profit = PositionGetDouble(POSITION_PROFIT);

        if(trade.PositionClose(ticket))
        {
            closedCount++;
            Print("✅ Pozisyon kapatıldı: ", ticket, " P&L: $", DoubleToString(profit, 2));
        }
        else
        {
            Print("❌ Pozisyon kapatma hatası: ", ticket, " Hata: ", GetLastError());
        }
    }

    Print("🚫 Toplam ", closedCount, " pozisyon kapatıldı");
    UpdateData();
}

//+------------------------------------------------------------------+
//| Global değişkenler                                              |
//+------------------------------------------------------------------+
TradeManagerDialog* g_tradeDialog = NULL;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Windows penceresi gibi dialog oluştur
    g_tradeDialog = new TradeManagerDialog();

    if(!g_tradeDialog.Create(0, "WindowsStyleTradePanel", 0, 50, 50))
    {
        Print("❌ Windows Style Trade Panel oluşturulamadı!");
        delete g_tradeDialog;
        g_tradeDialog = NULL;
        return INIT_FAILED;
    }

    Print("✅ Windows Style Trade Panel başlatıldı - Başlık çubuğundan sürükleyin!");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(g_tradeDialog != NULL)
    {
        g_tradeDialog.Destroy(reason);
        delete g_tradeDialog;
        g_tradeDialog = NULL;
    }

    Print("🔄 Windows Style Trade Panel kapatıldı");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(g_tradeDialog != NULL)
    {
        g_tradeDialog.OnTick();
    }
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(g_tradeDialog != NULL)
    {
        g_tradeDialog.OnEvent(id, lparam, dparam, sparam);
    }
}
