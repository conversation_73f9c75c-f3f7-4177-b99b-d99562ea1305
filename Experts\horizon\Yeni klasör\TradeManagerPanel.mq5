//+------------------------------------------------------------------+
//| TradeManagerPanel.mq5 - Sürüklenebilir İşlem Yönetim Paneli    |
//| MT5 İşlem sekmesi verilerini kullanarak panel oluşturur         |
//+------------------------------------------------------------------+
#property copyright "Horizon Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Sürüklenebilir İşlem Yönetim Paneli"

#include <Controls\Dialog.mqh>
#include <Controls\Label.mqh>
#include <Controls\Button.mqh>
#include <Controls\ListView.mqh>
#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| Panel boyutları ve renkler                                      |
//+------------------------------------------------------------------+
#define PANEL_WIDTH     400
#define PANEL_HEIGHT    500
#define BUTTON_WIDTH    120
#define BUTTON_HEIGHT   30
#define ROW_HEIGHT      25
#define MARGIN          10

//+------------------------------------------------------------------+
//| TradeManagerDialog sınıfı                                       |
//+------------------------------------------------------------------+
class TradeManagerDialog : public CDialog
{
private:
    // Panel elementleri
    CLabel            m_title;
    CLabel            m_dailyPL;
    CLabel            m_totalPositions;
    CLabel            m_totalVolume;
    CLabel            m_totalProfit;
    
    // Butonlar
    CButton           m_btnCloseProfitable;
    CButton           m_btnCloseLosing;
    CButton           m_btnCloseAll;
    
    // İşlem listesi için label'lar
    CLabel            m_positionLabels[20];  // Maksimum 20 işlem göster
    
    // Panel durumu
    bool              m_isDragging;
    int               m_dragStartX;
    int               m_dragStartY;
    
    // Veriler
    double            m_dailyProfitLoss;
    int               m_positionCount;
    double            m_totalVol;
    double            m_totalPL;
    
public:
    TradeManagerDialog();
    ~TradeManagerDialog();
    
    virtual bool      Create(const long chart, const string name, const int subwin, const int x1, const int y1);
    virtual void      OnTick();
    virtual bool      OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam);
    
    // Panel yönetimi
    void              UpdateTradeData();
    void              UpdateDisplay();
    void              CreateElements();
    
    // İşlem yönetimi
    void              CloseProfitablePositions();
    void              CloseLosingPositions();
    void              CloseAllPositions();
    
    // Günlük P&L hesaplama
    double            CalculateDailyPL();
    
    // Mouse olayları
    virtual bool      OnMouseEvent(const int id, const long &lparam, const double &dparam, const string &sparam);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
TradeManagerDialog::TradeManagerDialog()
{
    m_isDragging = false;
    m_dragStartX = 0;
    m_dragStartY = 0;
    m_dailyProfitLoss = 0;
    m_positionCount = 0;
    m_totalVol = 0;
    m_totalPL = 0;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
TradeManagerDialog::~TradeManagerDialog()
{
}

//+------------------------------------------------------------------+
//| Panel oluştur                                                   |
//+------------------------------------------------------------------+
bool TradeManagerDialog::Create(const long chart, const string name, const int subwin, const int x1, const int y1)
{
    if(!CDialog::Create(chart, name, subwin, x1, y1, x1 + PANEL_WIDTH, y1 + PANEL_HEIGHT))
        return false;
    
    // Panel özelliklerini ayarla
    Background(C'25,25,25');        // Koyu gri arka plan
    BorderType(BORDER_FLAT);
    Color(clrWhite);
    
    // Panel her zaman en üstte kalsın
    SetInteger(OBJPROP_ZORDER, 1000);
    
    CreateElements();
    UpdateTradeData();
    UpdateDisplay();
    
    return true;
}

//+------------------------------------------------------------------+
//| Panel elementlerini oluştur                                     |
//+------------------------------------------------------------------+
void TradeManagerDialog::CreateElements()
{
    int y_pos = MARGIN;
    
    // Başlık
    m_title.Create(m_chart_id, "TMPanel_Title", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 25);
    m_title.Text("📊 İşlem Yönetim Paneli");
    m_title.Color(clrWhite);
    m_title.FontSize(12);
    Add(m_title);
    y_pos += 35;
    
    // Günlük P&L
    m_dailyPL.Create(m_chart_id, "TMPanel_DailyPL", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 20);
    m_dailyPL.Text("💰 Günlük P&L: $0.00");
    m_dailyPL.Color(clrYellow);
    Add(m_dailyPL);
    y_pos += 25;
    
    // Toplam pozisyon sayısı
    m_totalPositions.Create(m_chart_id, "TMPanel_TotalPos", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 20);
    m_totalPositions.Text("📈 Açık Pozisyon: 0");
    m_totalPositions.Color(clrLightBlue);
    Add(m_totalPositions);
    y_pos += 25;
    
    // Toplam hacim
    m_totalVolume.Create(m_chart_id, "TMPanel_TotalVol", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 20);
    m_totalVolume.Text("📊 Toplam Hacim: 0.00");
    m_totalVolume.Color(clrLightGray);
    Add(m_totalVolume);
    y_pos += 25;
    
    // Toplam kar/zarar
    m_totalProfit.Create(m_chart_id, "TMPanel_TotalPL", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 20);
    m_totalProfit.Text("💵 Toplam P&L: $0.00");
    m_totalProfit.Color(clrWhite);
    Add(m_totalProfit);
    y_pos += 35;
    
    // Butonlar
    int btn_y = y_pos;
    
    // Karlı Kapat butonu
    m_btnCloseProfitable.Create(m_chart_id, "TMPanel_BtnProfit", m_subwin, MARGIN, btn_y, MARGIN + BUTTON_WIDTH, btn_y + BUTTON_HEIGHT);
    m_btnCloseProfitable.Text("💚 Karlı Kapat");
    m_btnCloseProfitable.Color(clrWhite);
    m_btnCloseProfitable.ColorBackground(C'0,128,0');
    Add(m_btnCloseProfitable);
    
    // Zarar Kapat butonu
    m_btnCloseLosing.Create(m_chart_id, "TMPanel_BtnLoss", m_subwin, MARGIN + BUTTON_WIDTH + 5, btn_y, MARGIN + 2*BUTTON_WIDTH + 5, btn_y + BUTTON_HEIGHT);
    m_btnCloseLosing.Text("❤️ Zarar Kapat");
    m_btnCloseLosing.Color(clrWhite);
    m_btnCloseLosing.ColorBackground(C'128,0,0');
    Add(m_btnCloseLosing);
    
    btn_y += BUTTON_HEIGHT + 10;
    
    // Hepsini Kapat butonu
    m_btnCloseAll.Create(m_chart_id, "TMPanel_BtnAll", m_subwin, MARGIN, btn_y, MARGIN + 2*BUTTON_WIDTH + 5, btn_y + BUTTON_HEIGHT);
    m_btnCloseAll.Text("🚫 Hepsini Kapat");
    m_btnCloseAll.Color(clrWhite);
    m_btnCloseAll.ColorBackground(C'128,128,0');
    Add(m_btnCloseAll);
    
    y_pos = btn_y + BUTTON_HEIGHT + 20;
    
    // Pozisyon listesi başlığı
    CLabel* listTitle = new CLabel();
    listTitle.Create(m_chart_id, "TMPanel_ListTitle", m_subwin, MARGIN, y_pos, PANEL_WIDTH - MARGIN, y_pos + 20);
    listTitle.Text("📋 Açık Pozisyonlar:");
    listTitle.Color(clrYellow);
    Add(listTitle);
    y_pos += 25;
    
    // Pozisyon listesi için label'lar oluştur
    for(int i = 0; i < 20; i++)
    {
        m_positionLabels[i].Create(m_chart_id, "TMPanel_Pos" + IntegerToString(i), m_subwin, 
                                  MARGIN, y_pos + i * ROW_HEIGHT, PANEL_WIDTH - MARGIN, y_pos + (i + 1) * ROW_HEIGHT);
        m_positionLabels[i].Text("");
        m_positionLabels[i].Color(clrLightGray);
        m_positionLabels[i].FontSize(8);
        Add(m_positionLabels[i]);
    }
}

//+------------------------------------------------------------------+
//| İşlem verilerini güncelle                                       |
//+------------------------------------------------------------------+
void TradeManagerDialog::UpdateTradeData()
{
    m_positionCount = 0;
    m_totalVol = 0;
    m_totalPL = 0;
    
    // Tüm pozisyon label'larını temizle
    for(int i = 0; i < 20; i++)
    {
        m_positionLabels[i].Text("");
    }
    
    // Açık pozisyonları tara
    for(int i = 0; i < PositionsTotal(); i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;
            
        ulong ticket = PositionGetInteger(POSITION_TICKET);
        ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);
        double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
        double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
        
        m_totalVol += volume;
        m_totalPL += profit;
        
        // Pozisyon bilgisini label'a yaz
        if(m_positionCount < 20)
        {
            string typeStr = (type == POSITION_TYPE_BUY) ? "BUY" : "SELL";
            string profitStr = (profit >= 0) ? "+" + DoubleToString(profit, 2) : DoubleToString(profit, 2);
            color profitColor = (profit >= 0) ? clrLimeGreen : clrTomato;
            
            string posText = StringFormat("%s %s %.2f | $%s", 
                                        typeStr, 
                                        symbol, 
                                        volume, 
                                        profitStr);
            
            m_positionLabels[m_positionCount].Text(posText);
            m_positionLabels[m_positionCount].Color(profitColor);
        }
        
        m_positionCount++;
    }
    
    // Günlük P&L hesapla
    m_dailyProfitLoss = CalculateDailyPL();
}

//+------------------------------------------------------------------+
//| Ekranı güncelle                                                 |
//+------------------------------------------------------------------+
void TradeManagerDialog::UpdateDisplay()
{
    // Günlük P&L
    string dailyText = "💰 Günlük P&L: $" + DoubleToString(m_dailyProfitLoss, 2);
    m_dailyPL.Text(dailyText);
    m_dailyPL.Color((m_dailyProfitLoss >= 0) ? clrLimeGreen : clrTomato);
    
    // Toplam pozisyon
    m_totalPositions.Text("📈 Açık Pozisyon: " + IntegerToString(m_positionCount));
    
    // Toplam hacim
    m_totalVolume.Text("📊 Toplam Hacim: " + DoubleToString(m_totalVol, 2));
    
    // Toplam P&L
    string totalPLText = "💵 Toplam P&L: $" + DoubleToString(m_totalPL, 2);
    m_totalProfit.Text(totalPLText);
    m_totalProfit.Color((m_totalPL >= 0) ? clrLimeGreen : clrTomato);
}

//+------------------------------------------------------------------+
//| Günlük P&L hesapla                                             |
//+------------------------------------------------------------------+
double TradeManagerDialog::CalculateDailyPL()
{
    double dailyPL = 0;
    datetime todayStart = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));
    
    // Bugünkü kapatılan işlemleri kontrol et
    HistorySelect(todayStart, TimeCurrent());
    
    for(int i = 0; i < HistoryDealsTotal(); i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket > 0)
        {
            ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);
            if(dealType == DEAL_TYPE_BUY || dealType == DEAL_TYPE_SELL)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                double commission = HistoryDealGetDouble(ticket, DEAL_COMMISSION);
                double swap = HistoryDealGetDouble(ticket, DEAL_SWAP);
                
                dailyPL += (profit + commission + swap);
            }
        }
    }
    
    // Açık pozisyonların kar/zararını da ekle
    dailyPL += m_totalPL;
    
    return dailyPL;
}

//+------------------------------------------------------------------+
//| Tick olayı                                                      |
//+------------------------------------------------------------------+
void TradeManagerDialog::OnTick()
{
    static datetime lastUpdate = 0;
    
    // Her 1 saniyede bir güncelle
    if(TimeCurrent() - lastUpdate >= 1)
    {
        UpdateTradeData();
        UpdateDisplay();
        lastUpdate = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Olay işleyici                                                   |
//+------------------------------------------------------------------+
bool TradeManagerDialog::OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == "TMPanel_BtnProfit")
        {
            CloseProfitablePositions();
            return true;
        }
        else if(sparam == "TMPanel_BtnLoss")
        {
            CloseLosingPositions();
            return true;
        }
        else if(sparam == "TMPanel_BtnAll")
        {
            CloseAllPositions();
            return true;
        }
    }
    
    return CDialog::OnEvent(id, lparam, dparam, sparam);
}

//+------------------------------------------------------------------+
//| Karlı pozisyonları kapat                                        |
//+------------------------------------------------------------------+
void TradeManagerDialog::CloseProfitablePositions()
{
    CTrade trade;
    int closedCount = 0;

    Print("💚 Karlı pozisyonlar kapatılıyor...");

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double profit = PositionGetDouble(POSITION_PROFIT);
        if(profit > 0)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(trade.PositionClose(ticket))
            {
                closedCount++;
                Print("✅ Karlı pozisyon kapatıldı: ", ticket, " Kar: $", DoubleToString(profit, 2));
            }
            else
            {
                Print("❌ Pozisyon kapatma hatası: ", ticket, " Hata: ", GetLastError());
            }
        }
    }

    Print("💚 Toplam ", closedCount, " karlı pozisyon kapatıldı");
}

//+------------------------------------------------------------------+
//| Zararlı pozisyonları kapat                                      |
//+------------------------------------------------------------------+
void TradeManagerDialog::CloseLosingPositions()
{
    CTrade trade;
    int closedCount = 0;

    Print("❤️ Zararlı pozisyonlar kapatılıyor...");

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double profit = PositionGetDouble(POSITION_PROFIT);
        if(profit < 0)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(trade.PositionClose(ticket))
            {
                closedCount++;
                Print("✅ Zararlı pozisyon kapatıldı: ", ticket, " Zarar: $", DoubleToString(profit, 2));
            }
            else
            {
                Print("❌ Pozisyon kapatma hatası: ", ticket, " Hata: ", GetLastError());
            }
        }
    }

    Print("❤️ Toplam ", closedCount, " zararlı pozisyon kapatıldı");
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void TradeManagerDialog::CloseAllPositions()
{
    CTrade trade;
    int closedCount = 0;

    Print("🚫 Tüm pozisyonlar kapatılıyor...");

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        double profit = PositionGetDouble(POSITION_PROFIT);

        if(trade.PositionClose(ticket))
        {
            closedCount++;
            Print("✅ Pozisyon kapatıldı: ", ticket, " P&L: $", DoubleToString(profit, 2));
        }
        else
        {
            Print("❌ Pozisyon kapatma hatası: ", ticket, " Hata: ", GetLastError());
        }
    }

    Print("🚫 Toplam ", closedCount, " pozisyon kapatıldı");
}

//+------------------------------------------------------------------+
//| Mouse olayları - Sürükleme desteği                             |
//+------------------------------------------------------------------+
bool TradeManagerDialog::OnMouseEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_MOUSE_MOVE)
    {
        int x = (int)lparam;
        int y = (int)dparam;
        int flags = (int)sparam;

        // Sol mouse tuşu basılı ve sürükleme başlatılmış
        if((flags & 1) && m_isDragging)
        {
            int deltaX = x - m_dragStartX;
            int deltaY = y - m_dragStartY;

            // Panel pozisyonunu güncelle
            int newX = Left() + deltaX;
            int newY = Top() + deltaY;

            // Ekran sınırları içinde tut
            if(newX < 0) newX = 0;
            if(newY < 0) newY = 0;
            if(newX + Width() > (int)ChartGetInteger(m_chart_id, CHART_WIDTH_IN_PIXELS))
                newX = (int)ChartGetInteger(m_chart_id, CHART_WIDTH_IN_PIXELS) - Width();
            if(newY + Height() > (int)ChartGetInteger(m_chart_id, CHART_HEIGHT_IN_PIXELS))
                newY = (int)ChartGetInteger(m_chart_id, CHART_HEIGHT_IN_PIXELS) - Height();

            Move(newX, newY);

            m_dragStartX = x;
            m_dragStartY = y;

            return true;
        }
    }
    else if(id == CHARTEVENT_MOUSE_WHEEL)
    {
        // Mouse wheel ile panel boyutunu değiştir (opsiyonel)
        return true;
    }

    return CDialog::OnMouseEvent(id, lparam, dparam, sparam);
}

//+------------------------------------------------------------------+
//| Global değişkenler                                              |
//+------------------------------------------------------------------+
TradeManagerDialog* g_tradePanel = NULL;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Panel oluştur
    g_tradePanel = new TradeManagerDialog();

    if(!g_tradePanel.Create(0, "TradeManagerPanel", 0, 50, 50))
    {
        Print("❌ Trade Manager Panel oluşturulamadı!");
        delete g_tradePanel;
        g_tradePanel = NULL;
        return INIT_FAILED;
    }

    Print("✅ Trade Manager Panel başlatıldı");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(g_tradePanel != NULL)
    {
        g_tradePanel.Destroy(reason);
        delete g_tradePanel;
        g_tradePanel = NULL;
    }

    Print("🔄 Trade Manager Panel kapatıldı");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(g_tradePanel != NULL)
    {
        g_tradePanel.OnTick();
    }
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(g_tradePanel != NULL)
    {
        g_tradePanel.OnEvent(id, lparam, dparam, sparam);
    }
}
