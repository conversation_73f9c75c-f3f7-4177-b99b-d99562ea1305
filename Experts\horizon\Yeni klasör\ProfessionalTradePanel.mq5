//+------------------------------------------------------------------+
//|                     Professional Trade Panel                    |
//|      <PERSON>, SL/TP, <PERSON>, Taşınabilir Panel         |
//+------------------------------------------------------------------+
#include <Trade\Trade.mqh>
CTrade trade;

input int      magicNumber     = 123456;
input ENUM_TIMEFRAMES tf       = PERIOD_CURRENT;
input color    panelColor      = clrDarkSlateGray;
input int      startPanelX     = 20;
input int      startPanelY     = 20;
input int      panelWidth      = 220;
input int      panelHeight     = 280;

string panelName = "trade_panel";
string sym;
double lotSize = 0.1;
double slPoints = 200;
double tpPoints = 400;

// Global değişkenler - sürükleme için
int panelX = 20;
int panelY = 20;

bool dragging = false;
int dragOffsetX, dragOffsetY;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
   sym = _Symbol;
   panelX = startPanelX;
   panelY = startPanelY;
   CreatePanel();
   EventSetTimer(1);
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   ObjectsDeleteAll(0, panelName);
   EventKillTimer();
}

//+------------------------------------------------------------------+
void OnTimer() {
   // isteğe bağlı olarak fiyatları güncelleyebilirsin
}

//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam) {
   if (id == CHARTEVENT_OBJECT_CLICK) {
      if (sparam == panelName + "_buy") Buy();
      if (sparam == panelName + "_sell") Sell();
      if (sparam == panelName + "_close") ClosePositions();
      if (sparam == panelName + "_exit") ObjectsDeleteAll(0, panelName);
   }
   else if (id == CHARTEVENT_OBJECT_DRAG) {
      if (sparam == panelName + "_bg") {
         // Panel arka planı sürüklendiğinde
         int newX = (int)ObjectGetInteger(0, panelName + "_bg", OBJPROP_XDISTANCE);
         int newY = (int)ObjectGetInteger(0, panelName + "_bg", OBJPROP_YDISTANCE);

         // Pozisyon değişikliğini hesapla
         int deltaX = newX - panelX;
         int deltaY = newY - panelY;

         // Yeni pozisyonu kaydet
         panelX = newX;
         panelY = newY;

         // Tüm panel elementlerini yeni pozisyona taşı
         UpdatePanelPositions(deltaX, deltaY);
      }
   }
}

//+------------------------------------------------------------------+
//| Panel elementlerinin pozisyonlarını güncelle                    |
//+------------------------------------------------------------------+
void UpdatePanelPositions(int deltaX, int deltaY) {
   // Tüm panel elementlerini yeni pozisyona taşı
   string elements[] = {
      panelName + "_buy",
      panelName + "_sell",
      panelName + "_close",
      panelName + "_exit",
      panelName + "_lot",
      panelName + "_sl",
      panelName + "_tp",
      panelName + "_lot_lbl",
      panelName + "_sl_lbl",
      panelName + "_tp_lbl"
   };

   for(int i = 0; i < ArraySize(elements); i++) {
      if(ObjectFind(0, elements[i]) >= 0) {
         int currentX = (int)ObjectGetInteger(0, elements[i], OBJPROP_XDISTANCE);
         int currentY = (int)ObjectGetInteger(0, elements[i], OBJPROP_YDISTANCE);

         ObjectSetInteger(0, elements[i], OBJPROP_XDISTANCE, currentX + deltaX);
         ObjectSetInteger(0, elements[i], OBJPROP_YDISTANCE, currentY + deltaY);
      }
   }

   ChartRedraw();
}

//+------------------------------------------------------------------+
void CreatePanel() {
   // Panel Arka Planı
   string bg = panelName + "_bg";
   CreateRect(bg, panelX, panelY, panelWidth, panelHeight, panelColor);
   ObjectSetInteger(0, bg, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, bg, OBJPROP_SELECTABLE, true);
   ObjectSetInteger(0, bg, OBJPROP_SELECTED, false);

   CreateButton(panelName + "_buy",     panelX + 10,  panelY + 10,  90, 30, "BUY", clrLime);
   CreateButton(panelName + "_sell",    panelX + 120, panelY + 10,  90, 30, "SELL", clrTomato);
   CreateButton(panelName + "_close",   panelX + 10,  panelY + 50, 200, 30, "TÜMÜNÜ KAPAT", clrDarkOrange);
   CreateButton(panelName + "_exit",    panelX + 10,  panelY + 90, 200, 30, "PANELİ KAPAT", clrDimGray);

   CreateEdit(panelName + "_lot",  panelX + 10,  panelY + 150, 200, 25, DoubleToString(lotSize, 2));
   CreateEdit(panelName + "_sl",   panelX + 10,  panelY + 190, 200, 25, IntegerToString((int)slPoints));
   CreateEdit(panelName + "_tp",   panelX + 10,  panelY + 230, 200, 25, IntegerToString((int)tpPoints));

   CreateLabel(panelName + "_lot_lbl", panelX + 10,  panelY + 150 - 15, "LOT:");
   CreateLabel(panelName + "_sl_lbl",  panelX + 10,  panelY + 190 - 15, "SL (puan):");
   CreateLabel(panelName + "_tp_lbl",  panelX + 10,  panelY + 230 - 15, "TP (puan):");
}

//+------------------------------------------------------------------+
void CreateButton(string name, int x, int y, int w, int h, string label, color clr) {
   if (!ObjectCreate(0, name, OBJ_BUTTON, 0, 0, 0)) return;
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_XSIZE, w);
   ObjectSetInteger(0, name, OBJPROP_YSIZE, h);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 10);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
   ObjectSetString(0, name, OBJPROP_TEXT, label);
}

void CreateRect(string name, int x, int y, int w, int h, color clr) {
   ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_XSIZE, w);
   ObjectSetInteger(0, name, OBJPROP_YSIZE, h);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
   ObjectSetInteger(0, name, OBJPROP_BACK, true);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

void CreateEdit(string name, int x, int y, int w, int h, string text) {
   ObjectCreate(0, name, OBJ_EDIT, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_XSIZE, w);
   ObjectSetInteger(0, name, OBJPROP_YSIZE, h);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 10);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
}

void CreateLabel(string name, int x, int y, string text) {
   ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
}

//+------------------------------------------------------------------+
void Buy() {
   lotSize = StringToDouble(ObjectGetString(0, panelName + "_lot", OBJPROP_TEXT));
   slPoints = StringToDouble(ObjectGetString(0, panelName + "_sl", OBJPROP_TEXT));
   tpPoints = StringToDouble(ObjectGetString(0, panelName + "_tp", OBJPROP_TEXT));
   double price = SymbolInfoDouble(sym, SYMBOL_ASK);
   double sl = price - slPoints * _Point;
   double tp = price + tpPoints * _Point;
   trade.Buy(lotSize, sym, price, sl, tp, NULL);
}

void Sell() {
   lotSize = StringToDouble(ObjectGetString(0, panelName + "_lot", OBJPROP_TEXT));
   slPoints = StringToDouble(ObjectGetString(0, panelName + "_sl", OBJPROP_TEXT));
   tpPoints = StringToDouble(ObjectGetString(0, panelName + "_tp", OBJPROP_TEXT));
   double price = SymbolInfoDouble(sym, SYMBOL_BID);
   double sl = price + slPoints * _Point;
   double tp = price - tpPoints * _Point;
   trade.Sell(lotSize, sym, price, sl, tp, NULL);
}

void ClosePositions() {
   for (int i = PositionsTotal() - 1; i >= 0; i--) {
      ulong ticket = PositionGetTicket(i);
      if (PositionGetString(POSITION_SYMBOL) == sym) {
         trade.PositionClose(ticket);
      }
   }
}
