//+------------------------------------------------------------------+
//| Simple MA Crossover EA                                           |
//| MA7/MA50 kesişimi ile giri<PERSON>, ters kesişim ile çı<PERSON>             |
//+------------------------------------------------------------------+
#property copyright "Horizon Trading"
#property version   "1.00"

#include <Trade\Trade.mqh>
CTrade trade;

//--- Input parametreleri
input group "=== MA Ayarları ==="
input int                   MA_Fast = 7;                           // Hızlı MA Periyodu (MA7)
input int                   MA_Mid = 10;                           // Orta MA Periyodu (MA10)
input int                   MA_Slow = 50;                          // Yavaş MA Periyodu (MA50 - Trend Filtresi)
input ENUM_MA_METHOD        MA_Method = MODE_SMA;                  // MA Hesaplama Metodu
input ENUM_APPLIED_PRICE    MA_Applied_Price = PRICE_CLOSE;        // MA Uygulanan Fiyat

input group "=== Risk Yönetimi ==="
input double                Lot_Size = 0.01;                      // Lot Büyüklüğü
input double                ATR_Multiplier = 2.0;                 // ATR Çarpanı (SL için)
input int                   ATR_Period = 14;                      // ATR Periyodu
input double                Risk_Percent = 2.0;                   // Risk Yüzdesi (% bakiye)

input group "=== Genel Ayarlar ==="
input int                   Magic_Number = 8888;                  // Magic Number
input string                Comment_Prefix = "Simple_MA";         // Yorum Ön Eki
input bool                  Use_ATR_SL = true;                    // ATR bazlı SL kullan
input bool                  Close_Opposite = true;                // Ters sinyal geldiğinde kapat

//--- Global değişkenler
int ma_fast_handle;
int ma_mid_handle;
int ma_slow_handle;
int atr_handle;
double ma_fast_buffer[];
double ma_mid_buffer[];
double ma_slow_buffer[];
double atr_buffer[];
int debug_log_number = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // MA handle'larını oluştur
    ma_fast_handle = iMA(_Symbol, _Period, MA_Fast, 0, MA_Method, MA_Applied_Price);
    ma_mid_handle = iMA(_Symbol, _Period, MA_Mid, 0, MA_Method, MA_Applied_Price);
    ma_slow_handle = iMA(_Symbol, _Period, MA_Slow, 0, MA_Method, MA_Applied_Price);
    atr_handle = iATR(_Symbol, _Period, ATR_Period);

    if(ma_fast_handle == INVALID_HANDLE || ma_mid_handle == INVALID_HANDLE ||
       ma_slow_handle == INVALID_HANDLE || atr_handle == INVALID_HANDLE)
    {
        Print("❌ Indicator handle oluşturulamadı!");
        return INIT_FAILED;
    }

    // Array ayarları
    ArraySetAsSeries(ma_fast_buffer, true);
    ArraySetAsSeries(ma_mid_buffer, true);
    ArraySetAsSeries(ma_slow_buffer, true);
    ArraySetAsSeries(atr_buffer, true);

    debug_log_number = 0;

    Print("✅ Simple MA Crossover EA başlatıldı - Magic: ", Magic_Number);
    Print("📊 MA", MA_Fast, "/MA", MA_Mid, " kesişim + MA", MA_Slow, " trend filtresi aktif");

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("🔄 Simple MA Crossover EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Yeterli bar kontrolü
    if(Bars(_Symbol, _Period) < MA_Slow + 10)
        return;

    // Indicator verilerini al
    if(CopyBuffer(ma_fast_handle, 0, 0, 3, ma_fast_buffer) < 3 ||
       CopyBuffer(ma_mid_handle, 0, 0, 3, ma_mid_buffer) < 3 ||
       CopyBuffer(ma_slow_handle, 0, 0, 3, ma_slow_buffer) < 3 ||
       CopyBuffer(atr_handle, 0, 0, 2, atr_buffer) < 2)
    {
        return;
    }

    // MA değerleri
    double ma_fast_current = ma_fast_buffer[0];
    double ma_fast_prev = ma_fast_buffer[1];
    double ma_mid_current = ma_mid_buffer[0];
    double ma_mid_prev = ma_mid_buffer[1];
    double ma_slow_current = ma_slow_buffer[0];
    double atr_current = atr_buffer[0];

    // Güncel fiyat
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // Trend filtresi (MA50)
    bool bullish_trend = current_price > ma_slow_current; // Fiyat MA50 üzerinde
    bool bearish_trend = current_price < ma_slow_current; // Fiyat MA50 altında

    // Kesişim kontrolü (MA7/MA10)
    bool buy_crossover = (ma_fast_prev <= ma_mid_prev) && (ma_fast_current > ma_mid_current) && bullish_trend;
    bool sell_crossover = (ma_fast_prev >= ma_mid_prev) && (ma_fast_current < ma_mid_current) && bearish_trend;

    // Çıkış kesişimi (ters yön)
    bool buy_exit_crossover = (ma_fast_prev >= ma_mid_prev) && (ma_fast_current < ma_mid_current);
    bool sell_exit_crossover = (ma_fast_prev <= ma_mid_prev) && (ma_fast_current > ma_mid_current);

    // Mevcut pozisyon kontrolü
    bool has_buy_position = HasPosition(ORDER_TYPE_BUY);
    bool has_sell_position = HasPosition(ORDER_TYPE_SELL);
    
    // Debug - sadece kesişim olduğunda
    if(buy_crossover || sell_crossover || buy_exit_crossover || sell_exit_crossover)
    {
        debug_log_number++;
        Print("🔍 [", debug_log_number, "] KESİŞİM ALGILANDI:");
        Print("   MA", MA_Fast, "[1]: ", DoubleToString(ma_fast_prev, 5), " → MA", MA_Fast, "[0]: ", DoubleToString(ma_fast_current, 5));
        Print("   MA", MA_Mid, "[1]: ", DoubleToString(ma_mid_prev, 5), " → MA", MA_Mid, "[0]: ", DoubleToString(ma_mid_current, 5));
        Print("   MA", MA_Slow, " (Trend): ", DoubleToString(ma_slow_current, 5), " Fiyat: ", DoubleToString(current_price, 5));
        Print("   Trend - Bullish: ", bullish_trend, " Bearish: ", bearish_trend);
        Print("   Giriş - BUY: ", buy_crossover, " SELL: ", sell_crossover);
        Print("   Çıkış - BUY Exit: ", buy_exit_crossover, " SELL Exit: ", sell_exit_crossover);
        Print("   Mevcut Pozisyon - BUY: ", has_buy_position, " SELL: ", has_sell_position);
    }

    // Çıkış sinyalleri (önce çıkış kontrol et)
    if(has_buy_position && buy_exit_crossover)
    {
        CloseAllPositions(ORDER_TYPE_BUY);
        debug_log_number++;
        Print("🔵 [", debug_log_number, "] BUY pozisyonu kapatıldı (MA7 < MA10 kesişimi)");
    }

    if(has_sell_position && sell_exit_crossover)
    {
        CloseAllPositions(ORDER_TYPE_SELL);
        debug_log_number++;
        Print("🔵 [", debug_log_number, "] SELL pozisyonu kapatıldı (MA7 > MA10 kesişimi)");
    }

    // Giriş sinyalleri
    if(buy_crossover && !has_buy_position)
    {
        OpenBuyOrder(atr_current);
    }

    if(sell_crossover && !has_sell_position)
    {
        OpenSellOrder(atr_current);
    }
}

//+------------------------------------------------------------------+
//| BUY emri aç                                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder(double atr_value)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double ma_slow_current = ma_slow_buffer[0];
    double sl = 0, tp = 0;

    if(Use_ATR_SL)
    {
        sl = ask - (atr_value * ATR_Multiplier);
    }

    // Lot hesaplama
    double lot = CalculateLotSize(sl > 0 ? MathAbs(ask - sl) : atr_value * ATR_Multiplier);

    trade.SetExpertMagicNumber(Magic_Number);

    if(trade.Buy(lot, _Symbol, ask, sl, tp, Comment_Prefix + "_BUY"))
    {
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(ask, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   MA", MA_Fast, ": ", DoubleToString(ma_fast_buffer[0], 5), " > MA", MA_Mid, ": ", DoubleToString(ma_mid_buffer[0], 5));
        Print("   Trend: Fiyat ", DoubleToString(current_price, 5), " > MA", MA_Slow, " ", DoubleToString(ma_slow_current, 5));
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] BUY EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder(double atr_value)
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double ma_slow_current = ma_slow_buffer[0];
    double sl = 0, tp = 0;

    if(Use_ATR_SL)
    {
        sl = bid + (atr_value * ATR_Multiplier);
    }

    // Lot hesaplama
    double lot = CalculateLotSize(sl > 0 ? MathAbs(bid - sl) : atr_value * ATR_Multiplier);

    trade.SetExpertMagicNumber(Magic_Number);

    if(trade.Sell(lot, _Symbol, bid, sl, tp, Comment_Prefix + "_SELL"))
    {
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] SELL EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(bid, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   MA", MA_Fast, ": ", DoubleToString(ma_fast_buffer[0], 5), " < MA", MA_Mid, ": ", DoubleToString(ma_mid_buffer[0], 5));
        Print("   Trend: Fiyat ", DoubleToString(current_price, 5), " < MA", MA_Slow, " ", DoubleToString(ma_slow_current, 5));
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] SELL EMRİ HATASI: ", trade.ResultRetcode(), " - ", trade.ResultComment());
    }
}

//+------------------------------------------------------------------+
//| Lot büyüklüğü hesapla                                           |
//+------------------------------------------------------------------+
double CalculateLotSize(double sl_distance)
{
    if(Risk_Percent <= 0) return Lot_Size;
    
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * Risk_Percent / 100.0;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    
    double sl_points = sl_distance / point;
    double sl_ticks = sl_points * tick_size / point;
    double lot = risk_amount / (sl_ticks * tick_value);
    
    // Minimum ve maksimum lot kontrolü
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lot = MathMax(lot, min_lot);
    lot = MathMin(lot, max_lot);
    lot = MathFloor(lot / lot_step) * lot_step;
    
    return lot;
}

//+------------------------------------------------------------------+
//| Pozisyon var mı kontrol et                                      |
//+------------------------------------------------------------------+
bool HasPosition(ENUM_ORDER_TYPE type)
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetTicket(i) > 0)
        {
            if(PositionSelectByTicket(PositionGetTicket(i)))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
                   PositionGetInteger(POSITION_TYPE) == type)
                {
                    return true;
                }
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void CloseAllPositions(ENUM_ORDER_TYPE type)
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
                   PositionGetInteger(POSITION_TYPE) == type)
                {
                    if(!trade.PositionClose(ticket))
                    {
                        debug_log_number++;
                        Print("❌ [", debug_log_number, "] Pozisyon kapatma hatası: ", trade.ResultRetcode());
                    }
                }
            }
        }
    }
}
