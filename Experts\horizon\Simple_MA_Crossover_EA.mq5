//+------------------------------------------------------------------+
//| Simple MA Crossover EA                                           |
//| MA7/MA50 kesişimi ile giri<PERSON>, ters kesişim ile ç<PERSON>             |
//+------------------------------------------------------------------+
#property copyright "Horizon Trading"
#property version   "1.00"
#property strict

//--- Input parametreleri
input group "=== MA Ayarları ==="
input int                   MA_Fast = 7;                           // Hızlı MA Periyodu
input int                   MA_Slow = 50;                          // Yavaş MA Periyodu
input ENUM_MA_METHOD        MA_Method = MODE_SMA;                  // MA Hesaplama Metodu
input ENUM_APPLIED_PRICE    MA_Applied_Price = PRICE_CLOSE;        // MA Uygulanan Fiyat

input group "=== Risk Yönetimi ==="
input double                Lot_Size = 0.01;                      // Lot Büyüklüğü
input double                ATR_Multiplier = 2.0;                 // ATR Çarpanı (SL için)
input int                   ATR_Period = 14;                      // ATR Periyodu
input double                Risk_Percent = 2.0;                   // Risk Yüzdesi (% bakiye)

input group "=== Genel Ayarlar ==="
input int                   Magic_Number = 8888;                  // Magic Number
input string                Comment_Prefix = "Simple_MA";         // Yorum Ön Eki
input bool                  Use_ATR_SL = true;                    // ATR bazlı SL kullan
input bool                  Close_Opposite = true;                // Ters sinyal geldiğinde kapat

//--- Global değişkenler
int ma_fast_handle;
int ma_slow_handle;
int atr_handle;
double ma_fast_buffer[];
double ma_slow_buffer[];
double atr_buffer[];
int debug_log_number = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // MA handle'larını oluştur
    ma_fast_handle = iMA(_Symbol, _Period, MA_Fast, 0, MA_Method, MA_Applied_Price);
    ma_slow_handle = iMA(_Symbol, _Period, MA_Slow, 0, MA_Method, MA_Applied_Price);
    atr_handle = iATR(_Symbol, _Period, ATR_Period);
    
    if(ma_fast_handle == INVALID_HANDLE || ma_slow_handle == INVALID_HANDLE || atr_handle == INVALID_HANDLE)
    {
        Print("❌ Indicator handle oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Array ayarları
    ArraySetAsSeries(ma_fast_buffer, true);
    ArraySetAsSeries(ma_slow_buffer, true);
    ArraySetAsSeries(atr_buffer, true);
    
    debug_log_number = 0;
    
    Print("✅ Simple MA Crossover EA başlatıldı - Magic: ", Magic_Number);
    Print("📊 MA", MA_Fast, "/MA", MA_Slow, " kesişim sistemi aktif");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("🔄 Simple MA Crossover EA durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Yeterli bar kontrolü
    if(Bars(_Symbol, _Period) < MA_Slow + 10)
        return;
    
    // Indicator verilerini al
    if(CopyBuffer(ma_fast_handle, 0, 0, 3, ma_fast_buffer) < 3 ||
       CopyBuffer(ma_slow_handle, 0, 0, 3, ma_slow_buffer) < 3 ||
       CopyBuffer(atr_handle, 0, 0, 2, atr_buffer) < 2)
    {
        return;
    }
    
    // MA değerleri
    double ma_fast_current = ma_fast_buffer[0];
    double ma_fast_prev = ma_fast_buffer[1];
    double ma_slow_current = ma_slow_buffer[0];
    double ma_slow_prev = ma_slow_buffer[1];
    double atr_current = atr_buffer[0];
    
    // Kesişim kontrolü
    bool buy_crossover = (ma_fast_prev <= ma_slow_prev) && (ma_fast_current > ma_slow_current);
    bool sell_crossover = (ma_fast_prev >= ma_slow_prev) && (ma_fast_current < ma_slow_current);
    
    // Mevcut pozisyon kontrolü
    bool has_buy_position = HasPosition(ORDER_TYPE_BUY);
    bool has_sell_position = HasPosition(ORDER_TYPE_SELL);
    
    // Debug - sadece kesişim olduğunda
    if(buy_crossover || sell_crossover)
    {
        debug_log_number++;
        Print("🔍 [", debug_log_number, "] KESİŞİM ALGILANDI:");
        Print("   MA", MA_Fast, "[1]: ", DoubleToString(ma_fast_prev, 5), " → MA", MA_Fast, "[0]: ", DoubleToString(ma_fast_current, 5));
        Print("   MA", MA_Slow, "[1]: ", DoubleToString(ma_slow_prev, 5), " → MA", MA_Slow, "[0]: ", DoubleToString(ma_slow_current, 5));
        Print("   BUY Kesişim: ", buy_crossover, " SELL Kesişim: ", sell_crossover);
        Print("   Mevcut Pozisyon - BUY: ", has_buy_position, " SELL: ", has_sell_position);
    }
    
    // BUY Sinyali
    if(buy_crossover)
    {
        // Ters pozisyon varsa kapat
        if(has_sell_position && Close_Opposite)
        {
            CloseAllPositions(ORDER_TYPE_SELL);
            debug_log_number++;
            Print("🔴 [", debug_log_number, "] SELL pozisyonları kapatıldı (BUY sinyali)");
        }
        
        // BUY pozisyon yoksa aç
        if(!has_buy_position)
        {
            OpenBuyOrder(atr_current);
        }
    }
    
    // SELL Sinyali
    if(sell_crossover)
    {
        // Ters pozisyon varsa kapat
        if(has_buy_position && Close_Opposite)
        {
            CloseAllPositions(ORDER_TYPE_BUY);
            debug_log_number++;
            Print("🟢 [", debug_log_number, "] BUY pozisyonları kapatıldı (SELL sinyali)");
        }
        
        // SELL pozisyon yoksa aç
        if(!has_sell_position)
        {
            OpenSellOrder(atr_current);
        }
    }
}

//+------------------------------------------------------------------+
//| BUY emri aç                                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder(double atr_value)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = 0, tp = 0;
    
    if(Use_ATR_SL)
    {
        sl = ask - (atr_value * ATR_Multiplier);
    }
    
    // Lot hesaplama
    double lot = CalculateLotSize(sl > 0 ? MathAbs(ask - sl) : atr_value * ATR_Multiplier);
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lot;
    request.type = ORDER_TYPE_BUY;
    request.price = ask;
    request.sl = sl;
    request.tp = tp;
    request.magic = Magic_Number;
    request.comment = Comment_Prefix + "_BUY";
    request.type_filling = ORDER_FILLING_IOC;
    
    if(OrderSend(request, result))
    {
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI!");
        Print("   Ticket: ", result.order, " Fiyat: ", DoubleToString(ask, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   MA", MA_Fast, ": ", DoubleToString(ma_fast_buffer[0], 5), " > MA", MA_Slow, ": ", DoubleToString(ma_slow_buffer[0], 5));
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] BUY EMRİ HATASI: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder(double atr_value)
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = 0, tp = 0;
    
    if(Use_ATR_SL)
    {
        sl = bid + (atr_value * ATR_Multiplier);
    }
    
    // Lot hesaplama
    double lot = CalculateLotSize(sl > 0 ? MathAbs(bid - sl) : atr_value * ATR_Multiplier);
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lot;
    request.type = ORDER_TYPE_SELL;
    request.price = bid;
    request.sl = sl;
    request.tp = tp;
    request.magic = Magic_Number;
    request.comment = Comment_Prefix + "_SELL";
    request.type_filling = ORDER_FILLING_IOC;
    
    if(OrderSend(request, result))
    {
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] SELL EMRİ AÇILDI!");
        Print("   Ticket: ", result.order, " Fiyat: ", DoubleToString(bid, 5));
        Print("   Lot: ", DoubleToString(lot, 2), " SL: ", DoubleToString(sl, 5));
        Print("   MA", MA_Fast, ": ", DoubleToString(ma_fast_buffer[0], 5), " < MA", MA_Slow, ": ", DoubleToString(ma_slow_buffer[0], 5));
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] SELL EMRİ HATASI: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| Lot büyüklüğü hesapla                                           |
//+------------------------------------------------------------------+
double CalculateLotSize(double sl_distance)
{
    if(Risk_Percent <= 0) return Lot_Size;
    
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * Risk_Percent / 100.0;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    
    double sl_points = sl_distance / point;
    double sl_ticks = sl_points * tick_size / point;
    double lot = risk_amount / (sl_ticks * tick_value);
    
    // Minimum ve maksimum lot kontrolü
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lot = MathMax(lot, min_lot);
    lot = MathMin(lot, max_lot);
    lot = MathFloor(lot / lot_step) * lot_step;
    
    return lot;
}

//+------------------------------------------------------------------+
//| Pozisyon var mı kontrol et                                      |
//+------------------------------------------------------------------+
bool HasPosition(ENUM_ORDER_TYPE type)
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionSelectByIndex(i))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
               PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
               PositionGetInteger(POSITION_TYPE) == type)
            {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void CloseAllPositions(ENUM_ORDER_TYPE type)
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByIndex(i))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
               PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
               PositionGetInteger(POSITION_TYPE) == type)
            {
                ulong ticket = PositionGetInteger(POSITION_TICKET);
                
                MqlTradeRequest request = {};
                MqlTradeResult result = {};
                
                request.action = TRADE_ACTION_DEAL;
                request.symbol = _Symbol;
                request.volume = PositionGetDouble(POSITION_VOLUME);
                request.type = (type == ORDER_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
                request.position = ticket;
                request.price = (type == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                request.magic = Magic_Number;
                request.comment = Comment_Prefix + "_CLOSE";
                request.type_filling = ORDER_FILLING_IOC;
                
                OrderSend(request, result);
            }
        }
    }
}
