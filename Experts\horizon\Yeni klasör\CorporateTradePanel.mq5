//+------------------------------------------------------------------+
//| CorporateTradePanel.mq5 - Kurumsal 2 Sütunlu İşlem Paneli      |
//| Profesyonel tasarım + Teknik indikatörler + Hızlı işlem        |
//+------------------------------------------------------------------+
#property copyright "Horizon Systems"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Kurumsal 2 Sütunlu Gelişmiş İşlem Paneli"

#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| Panel ayarları                                                  |
//+------------------------------------------------------------------+
input group "=== Panel Ayarları ==="
input int StartX = 50;              // Başlangıç X pozisyonu
input int StartY = 50;              // Başlangıç Y pozisyonu

input group "=== İşlem Ayarları ==="
input double DefaultLot = 0.1;      // Varsayılan Lot
input int DefaultSL = 50;           // Varsayılan SL (pip)
input int DefaultTP = 100;          // Varsayılan TP (pip)

input group "=== İndikatör Eşikleri ==="
input double RSI_Oversold = 30;    // RSI Aşırı Satım
input double RSI_Overbought = 70;  // RSI Aşırı Alım
input double ATR_High = 0.002;     // ATR Yüksek Eşik
input int MA_Period = 20;          // MA Periyodu

//+------------------------------------------------------------------+
//| Panel boyutları - 2 Sütunlu Tasarım                            |
//+------------------------------------------------------------------+
#define PANEL_WIDTH     420
#define PANEL_HEIGHT    380
#define TITLE_HEIGHT    28
#define COLUMN_WIDTH    190
#define ROW_HEIGHT      25
#define MARGIN          10
#define SPACING         8

//+------------------------------------------------------------------+
//| Kurumsal Renkler - Mavi Gri Tonları                            |
//+------------------------------------------------------------------+
#define COLOR_TITLE_BG      C'45,85,135'      // Koyu mavi başlık
#define COLOR_PANEL_BG      C'240,245,250'    // Açık gri arka plan
#define COLOR_SECTION_BG    C'220,230,240'    // Bölüm arka planı
#define COLOR_BUTTON_BUY    C'40,120,40'      // Yeşil buy
#define COLOR_BUTTON_SELL   C'180,40,40'      // Kırmızı sell
#define COLOR_BUTTON_CLOSE  C'100,100,100'    // Gri close
#define COLOR_TEXT_NORMAL   C'50,50,50'       // Koyu gri metin
#define COLOR_TEXT_TITLE    clrWhite          // Beyaz başlık
#define COLOR_BORDER        C'180,190,200'    // Açık gri kenarlık

//+------------------------------------------------------------------+
//| Global değişkenler                                              |
//+------------------------------------------------------------------+
string g_panelName = "CorporateTradePanel";
datetime g_lastUpdate = 0;

// Sürükleme durumu
bool g_isDragging = false;
int g_dragStartX = 0;
int g_dragStartY = 0;
int g_currentX = 0;
int g_currentY = 0;

// İşlem ayarları
double g_currentLot = 0.1;
int g_currentSL = 50;
int g_currentTP = 100;

// İndikatör değerleri
double g_rsiValue = 0;
double g_maValue = 0;
double g_atrValue = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    g_currentX = StartX;
    g_currentY = StartY;
    g_currentLot = DefaultLot;
    g_currentSL = DefaultSL;
    g_currentTP = DefaultTP;
    
    // Mouse event'lerini aktif et
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, true);
    
    CreateCorporatePanel();
    UpdateIndicators();
    UpdatePanelData();
    
    Print("✅ Corporate Trade Panel başlatıldı - Kurumsal tasarım aktif!");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    ObjectsDeleteAll(0, g_panelName);
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, false);
    Print("🔄 Corporate Trade Panel kapatıldı");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(TimeCurrent() - g_lastUpdate >= 2)
    {
        UpdateIndicators();
        UpdatePanelData();
        g_lastUpdate = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| ChartEvent function                                              |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        HandleButtonClick(sparam);
    }
    else if(id == CHARTEVENT_MOUSE_MOVE)
    {
        HandleCorporateMouseMove((int)lparam, (int)dparam, (int)sparam);
    }
}

//+------------------------------------------------------------------+
//| Kurumsal mouse hareket işleyici                                 |
//+------------------------------------------------------------------+
void HandleCorporateMouseMove(int x, int y, int flags)
{
    bool leftButtonPressed = (flags & 1) != 0;
    bool mouseOverPanel = IsMouseOverPanel(x, y);
    
    if(leftButtonPressed)
    {
        if(!g_isDragging && IsMouseOverTitleBar(x, y))
        {
            g_isDragging = true;
            g_dragStartX = x;
            g_dragStartY = y;
            
            // Chart interaction'ı bloke et
            ChartSetInteger(0, CHART_MOUSE_SCROLL, false);
            ChartSetInteger(0, CHART_DRAG_TRADE_LEVELS, false);
        }
        
        if(g_isDragging)
        {
            int deltaX = x - g_dragStartX;
            int deltaY = y - g_dragStartY;
            
            g_currentX += deltaX;
            g_currentY += deltaY;
            
            // Ekran sınırları kontrolü
            int chartWidth = (int)ChartGetInteger(0, CHART_WIDTH_IN_PIXELS);
            int chartHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);
            
            if(g_currentX < 0) g_currentX = 0;
            if(g_currentY < 0) g_currentY = 0;
            if(g_currentX + PANEL_WIDTH > chartWidth) g_currentX = chartWidth - PANEL_WIDTH;
            if(g_currentY + PANEL_HEIGHT > chartHeight) g_currentY = chartHeight - PANEL_HEIGHT;
            
            UpdateAllPositions();
            
            g_dragStartX = x;
            g_dragStartY = y;
        }
        else if(mouseOverPanel)
        {
            ChartSetInteger(0, CHART_MOUSE_SCROLL, false);
            ChartSetInteger(0, CHART_DRAG_TRADE_LEVELS, false);
        }
    }
    else
    {
        if(g_isDragging)
        {
            g_isDragging = false;
            ChartSetInteger(0, CHART_MOUSE_SCROLL, true);
            ChartSetInteger(0, CHART_DRAG_TRADE_LEVELS, true);
        }
        else if(!mouseOverPanel)
        {
            ChartSetInteger(0, CHART_MOUSE_SCROLL, true);
            ChartSetInteger(0, CHART_DRAG_TRADE_LEVELS, true);
        }
    }
}

//+------------------------------------------------------------------+
//| Mouse kontrol fonksiyonları                                     |
//+------------------------------------------------------------------+
bool IsMouseOverPanel(int mouseX, int mouseY)
{
    return (mouseX >= g_currentX && mouseX <= g_currentX + PANEL_WIDTH &&
            mouseY >= g_currentY && mouseY <= g_currentY + PANEL_HEIGHT);
}

bool IsMouseOverTitleBar(int mouseX, int mouseY)
{
    return (mouseX >= g_currentX && mouseX <= g_currentX + PANEL_WIDTH &&
            mouseY >= g_currentY && mouseY <= g_currentY + TITLE_HEIGHT);
}

//+------------------------------------------------------------------+
//| Kurumsal panel oluştur                                          |
//+------------------------------------------------------------------+
void CreateCorporatePanel()
{
    // Ana panel arka planı
    CreateRectangle("Background", g_currentX, g_currentY, PANEL_WIDTH, PANEL_HEIGHT, 
                   COLOR_PANEL_BG, COLOR_BORDER, 1000);
    
    // Başlık çubuğu
    CreateRectangle("TitleBar", g_currentX, g_currentY, PANEL_WIDTH, TITLE_HEIGHT, 
                   COLOR_TITLE_BG, COLOR_TITLE_BG, 1001);
    
    // Başlık metni
    CreateLabel("Title", "📊 CORPORATE TRADE PANEL", g_currentX + PANEL_WIDTH/2, 
               g_currentY + TITLE_HEIGHT/2 - 7, COLOR_TEXT_TITLE, 10, ANCHOR_CENTER, 1002);
    
    // Kapat butonu
    CreateLabel("CloseBtn", "✖", g_currentX + PANEL_WIDTH - 20, 
               g_currentY + TITLE_HEIGHT/2 - 7, COLOR_TEXT_TITLE, 12, ANCHOR_CENTER, 1003);
    
    CreateTwoColumnLayout();
}

//+------------------------------------------------------------------+
//| 2 Sütunlu layout oluştur                                        |
//+------------------------------------------------------------------+
void CreateTwoColumnLayout()
{
    int startY = g_currentY + TITLE_HEIGHT + MARGIN;
    
    // SOL SÜTUN - İndikatörler ve Bilgiler
    CreateLeftColumn(startY);
    
    // SAĞ SÜTUN - İşlem Kontrolleri
    CreateRightColumn(startY);
}

//+------------------------------------------------------------------+
//| Sol sütun - İndikatörler                                        |
//+------------------------------------------------------------------+
void CreateLeftColumn(int startY)
{
    int leftX = g_currentX + MARGIN;
    int currentY = startY;
    
    // İndikatörler başlığı
    CreateSectionHeader("IndicatorHeader", "📈 TEKNİK İNDİKATÖRLER", leftX, currentY);
    currentY += ROW_HEIGHT + SPACING;
    
    // İndikatör bölümü arka planı
    CreateRectangle("IndicatorBG", leftX, currentY, COLUMN_WIDTH, 90, 
                   COLOR_SECTION_BG, COLOR_BORDER, 1001);
    currentY += SPACING;
    
    // RSI
    CreateIndicatorRow("RSI", "RSI:", leftX + SPACING, currentY);
    currentY += ROW_HEIGHT;
    
    // MA
    CreateIndicatorRow("MA", "MA(" + IntegerToString(MA_Period) + "):", leftX + SPACING, currentY);
    currentY += ROW_HEIGHT;
    
    // ATR
    CreateIndicatorRow("ATR", "ATR:", leftX + SPACING, currentY);
    currentY += ROW_HEIGHT + MARGIN;
    
    // Pozisyon bilgileri başlığı
    CreateSectionHeader("PositionHeader", "💼 POZİSYON BİLGİLERİ", leftX, currentY);
    currentY += ROW_HEIGHT + SPACING;
    
    // Pozisyon bölümü arka planı
    CreateRectangle("PositionBG", leftX, currentY, COLUMN_WIDTH, 110, 
                   COLOR_SECTION_BG, COLOR_BORDER, 1001);
    currentY += SPACING;
    
    // Pozisyon bilgileri
    CreatePositionInfo(leftX + SPACING, currentY);
}

//+------------------------------------------------------------------+
//| Sağ sütun - İşlem kontrolleri                                   |
//+------------------------------------------------------------------+
void CreateRightColumn(int startY)
{
    int rightX = g_currentX + MARGIN + COLUMN_WIDTH + MARGIN;
    int currentY = startY;

    // Hızlı işlem başlığı
    CreateSectionHeader("TradeHeader", "⚡ HIZLI İŞLEM", rightX, currentY);
    currentY += ROW_HEIGHT + SPACING;

    // İşlem ayarları arka planı
    CreateRectangle("TradeBG", rightX, currentY, COLUMN_WIDTH, 130,
                   COLOR_SECTION_BG, COLOR_BORDER, 1001);
    currentY += SPACING;

    // Lot ayarı
    CreateTradeControl("Lot", "LOT:", rightX + SPACING, currentY);
    currentY += ROW_HEIGHT;

    // SL ayarı
    CreateTradeControl("SL", "SL (pip):", rightX + SPACING, currentY);
    currentY += ROW_HEIGHT;

    // TP ayarı
    CreateTradeControl("TP", "TP (pip):", rightX + SPACING, currentY);
    currentY += ROW_HEIGHT;

    // Buy/Sell butonları
    currentY += SPACING;
    CreateTradeButton("BuyBtn", "🟢 BUY", rightX + SPACING, currentY, 80, COLOR_BUTTON_BUY);
    CreateTradeButton("SellBtn", "🔴 SELL", rightX + SPACING + 85, currentY, 80, COLOR_BUTTON_SELL);
    currentY += ROW_HEIGHT + MARGIN;

    // Pozisyon yönetimi başlığı
    CreateSectionHeader("ManageHeader", "🎯 POZİSYON YÖNETİMİ", rightX, currentY);
    currentY += ROW_HEIGHT + SPACING;

    // Yönetim butonları arka planı
    CreateRectangle("ManageBG", rightX, currentY, COLUMN_WIDTH, 90,
                   COLOR_SECTION_BG, COLOR_BORDER, 1001);
    currentY += SPACING;

    // Yönetim butonları
    CreateTradeButton("CloseProfitBtn", "💚 Karlı Kapat", rightX + SPACING, currentY, COLUMN_WIDTH - 2*SPACING, COLOR_BUTTON_CLOSE);
    currentY += ROW_HEIGHT + SPACING/2;

    CreateTradeButton("CloseLossBtn", "❤️ Zararlı Kapat", rightX + SPACING, currentY, COLUMN_WIDTH - 2*SPACING, COLOR_BUTTON_CLOSE);
    currentY += ROW_HEIGHT + SPACING/2;

    CreateTradeButton("CloseAllBtn", "🚫 Hepsini Kapat", rightX + SPACING, currentY, COLUMN_WIDTH - 2*SPACING, COLOR_BUTTON_CLOSE);
}

//+------------------------------------------------------------------+
//| Bölüm başlığı oluştur                                           |
//+------------------------------------------------------------------+
void CreateSectionHeader(string name, string text, int x, int y)
{
    CreateLabel(name, text, x, y, COLOR_TEXT_NORMAL, 9, ANCHOR_LEFT_UPPER, 1002);
}

//+------------------------------------------------------------------+
//| İndikatör satırı oluştur                                        |
//+------------------------------------------------------------------+
void CreateIndicatorRow(string name, string label, int x, int y)
{
    // Label
    CreateLabel(name + "_Label", label, x, y, COLOR_TEXT_NORMAL, 8, ANCHOR_LEFT_UPPER, 1002);

    // Değer
    CreateLabel(name + "_Value", "0.00", x + 80, y, COLOR_TEXT_NORMAL, 8, ANCHOR_LEFT_UPPER, 1002);
}

//+------------------------------------------------------------------+
//| İşlem kontrolü oluştur                                          |
//+------------------------------------------------------------------+
void CreateTradeControl(string name, string label, int x, int y)
{
    // Label
    CreateLabel(name + "_Label", label, x, y, COLOR_TEXT_NORMAL, 8, ANCHOR_LEFT_UPPER, 1002);

    // Değer (düzenlenebilir görünüm)
    CreateRectangle(name + "_Box", x + 80, y - 2, 80, 18, clrWhite, COLOR_BORDER, 1002);
    CreateLabel(name + "_Value", "0.00", x + 120, y, COLOR_TEXT_NORMAL, 8, ANCHOR_CENTER, 1003);

    // Artır/Azalt butonları
    CreateMiniButton(name + "_Up", "▲", x + 165, y - 2, 15, 8, COLOR_BORDER);
    CreateMiniButton(name + "_Down", "▼", x + 165, y + 8, 15, 8, COLOR_BORDER);
}

//+------------------------------------------------------------------+
//| İşlem butonu oluştur                                            |
//+------------------------------------------------------------------+
void CreateTradeButton(string name, string text, int x, int y, int width, color bgColor)
{
    CreateRectangle(name, x, y, width, ROW_HEIGHT, bgColor, COLOR_BORDER, 1002);
    CreateLabel(name + "_Text", text, x + width/2, y + ROW_HEIGHT/2 - 6, clrWhite, 9, ANCHOR_CENTER, 1003);
}

//+------------------------------------------------------------------+
//| Mini buton oluştur                                              |
//+------------------------------------------------------------------+
void CreateMiniButton(string name, string text, int x, int y, int width, int height, color bgColor)
{
    CreateRectangle(name, x, y, width, height, bgColor, COLOR_BORDER, 1002);
    CreateLabel(name + "_Text", text, x + width/2, y + height/2 - 3, COLOR_TEXT_NORMAL, 6, ANCHOR_CENTER, 1003);
}

//+------------------------------------------------------------------+
//| Pozisyon bilgileri oluştur                                      |
//+------------------------------------------------------------------+
void CreatePositionInfo(int x, int y)
{
    int currentY = y;

    CreateLabel("PosCount", "Açık Pozisyon: 0", x, currentY, COLOR_TEXT_NORMAL, 8, ANCHOR_LEFT_UPPER, 1002);
    currentY += 18;

    CreateLabel("TotalPL", "Toplam P&L: $0.00", x, currentY, COLOR_TEXT_NORMAL, 8, ANCHOR_LEFT_UPPER, 1002);
    currentY += 18;

    CreateLabel("DailyPL", "Günlük P&L: $0.00", x, currentY, COLOR_TEXT_NORMAL, 8, ANCHOR_LEFT_UPPER, 1002);
    currentY += 18;

    CreateLabel("TotalVol", "Toplam Hacim: 0.00", x, currentY, COLOR_TEXT_NORMAL, 8, ANCHOR_LEFT_UPPER, 1002);
    currentY += 18;

    CreateLabel("ProfitCount", "Karlı: 0 | Zararlı: 0", x, currentY, COLOR_TEXT_NORMAL, 8, ANCHOR_LEFT_UPPER, 1002);
}

//+------------------------------------------------------------------+
//| Dikdörtgen oluştur                                              |
//+------------------------------------------------------------------+
void CreateRectangle(string name, int x, int y, int width, int height, color bgColor, color borderColor, int zorder)
{
    string objName = g_panelName + "_" + name;

    ObjectCreate(0, objName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetInteger(0, objName, OBJPROP_XSIZE, width);
    ObjectSetInteger(0, objName, OBJPROP_YSIZE, height);
    ObjectSetInteger(0, objName, OBJPROP_BGCOLOR, bgColor);
    ObjectSetInteger(0, objName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, borderColor);
    ObjectSetInteger(0, objName, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, objName, OBJPROP_BACK, false);
    ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName, OBJPROP_ZORDER, zorder);
}

//+------------------------------------------------------------------+
//| Label oluştur                                                   |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, int x, int y, color clr, int fontSize, ENUM_ANCHOR_POINT anchor, int zorder)
{
    string objName = g_panelName + "_" + name;

    ObjectCreate(0, objName, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, x);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, y);
    ObjectSetString(0, objName, OBJPROP_TEXT, text);
    ObjectSetString(0, objName, OBJPROP_FONT, "Segoe UI");
    ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, fontSize);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, objName, OBJPROP_ANCHOR, anchor);
    ObjectSetInteger(0, objName, OBJPROP_BACK, false);
    ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, objName, OBJPROP_ZORDER, zorder);
}

//+------------------------------------------------------------------+
//| İndikatörleri güncelle                                          |
//+------------------------------------------------------------------+
void UpdateIndicators()
{
    // RSI hesapla
    int rsiHandle = iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
    if(rsiHandle != INVALID_HANDLE)
    {
        double rsiBuffer[1];
        if(CopyBuffer(rsiHandle, 0, 0, 1, rsiBuffer) > 0)
        {
            g_rsiValue = rsiBuffer[0];
        }
        IndicatorRelease(rsiHandle);
    }

    // MA hesapla
    int maHandle = iMA(_Symbol, PERIOD_CURRENT, MA_Period, 0, MODE_SMA, PRICE_CLOSE);
    if(maHandle != INVALID_HANDLE)
    {
        double maBuffer[1];
        if(CopyBuffer(maHandle, 0, 0, 1, maBuffer) > 0)
        {
            g_maValue = maBuffer[0];
        }
        IndicatorRelease(maHandle);
    }

    // ATR hesapla
    int atrHandle = iATR(_Symbol, PERIOD_CURRENT, 14);
    if(atrHandle != INVALID_HANDLE)
    {
        double atrBuffer[1];
        if(CopyBuffer(atrHandle, 0, 0, 1, atrBuffer) > 0)
        {
            g_atrValue = atrBuffer[0];
        }
        IndicatorRelease(atrHandle);
    }

    // İndikatör değerlerini güncelle
    UpdateIndicatorDisplay();
}

//+------------------------------------------------------------------+
//| İndikatör görünümünü güncelle                                   |
//+------------------------------------------------------------------+
void UpdateIndicatorDisplay()
{
    // RSI güncelle ve renk değiştir
    color rsiColor = COLOR_TEXT_NORMAL;
    if(g_rsiValue <= RSI_Oversold)
        rsiColor = clrLimeGreen;  // Aşırı satım - yeşil
    else if(g_rsiValue >= RSI_Overbought)
        rsiColor = clrTomato;     // Aşırı alım - kırmızı

    string rsiText = DoubleToString(g_rsiValue, 1);
    ObjectSetString(0, g_panelName + "_RSI_Value", OBJPROP_TEXT, rsiText);
    ObjectSetInteger(0, g_panelName + "_RSI_Value", OBJPROP_COLOR, rsiColor);

    // MA güncelle
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    color maColor = COLOR_TEXT_NORMAL;
    if(currentPrice > g_maValue)
        maColor = clrLimeGreen;   // Fiyat MA üzerinde - yeşil
    else if(currentPrice < g_maValue)
        maColor = clrTomato;      // Fiyat MA altında - kırmızı

    string maText = DoubleToString(g_maValue, _Digits);
    ObjectSetString(0, g_panelName + "_MA_Value", OBJPROP_TEXT, maText);
    ObjectSetInteger(0, g_panelName + "_MA_Value", OBJPROP_COLOR, maColor);

    // ATR güncelle ve renk değiştir
    color atrColor = COLOR_TEXT_NORMAL;
    if(g_atrValue >= ATR_High)
        atrColor = clrOrange;     // Yüksek volatilite - turuncu

    string atrText = DoubleToString(g_atrValue, _Digits + 1);
    ObjectSetString(0, g_panelName + "_ATR_Value", OBJPROP_TEXT, atrText);
    ObjectSetInteger(0, g_panelName + "_ATR_Value", OBJPROP_COLOR, atrColor);
}

//+------------------------------------------------------------------+
//| Panel verilerini güncelle                                       |
//+------------------------------------------------------------------+
void UpdatePanelData()
{
    // Pozisyon verilerini hesapla
    int positionCount = 0;
    double totalVolume = 0;
    double totalProfit = 0;
    int profitableCount = 0;
    int losingCount = 0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        double volume = PositionGetDouble(POSITION_VOLUME);
        double profit = PositionGetDouble(POSITION_PROFIT);

        positionCount++;
        totalVolume += volume;
        totalProfit += profit;

        if(profit > 0)
            profitableCount++;
        else if(profit < 0)
            losingCount++;
    }

    // Günlük P&L hesapla
    double dailyPL = CalculateDailyPL(totalProfit);

    // Pozisyon bilgilerini güncelle
    UpdatePositionDisplay(positionCount, totalVolume, totalProfit, profitableCount, losingCount, dailyPL);

    // İşlem ayarlarını güncelle
    UpdateTradeControls();
}

//+------------------------------------------------------------------+
//| Pozisyon görünümünü güncelle                                    |
//+------------------------------------------------------------------+
void UpdatePositionDisplay(int posCount, double totalVol, double totalPL, int profitCount, int lossCount, double dailyPL)
{
    // Açık pozisyon sayısı
    ObjectSetString(0, g_panelName + "_PosCount", OBJPROP_TEXT, "Açık Pozisyon: " + IntegerToString(posCount));

    // Toplam P&L
    color plColor = (totalPL >= 0) ? clrLimeGreen : clrTomato;
    ObjectSetString(0, g_panelName + "_TotalPL", OBJPROP_TEXT, "Toplam P&L: $" + DoubleToString(totalPL, 2));
    ObjectSetInteger(0, g_panelName + "_TotalPL", OBJPROP_COLOR, plColor);

    // Günlük P&L
    color dailyColor = (dailyPL >= 0) ? clrLimeGreen : clrTomato;
    ObjectSetString(0, g_panelName + "_DailyPL", OBJPROP_TEXT, "Günlük P&L: $" + DoubleToString(dailyPL, 2));
    ObjectSetInteger(0, g_panelName + "_DailyPL", OBJPROP_COLOR, dailyColor);

    // Toplam hacim
    ObjectSetString(0, g_panelName + "_TotalVol", OBJPROP_TEXT, "Toplam Hacim: " + DoubleToString(totalVol, 2));

    // Karlı/Zararlı sayısı
    ObjectSetString(0, g_panelName + "_ProfitCount", OBJPROP_TEXT, "Karlı: " + IntegerToString(profitCount) + " | Zararlı: " + IntegerToString(lossCount));
}

//+------------------------------------------------------------------+
//| İşlem kontrollerini güncelle                                    |
//+------------------------------------------------------------------+
void UpdateTradeControls()
{
    // Lot değeri
    ObjectSetString(0, g_panelName + "_Lot_Value", OBJPROP_TEXT, DoubleToString(g_currentLot, 2));

    // SL değeri
    ObjectSetString(0, g_panelName + "_SL_Value", OBJPROP_TEXT, IntegerToString(g_currentSL));

    // TP değeri
    ObjectSetString(0, g_panelName + "_TP_Value", OBJPROP_TEXT, IntegerToString(g_currentTP));
}

//+------------------------------------------------------------------+
//| Günlük P&L hesapla                                             |
//+------------------------------------------------------------------+
double CalculateDailyPL(double currentPL)
{
    double dailyPL = 0;
    datetime todayStart = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));

    HistorySelect(todayStart, TimeCurrent());

    for(int i = 0; i < HistoryDealsTotal(); i++)
    {
        ulong ticket = HistoryDealGetTicket(i);
        if(ticket > 0)
        {
            ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(ticket, DEAL_TYPE);
            if(dealType == DEAL_TYPE_BUY || dealType == DEAL_TYPE_SELL)
            {
                double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                double commission = HistoryDealGetDouble(ticket, DEAL_COMMISSION);
                double swap = HistoryDealGetDouble(ticket, DEAL_SWAP);

                dailyPL += (profit + commission + swap);
            }
        }
    }

    dailyPL += currentPL;
    return dailyPL;
}

//+------------------------------------------------------------------+
//| Buton tıklama işleyici                                          |
//+------------------------------------------------------------------+
void HandleButtonClick(string objName)
{
    // Lot ayarları
    if(StringFind(objName, "Lot_Up") >= 0)
    {
        g_currentLot += 0.01;
        if(g_currentLot > 10.0) g_currentLot = 10.0;
    }
    else if(StringFind(objName, "Lot_Down") >= 0)
    {
        g_currentLot -= 0.01;
        if(g_currentLot < 0.01) g_currentLot = 0.01;
    }
    // SL ayarları
    else if(StringFind(objName, "SL_Up") >= 0)
    {
        g_currentSL += 5;
        if(g_currentSL > 500) g_currentSL = 500;
    }
    else if(StringFind(objName, "SL_Down") >= 0)
    {
        g_currentSL -= 5;
        if(g_currentSL < 5) g_currentSL = 5;
    }
    // TP ayarları
    else if(StringFind(objName, "TP_Up") >= 0)
    {
        g_currentTP += 5;
        if(g_currentTP > 1000) g_currentTP = 1000;
    }
    else if(StringFind(objName, "TP_Down") >= 0)
    {
        g_currentTP -= 5;
        if(g_currentTP < 5) g_currentTP = 5;
    }
    // İşlem butonları
    else if(StringFind(objName, "BuyBtn") >= 0)
    {
        ExecuteBuyOrder();
    }
    else if(StringFind(objName, "SellBtn") >= 0)
    {
        ExecuteSellOrder();
    }
    // Pozisyon yönetimi
    else if(StringFind(objName, "CloseProfitBtn") >= 0)
    {
        CloseProfitablePositions();
    }
    else if(StringFind(objName, "CloseLossBtn") >= 0)
    {
        CloseLosingPositions();
    }
    else if(StringFind(objName, "CloseAllBtn") >= 0)
    {
        CloseAllPositions();
    }
    else if(StringFind(objName, "CloseBtn") >= 0)
    {
        ObjectsDeleteAll(0, g_panelName);
        ExpertRemove();
    }

    // Kontrolleri güncelle
    UpdateTradeControls();
}

//+------------------------------------------------------------------+
//| Buy emri ver                                                     |
//+------------------------------------------------------------------+
void ExecuteBuyOrder()
{
    CTrade trade;

    double price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = price - (g_currentSL * _Point * 10);
    double tp = price + (g_currentTP * _Point * 10);

    if(trade.Buy(g_currentLot, _Symbol, price, sl, tp, "Corporate Panel Buy"))
    {
        Print("✅ BUY emri başarılı - Lot: ", g_currentLot, " SL: ", g_currentSL, " TP: ", g_currentTP);
    }
    else
    {
        Print("❌ BUY emri başarısız - Hata: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Sell emri ver                                                    |
//+------------------------------------------------------------------+
void ExecuteSellOrder()
{
    CTrade trade;

    double price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = price + (g_currentSL * _Point * 10);
    double tp = price - (g_currentTP * _Point * 10);

    if(trade.Sell(g_currentLot, _Symbol, price, sl, tp, "Corporate Panel Sell"))
    {
        Print("✅ SELL emri başarılı - Lot: ", g_currentLot, " SL: ", g_currentSL, " TP: ", g_currentTP);
    }
    else
    {
        Print("❌ SELL emri başarısız - Hata: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Karlı pozisyonları kapat                                        |
//+------------------------------------------------------------------+
void CloseProfitablePositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "" || PositionGetDouble(POSITION_PROFIT) <= 0)
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
            closedCount++;
    }

    Print("💚 ", closedCount, " karlı pozisyon kapatıldı");
}

//+------------------------------------------------------------------+
//| Zararlı pozisyonları kapat                                      |
//+------------------------------------------------------------------+
void CloseLosingPositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "" || PositionGetDouble(POSITION_PROFIT) >= 0)
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
            closedCount++;
    }

    Print("❤️ ", closedCount, " zararlı pozisyon kapatıldı");
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları kapat                                          |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    CTrade trade;
    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        string symbol = PositionGetSymbol(i);
        if(symbol == "")
            continue;

        ulong ticket = PositionGetInteger(POSITION_TICKET);
        if(trade.PositionClose(ticket))
            closedCount++;
    }

    Print("🚫 ", closedCount, " pozisyon kapatıldı");
}

//+------------------------------------------------------------------+
//| Tüm pozisyonları güncelle                                       |
//+------------------------------------------------------------------+
void UpdateAllPositions()
{
    // Tüm objeleri sil ve yeniden oluştur
    ObjectsDeleteAll(0, g_panelName);
    CreateCorporatePanel();
    UpdateIndicators();
    UpdatePanelData();
}
