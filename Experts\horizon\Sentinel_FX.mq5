//+------------------------------------------------------------------+
//| Sentinel FX - Dip Tepe Yakalama EA                              |
//| Dip noktalarında BUY, Tepe noktalarında SELL + Telegram         |
//+------------------------------------------------------------------+
#property copyright "Horizon Trading"
#property version   "2.00"
#property description "Sentinel FX - Advanced Dip/Peak Detection with Telegram Notifications"

#include <Trade\Trade.mqh>
CTrade trade;

//--- Input parametreleri
input group "=== Dip/Tepe Yakalama Ayarları ==="
input int                   RSI_Period = 14;                     // RSI Periyodu
input double                RSI_Oversold = 40.0;                 // RSI Aşırı Satım Seviyesi (Dip) - Gevşetildi
input double                RSI_Overbought = 60.0;               // RSI Aşırı Alım Seviyesi (Tepe) - Gevşetildi
input int                   WPR_Period = 14;                     // Williams %R Periyodu
input double                WPR_Oversold = -60.0;                // WPR Aşırı Satım (Dip) - Gevşetildi
input double                WPR_Overbought = -40.0;              // WPR Aşırı Alım (Tepe) - Gevşetildi

input group "=== Trend Filtresi ==="
input int                   MA_Period = 50;                      // MA Trend Filtresi
input ENUM_MA_METHOD        MA_Method = MODE_EMA;                 // MA Metodu
input bool                  Use_MA_Filter = false;               // MA Filtresi Kullan (Kapalı)

input group "=== Momentum Filtresi ==="
input int                   Momentum_Period = 10;                // Momentum Periyodu
input double                Min_Momentum_Change = 0.1;           // Minimum Momentum Değişimi - Gevşetildi
input bool                  Use_Momentum_Filter = false;         // Momentum Filtresi Kullan (Kapalı)

input group "=== Çıkış Ayarları ==="
input bool                  Use_ATR_TP_SL = true;                // ATR Bazlı TP/SL Kullan
input double                ATR_TP_Multiplier = 3.0;             // ATR TP Çarpanı
input double                ATR_SL_Multiplier = 1.5;             // ATR SL Çarpanı
input int                   ATR_Period = 14;                     // ATR Periyodu
input double                Profit_Target_Pips = 50.0;           // Kar Hedefi (pip) - ATR kapalıysa
input double                Stop_Loss_Pips = 25.0;               // Zarar Durdurma (pip) - ATR kapalıysa
input bool                  Use_Trailing_Stop = true;            // Takip Eden Stop Kullan
input double                Trailing_Stop_Pips = 15.0;           // Takip Eden Stop (pip)

input group "=== Risk Yönetimi ==="
input bool                  Use_Risk_Management = true;          // Risk Yönetimi Kullan
input double                Risk_Percent = 2.0;                  // Risk Yüzdesi (% bakiye)
input double                Fixed_Lot_Size = 0.01;               // Sabit Lot Büyüklüğü (Risk yönetimi kapalıysa)
input double                Max_Lot_Size = 1.0;                  // Maksimum Lot Büyüklüğü
input double                Min_Lot_Size = 0.01;                 // Minimum Lot Büyüklüğü
input int                   Magic_Number = 8888;                 // Magic Number

input group "=== Zaman Filtresi ==="
input bool                  Use_Time_Filter = true;              // Zaman Filtresi Kullan
input int                   Start_Hour = 8;                      // Başlangıç Saati (0-23)
input int                   Start_Minute = 0;                    // Başlangıç Dakikası (0-59)
input int                   End_Hour = 18;                       // Bitiş Saati (0-23)
input int                   End_Minute = 0;                      // Bitiş Dakikası (0-59)
input bool                  Trade_Monday = true;                 // Pazartesi İşlem Yap
input bool                  Trade_Tuesday = true;                // Salı İşlem Yap
input bool                  Trade_Wednesday = true;              // Çarşamba İşlem Yap
input bool                  Trade_Thursday = true;               // Perşembe İşlem Yap
input bool                  Trade_Friday = true;                 // Cuma İşlem Yap
input bool                  Trade_Saturday = false;              // Cumartesi İşlem Yap
input bool                  Trade_Sunday = false;                // Pazar İşlem Yap

input group "=== Telegram Bildirimleri ==="
input bool                  Enable_Telegram = true;              // Telegram Bildirimleri
input string                Telegram_Token = "";                 // Bot Token (BotFather'dan alın)
input string                Telegram_ChatID = "";                // Chat ID (Bot'a /start gönderin)
input bool                  Send_Signal_Alerts = true;           // Sinyal Bildirimleri
input bool                  Send_Trade_Alerts = true;            // İşlem Bildirimleri
input bool                  Send_Status_Reports = true;          // Durum Raporları
input int                   Status_Report_Hours = 4;             // Durum Raporu Saatleri

input group "=== Genel Ayarlar ==="
input bool                  Enable_Debug = true;                 // Debug Logları

//--- Global değişkenler
int rsi_handle, wpr_handle, ma_handle, momentum_handle, atr_handle;
double rsi_buffer[], wpr_buffer[], ma_buffer[], momentum_buffer[], atr_buffer[];
int debug_log_number = 0;

// Dip/Tepe takip değişkenleri
bool dip_signal_active = false;    // Dip sinyali aktif mi?
bool tepe_signal_active = false;   // Tepe sinyali aktif mi?
datetime last_signal_time = 0;     // Son sinyal zamanı

// Telegram değişkenleri
datetime last_telegram_time = 0;   // Son Telegram mesajı zamanı
datetime last_status_report = 0;   // Son durum raporu zamanı
int total_signals = 0;             // Toplam sinyal sayısı
int successful_trades = 0;         // Başarılı işlem sayısı

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // WebRequest izni kontrolü
    if(!TerminalInfoInteger(TERMINAL_DLLS_ALLOWED))
    {
        Print("❌ DLL izni verilmemiş! Tools → Options → Expert Advisors → Allow DLL imports");
        return INIT_FAILED;
    }

    // Telegram test mesajı
    if(Enable_Telegram && StringLen(Telegram_Token) > 0 && StringLen(Telegram_ChatID) > 0)
    {
        Print("📱 Telegram test mesajı gönderiliyor...");
        bool test_result = SendTelegramMessage("🧪 TEST MESAJI\n\nSentinel FX Telegram bağlantısı test ediliyor...\n⏰ " + TimeToString(TimeCurrent()));
        if(test_result)
        {
            Print("✅ Telegram test mesajı başarılı!");
        }
        else
        {
            Print("❌ Telegram test mesajı başarısız!");
            Print("🔧 Kontrol edin:");
            Print("   - Token doğru mu?");
            Print("   - Chat ID doğru mu?");
            Print("   - İnternet bağlantısı var mı?");
            Print("   - WebRequest izni verildi mi?");
        }
    }

    // Indicator handle'larını oluştur
    rsi_handle = iRSI(_Symbol, _Period, RSI_Period, PRICE_CLOSE);
    wpr_handle = iWPR(_Symbol, _Period, WPR_Period);
    ma_handle = iMA(_Symbol, _Period, MA_Period, 0, MA_Method, PRICE_CLOSE);
    momentum_handle = iMomentum(_Symbol, _Period, Momentum_Period, PRICE_CLOSE);
    atr_handle = iATR(_Symbol, _Period, ATR_Period);
    
    if(rsi_handle == INVALID_HANDLE || wpr_handle == INVALID_HANDLE ||
       ma_handle == INVALID_HANDLE || momentum_handle == INVALID_HANDLE ||
       atr_handle == INVALID_HANDLE)
    {
        Print("❌ Indicator handle oluşturulamadı!");
        return INIT_FAILED;
    }
    
    // Array ayarları
    ArraySetAsSeries(rsi_buffer, true);
    ArraySetAsSeries(wpr_buffer, true);
    ArraySetAsSeries(ma_buffer, true);
    ArraySetAsSeries(momentum_buffer, true);
    ArraySetAsSeries(atr_buffer, true);
    
    // Değişkenleri sıfırla
    debug_log_number = 0;
    dip_signal_active = false;
    tepe_signal_active = false;
    last_signal_time = 0;
    last_telegram_time = 0;
    last_status_report = TimeCurrent();
    total_signals = 0;
    successful_trades = 0;
    
    Print("✅ Sentinel FX başlatıldı - Magic: ", Magic_Number);
    Print("📊 DIP: RSI<=", RSI_Oversold, " + WPR<=", WPR_Oversold, " → BUY");
    Print("📊 TEPE: RSI>=", RSI_Overbought, " + WPR>=", WPR_Overbought, " → SELL");
    
    if(Use_ATR_TP_SL)
    {
        Print("🎯 ATR TP/SL: TP=ATR×", ATR_TP_Multiplier, " | SL=ATR×", ATR_SL_Multiplier, " | ATR Periyod:", ATR_Period);
    }
    else
    {
        Print("🎯 Sabit TP/SL: TP=", Profit_Target_Pips, "pip | SL=", Stop_Loss_Pips, "pip");
    }
    
    Print("⚙️ MA Filtresi: ", Use_MA_Filter, " | Momentum Filtresi: ", Use_Momentum_Filter);
    Print("🔧 Gevşetilmiş parametreler - Daha fazla sinyal bekleniyor");
    
    if(Use_Risk_Management)
    {
        Print("💰 Risk Yönetimi: AÇIK - ", Risk_Percent, "% risk | Min:", Min_Lot_Size, " Max:", Max_Lot_Size);
    }
    else
    {
        Print("💰 Risk Yönetimi: KAPALI - Sabit lot:", Fixed_Lot_Size);
    }
    
    if(Use_Time_Filter)
    {
        Print("⏰ Zaman Filtresi: ", StringFormat("%02d:%02d", Start_Hour, Start_Minute), 
              " - ", StringFormat("%02d:%02d", End_Hour, End_Minute));
        Print("📅 İşlem Günleri: Pzt:", Trade_Monday, " Sal:", Trade_Tuesday, " Çar:", Trade_Wednesday,
              " Per:", Trade_Thursday, " Cum:", Trade_Friday, " Cmt:", Trade_Saturday, " Paz:", Trade_Sunday);
    }
    else
    {
        Print("⏰ Zaman Filtresi: KAPALI - 7/24 işlem");
    }
    
    if(Enable_Telegram)
    {
        Print("📱 Telegram: AÇIK");
        Print("   Token uzunluğu: ", StringLen(Telegram_Token), " karakter");
        Print("   Chat ID: ", Telegram_ChatID);

        if(StringLen(Telegram_Token) == 0)
        {
            Print("❌ TELEGRAM TOKEN BOŞ!");
            Print("🔧 Nasıl alınır:");
            Print("   1. Telegram'da @BotFather'a git");
            Print("   2. /newbot komutunu gönder");
            Print("   3. Bot adını belirle");
            Print("   4. Aldığın token'ı EA'ya gir");
        }

        if(StringLen(Telegram_ChatID) == 0)
        {
            Print("❌ TELEGRAM CHAT ID BOŞ!");
            Print("🔧 Nasıl alınır:");
            Print("   1. Bot'una /start mesajı gönder");
            Print("   2. https://api.telegram.org/bot[TOKEN]/getUpdates adresini aç");
            Print("   3. 'chat':{'id': sonrasındaki sayıyı kopyala");
        }

        if(StringLen(Telegram_Token) > 0 && StringLen(Telegram_ChatID) > 0)
        {
            Print("📱 Telegram başlatma mesajı gönderiliyor...");
            bool startup_result = SendTelegramMessage("🚀 Sentinel FX Started!\n\n" +
                              "📊 Symbol: " + _Symbol + "\n" +
                              "⏰ Time: " + TimeToString(TimeCurrent()) + "\n" +
                              "🎯 Strategy: Dip/Peak Detection\n" +
                              "💰 Risk: " + DoubleToString(Risk_Percent, 1) + "%");

            if(!startup_result)
            {
                Print("❌ Başlatma mesajı gönderilemedi!");
                Print("🔧 Kontrol listesi:");
                Print("   1. MetaTrader → Tools → Options → Expert Advisors");
                Print("   2. 'Allow WebRequest for listed URL' işaretli mi?");
                Print("   3. URL listesinde 'https://api.telegram.org' var mı?");
                Print("   4. İnternet bağlantınız çalışıyor mu?");
                Print("   5. Bot Token doğru mu?");
                Print("   6. Chat ID doğru mu?");
                Print("   7. Bot'a /start mesajı gönderdiniz mi?");
            }
        }
    }
    else
    {
        Print("📱 Telegram: KAPALI");
    }
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    if(Enable_Telegram && StringLen(Telegram_Token) > 0 && StringLen(Telegram_ChatID) > 0)
    {
        string stop_reason = "";
        switch(reason)
        {
            case REASON_PROGRAM: stop_reason = "Program değişikliği"; break;
            case REASON_REMOVE: stop_reason = "EA kaldırıldı"; break;
            case REASON_RECOMPILE: stop_reason = "Yeniden derleme"; break;
            case REASON_CHARTCHANGE: stop_reason = "Grafik değişikliği"; break;
            case REASON_CHARTCLOSE: stop_reason = "Grafik kapatıldı"; break;
            case REASON_PARAMETERS: stop_reason = "Parametre değişikliği"; break;
            case REASON_ACCOUNT: stop_reason = "Hesap değişikliği"; break;
            default: stop_reason = "Bilinmeyen sebep";
        }
        
        SendTelegramMessage("⏹️ Sentinel FX Stopped\n\n" +
                          "📊 Symbol: " + _Symbol + "\n" +
                          "⏰ Time: " + TimeToString(TimeCurrent()) + "\n" +
                          "📈 Total Signals: " + IntegerToString(total_signals) + "\n" +
                          "✅ Successful Trades: " + IntegerToString(successful_trades) + "\n" +
                          "🔄 Reason: " + stop_reason);
    }
    
    Print("🔄 Sentinel FX durduruldu");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Yeterli bar kontrolü
    if(Bars(_Symbol, _Period) < MathMax(RSI_Period, MA_Period) + 5)
        return;
    
    // Indicator verilerini al
    if(CopyBuffer(rsi_handle, 0, 0, 3, rsi_buffer) < 3 ||
       CopyBuffer(wpr_handle, 0, 0, 3, wpr_buffer) < 3 ||
       CopyBuffer(ma_handle, 0, 0, 3, ma_buffer) < 3 ||
       CopyBuffer(momentum_handle, 0, 0, 3, momentum_buffer) < 3 ||
       CopyBuffer(atr_handle, 0, 0, 2, atr_buffer) < 2)
    {
        return;
    }
    
    // Mevcut değerler
    double rsi_current = rsi_buffer[0];
    double wpr_current = wpr_buffer[0];
    double ma_current = ma_buffer[0];
    double momentum_current = momentum_buffer[0];
    double momentum_prev = momentum_buffer[1];
    double atr_current = atr_buffer[0];
    
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    // Mevcut pozisyon kontrolü
    bool has_position = HasAnyPosition();
    
    // Trailing Stop kontrolü
    if(has_position && Use_Trailing_Stop)
    {
        UpdateTrailingStop();
    }
    
    // Durum raporu gönder
    SendStatusReport();
    
    // Zaman filtresi kontrolü
    if(Use_Time_Filter && !IsTimeToTrade())
    {
        static datetime last_time_report = 0;
        if(TimeCurrent() - last_time_report >= 30) // 30 saniyede bir
        {
            debug_log_number++;
            Print("⏰ [", debug_log_number, "] İşlem saati dışında - Sinyal arama durduruldu");
            last_time_report = TimeCurrent();
        }
        return;
    }
    
    // Pozisyon varsa sinyal arama yapma
    if(has_position)
    {
        static datetime last_position_report = 0;
        if(TimeCurrent() - last_position_report >= 10)
        {
            debug_log_number++;
            Print("⏸️ [", debug_log_number, "] Pozisyon açık - Sinyal arama durduruldu");
            last_position_report = TimeCurrent();
        }
        return;
    }
    
    // Momentum değişimi hesapla
    double momentum_change = momentum_current - momentum_prev;
    
    // Trend yönü (MA ile)
    bool price_above_ma = current_price > ma_current;
    bool price_below_ma = current_price < ma_current;
    
    // 🎯 DIP YAKALAMA SİNYALİ (BUY) - Gevşetilmiş koşullar
    bool rsi_dip = (rsi_current <= RSI_Oversold);
    bool wpr_dip = (wpr_current <= WPR_Oversold);
    bool momentum_up = Use_Momentum_Filter ? (momentum_change > Min_Momentum_Change) : true;
    bool trend_down = Use_MA_Filter ? price_below_ma : true;
    
    bool dip_conditions = rsi_dip && wpr_dip && momentum_up && trend_down;
    
    // 🎯 TEPE YAKALAMA SİNYALİ (SELL) - Gevşetilmiş koşullar
    bool rsi_tepe = (rsi_current >= RSI_Overbought);
    bool wpr_tepe = (wpr_current >= WPR_Overbought);
    bool momentum_down = Use_Momentum_Filter ? (momentum_change < -Min_Momentum_Change) : true;
    bool trend_up = Use_MA_Filter ? price_above_ma : true;
    
    bool tepe_conditions = rsi_tepe && wpr_tepe && momentum_down && trend_up;

    // Debug - detaylı durum raporu
    static datetime last_debug_time = 0;
    if(TimeCurrent() - last_debug_time >= 3) // 3 saniyede bir
    {
        debug_log_number++;
        Print("📊 [", debug_log_number, "] DIP/TEPE ANALİZİ - ", GetTimeInfo());
        Print("   Zaman Filtresi: ", Use_Time_Filter ? "AÇIK" : "KAPALI", " İşlem Zamanı: ", IsTimeToTrade());
        Print("   RSI: ", DoubleToString(rsi_current, 1), " (Dip<=", RSI_Oversold, " Tepe>=", RSI_Overbought, ")");
        Print("   WPR: ", DoubleToString(wpr_current, 1), " (Dip<=", WPR_Oversold, " Tepe>=", WPR_Overbought, ")");
        Print("   Momentum: ", DoubleToString(momentum_current, 5), " Değişim: ", DoubleToString(momentum_change, 5), " (Min:", Min_Momentum_Change, ")");
        Print("   Fiyat: ", DoubleToString(current_price, 5), " MA: ", DoubleToString(ma_current, 5));
        Print("   ATR: ", DoubleToString(atr_current, 5), " TP:", DoubleToString(atr_current * ATR_TP_Multiplier, 5), " SL:", DoubleToString(atr_current * ATR_SL_Multiplier, 5));
        Print("   Fiyat>MA: ", price_above_ma, " Fiyat<MA: ", price_below_ma);

        // Koşul detayları
        Print("   DIP Koşulları:");
        Print("     RSI<=", RSI_Oversold, ": ", rsi_dip);
        Print("     WPR<=", WPR_Oversold, ": ", wpr_dip);
        Print("     Momentum>", Min_Momentum_Change, ": ", momentum_up, " (Aktif:", Use_Momentum_Filter, ")");
        Print("     Fiyat<MA: ", trend_down, " (Aktif:", Use_MA_Filter, ")");
        Print("     TOPLAM DIP: ", dip_conditions);

        Print("   TEPE Koşulları:");
        Print("     RSI>=", RSI_Overbought, ": ", rsi_tepe);
        Print("     WPR>=", WPR_Overbought, ": ", wpr_tepe);
        Print("     Momentum<-", Min_Momentum_Change, ": ", momentum_down, " (Aktif:", Use_Momentum_Filter, ")");
        Print("     Fiyat>MA: ", trend_up, " (Aktif:", Use_MA_Filter, ")");
        Print("     TOPLAM TEPE: ", tepe_conditions);

        last_debug_time = TimeCurrent();
    }

    // DIP sinyali - BUY aç
    if(dip_conditions && !dip_signal_active)
    {
        total_signals++;
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] DIP YAKALANDI! BUY sinyali:");
        Print("   RSI: ", DoubleToString(rsi_current, 1), " (<=", RSI_Oversold, ")");
        Print("   WPR: ", DoubleToString(wpr_current, 1), " (<=", WPR_Oversold, ")");
        Print("   Momentum artış: ", DoubleToString(momentum_change, 5), " (>", Min_Momentum_Change, ")");
        Print("   Fiyat MA altında: ", price_below_ma);

        // Telegram bildirimi
        if(Enable_Telegram && Send_Signal_Alerts)
        {
            string signal_message = "🟢 DIP DETECTED - BUY SIGNAL\n\n" +
                                   "📊 " + _Symbol + " - " + GetTimeframeString() + "\n" +
                                   "💰 Price: " + DoubleToString(current_price, 5) + "\n" +
                                   "📈 RSI: " + DoubleToString(rsi_current, 1) + " (<=" + DoubleToString(RSI_Oversold, 0) + ")\n" +
                                   "📉 WPR: " + DoubleToString(wpr_current, 1) + " (<=" + DoubleToString(WPR_Oversold, 0) + ")\n" +
                                   "⏰ " + TimeToString(TimeCurrent());
            SendTelegramMessage(signal_message);
        }

        OpenBuyOrder(atr_current);
        dip_signal_active = true;
        last_signal_time = TimeCurrent();
    }

    // TEPE sinyali - SELL aç
    if(tepe_conditions && !tepe_signal_active)
    {
        total_signals++;
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] TEPE YAKALANDI! SELL sinyali:");
        Print("   RSI: ", DoubleToString(rsi_current, 1), " (>=", RSI_Overbought, ")");
        Print("   WPR: ", DoubleToString(wpr_current, 1), " (>=", WPR_Overbought, ")");
        Print("   Momentum azalış: ", DoubleToString(momentum_change, 5), " (<-", Min_Momentum_Change, ")");
        Print("   Fiyat MA üstünde: ", price_above_ma);

        // Telegram bildirimi
        if(Enable_Telegram && Send_Signal_Alerts)
        {
            string signal_message = "🔴 PEAK DETECTED - SELL SIGNAL\n\n" +
                                   "📊 " + _Symbol + " - " + GetTimeframeString() + "\n" +
                                   "💰 Price: " + DoubleToString(current_price, 5) + "\n" +
                                   "📈 RSI: " + DoubleToString(rsi_current, 1) + " (>=" + DoubleToString(RSI_Overbought, 0) + ")\n" +
                                   "📉 WPR: " + DoubleToString(wpr_current, 1) + " (>=" + DoubleToString(WPR_Overbought, 0) + ")\n" +
                                   "⏰ " + TimeToString(TimeCurrent());
            SendTelegramMessage(signal_message);
        }

        OpenSellOrder(atr_current);
        tepe_signal_active = true;
        last_signal_time = TimeCurrent();
    }

    // Sinyal sıfırlama (zaman geçtikten sonra)
    if(TimeCurrent() - last_signal_time > 300) // 5 dakika
    {
        dip_signal_active = false;
        tepe_signal_active = false;
    }
}

//+------------------------------------------------------------------+
//| BUY emri aç                                                      |
//+------------------------------------------------------------------+
void OpenBuyOrder(double atr_value)
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl, tp;

    if(Use_ATR_TP_SL)
    {
        sl = ask - (atr_value * ATR_SL_Multiplier);
        tp = ask + (atr_value * ATR_TP_Multiplier);
    }
    else
    {
        sl = ask - (Stop_Loss_Pips * _Point * 10);
        tp = ask + (Profit_Target_Pips * _Point * 10);
    }

    double lot_size = CalculateLotSize(MathAbs(ask - sl));

    trade.SetExpertMagicNumber(Magic_Number);

    if(trade.Buy(lot_size, _Symbol, ask, sl, tp, "Sentinel_FX_BUY"))
    {
        successful_trades++;
        debug_log_number++;
        Print("🟢 [", debug_log_number, "] BUY EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(ask, 5));
        Print("   SL: ", DoubleToString(sl, 5), " TP: ", DoubleToString(tp, 5));
        Print("   Lot: ", DoubleToString(lot_size, 2), " (Risk: ", Risk_Percent, "% | SL Mesafe: ", DoubleToString(MathAbs(ask - sl), 5), ")");

        if(Use_ATR_TP_SL)
        {
            Print("   ATR: ", DoubleToString(atr_value, 5), " SL Mesafe: ", DoubleToString(atr_value * ATR_SL_Multiplier, 5));
            Print("   TP Mesafe: ", DoubleToString(atr_value * ATR_TP_Multiplier, 5), " Risk/Reward: 1:", DoubleToString(ATR_TP_Multiplier/ATR_SL_Multiplier, 2));
        }

        // Telegram bildirimi
        if(Enable_Telegram && Send_Trade_Alerts)
        {
            string trade_message = "✅ BUY ORDER OPENED\n\n" +
                                 "🎫 Ticket: " + IntegerToString(trade.ResultOrder()) + "\n" +
                                 "📊 " + _Symbol + " - " + GetTimeframeString() + "\n" +
                                 "💰 Price: " + DoubleToString(ask, 5) + "\n" +
                                 "📉 SL: " + DoubleToString(sl, 5) + "\n" +
                                 "📈 TP: " + DoubleToString(tp, 5) + "\n" +
                                 "📦 Lot: " + DoubleToString(lot_size, 2) + "\n" +
                                 "💎 Risk: " + DoubleToString(Risk_Percent, 1) + "%\n" +
                                 "⏰ " + TimeToString(TimeCurrent());
            SendTelegramMessage(trade_message);
        }
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] BUY EMRİ HATASI: ", trade.ResultRetcode());

        // Telegram hata bildirimi
        if(Enable_Telegram)
        {
            SendTelegramMessage("❌ BUY ORDER ERROR\n\n" +
                              "📊 " + _Symbol + "\n" +
                              "🔴 Error Code: " + IntegerToString(trade.ResultRetcode()) + "\n" +
                              "⏰ " + TimeToString(TimeCurrent()));
        }
    }
}

//+------------------------------------------------------------------+
//| SELL emri aç                                                     |
//+------------------------------------------------------------------+
void OpenSellOrder(double atr_value)
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl, tp;

    if(Use_ATR_TP_SL)
    {
        sl = bid + (atr_value * ATR_SL_Multiplier);
        tp = bid - (atr_value * ATR_TP_Multiplier);
    }
    else
    {
        sl = bid + (Stop_Loss_Pips * _Point * 10);
        tp = bid - (Profit_Target_Pips * _Point * 10);
    }

    double lot_size = CalculateLotSize(MathAbs(bid - sl));

    trade.SetExpertMagicNumber(Magic_Number);

    if(trade.Sell(lot_size, _Symbol, bid, sl, tp, "Sentinel_FX_SELL"))
    {
        successful_trades++;
        debug_log_number++;
        Print("🔴 [", debug_log_number, "] SELL EMRİ AÇILDI!");
        Print("   Ticket: ", trade.ResultOrder(), " Fiyat: ", DoubleToString(bid, 5));
        Print("   SL: ", DoubleToString(sl, 5), " TP: ", DoubleToString(tp, 5));
        Print("   Lot: ", DoubleToString(lot_size, 2), " (Risk: ", Risk_Percent, "% | SL Mesafe: ", DoubleToString(MathAbs(bid - sl), 5), ")");

        if(Use_ATR_TP_SL)
        {
            Print("   ATR: ", DoubleToString(atr_value, 5), " SL Mesafe: ", DoubleToString(atr_value * ATR_SL_Multiplier, 5));
            Print("   TP Mesafe: ", DoubleToString(atr_value * ATR_TP_Multiplier, 5), " Risk/Reward: 1:", DoubleToString(ATR_TP_Multiplier/ATR_SL_Multiplier, 2));
        }

        // Telegram bildirimi
        if(Enable_Telegram && Send_Trade_Alerts)
        {
            string trade_message = "✅ SELL ORDER OPENED\n\n" +
                                 "🎫 Ticket: " + IntegerToString(trade.ResultOrder()) + "\n" +
                                 "📊 " + _Symbol + " - " + GetTimeframeString() + "\n" +
                                 "💰 Price: " + DoubleToString(bid, 5) + "\n" +
                                 "📈 SL: " + DoubleToString(sl, 5) + "\n" +
                                 "📉 TP: " + DoubleToString(tp, 5) + "\n" +
                                 "📦 Lot: " + DoubleToString(lot_size, 2) + "\n" +
                                 "💎 Risk: " + DoubleToString(Risk_Percent, 1) + "%\n" +
                                 "⏰ " + TimeToString(TimeCurrent());
            SendTelegramMessage(trade_message);
        }
    }
    else
    {
        debug_log_number++;
        Print("❌ [", debug_log_number, "] SELL EMRİ HATASI: ", trade.ResultRetcode());

        // Telegram hata bildirimi
        if(Enable_Telegram)
        {
            SendTelegramMessage("❌ SELL ORDER ERROR\n\n" +
                              "📊 " + _Symbol + "\n" +
                              "🔴 Error Code: " + IntegerToString(trade.ResultRetcode()) + "\n" +
                              "⏰ " + TimeToString(TimeCurrent()));
        }
    }
}

//+------------------------------------------------------------------+
//| Telegram mesajı gönder                                          |
//+------------------------------------------------------------------+
bool SendTelegramMessage(string message)
{
    if(!Enable_Telegram)
    {
        if(Enable_Debug) Print("📱 Telegram kapalı - mesaj gönderilmedi");
        return false;
    }

    if(StringLen(Telegram_Token) == 0)
    {
        Print("❌ Telegram Token boş! Lütfen Bot Token'ınızı girin.");
        return false;
    }

    if(StringLen(Telegram_ChatID) == 0)
    {
        Print("❌ Telegram Chat ID boş! Lütfen Chat ID'nizi girin.");
        return false;
    }

    // Çok sık mesaj göndermeyi önle (2 saniye minimum)
    if(TimeCurrent() - last_telegram_time < 2)
    {
        if(Enable_Debug) Print("📱 Telegram rate limit - mesaj atlandı");
        return false;
    }

    // URL ve data hazırla
    string url = "https://api.telegram.org/bot" + Telegram_Token + "/sendMessage";
    string data = "chat_id=" + Telegram_ChatID + "&text=" + UrlEncode(message);

    if(Enable_Debug)
    {
        Print("📱 Telegram gönderiliyor:");
        Print("   URL: ", StringSubstr(url, 0, 50), "...");
        Print("   Chat ID: ", Telegram_ChatID);
        Print("   Mesaj uzunluğu: ", StringLen(message), " karakter");
    }

    char post[], result[];
    string headers = "Content-Type: application/x-www-form-urlencoded\r\n";

    StringToCharArray(data, post, 0, StringLen(data));

    int timeout = 10000; // 10 saniye timeout
    int res = WebRequest("POST", url, headers, timeout, post, result, headers);

    last_telegram_time = TimeCurrent();

    if(res == 200)
    {
        string response = CharArrayToString(result);
        if(Enable_Debug)
        {
            Print("✅ Telegram başarılı! Kod: ", res);
            Print("   Yanıt: ", StringSubstr(response, 0, 100), "...");
        }
        return true;
    }
    else if(res == -1)
    {
        Print("❌ Telegram WebRequest hatası!");
        Print("🔧 Kontrol edin:");
        Print("   1. Tools → Options → Expert Advisors → Allow WebRequest for listed URL");
        Print("   2. URL listesine ekleyin: https://api.telegram.org");
        Print("   3. İnternet bağlantınızı kontrol edin");
        return false;
    }
    else
    {
        string response = CharArrayToString(result);
        Print("❌ Telegram HTTP hatası: ", res);
        Print("   Yanıt: ", response);

        if(res == 400)
        {
            Print("🔧 Muhtemel sorunlar:");
            Print("   - Chat ID yanlış (sayısal olmalı)");
            Print("   - Bot Token yanlış");
            Print("   - Bot'a /start mesajı gönderilmemiş");
        }
        else if(res == 401)
        {
            Print("🔧 Bot Token yanlış veya geçersiz!");
        }
        else if(res == 403)
        {
            Print("🔧 Bot'a /start mesajı gönderin!");
        }

        return false;
    }
}

//+------------------------------------------------------------------+
//| URL Encode fonksiyonu (UTF-8 desteği ile)                      |
//+------------------------------------------------------------------+
string UrlEncode(string text)
{
    string result = "";

    // Türkçe karakterleri değiştir
    StringReplace(text, "ç", "c");
    StringReplace(text, "Ç", "C");
    StringReplace(text, "ğ", "g");
    StringReplace(text, "Ğ", "G");
    StringReplace(text, "ı", "i");
    StringReplace(text, "İ", "I");
    StringReplace(text, "ö", "o");
    StringReplace(text, "Ö", "O");
    StringReplace(text, "ş", "s");
    StringReplace(text, "Ş", "S");
    StringReplace(text, "ü", "u");
    StringReplace(text, "Ü", "U");

    for(int i = 0; i < StringLen(text); i++)
    {
        ushort ch = StringGetCharacter(text, i);
        if((ch >= 'A' && ch <= 'Z') ||
           (ch >= 'a' && ch <= 'z') ||
           (ch >= '0' && ch <= '9') ||
           ch == '-' || ch == '_' || ch == '.' || ch == '~' ||
           ch == ':' || ch == '/' || ch == '(' || ch == ')' ||
           ch == '[' || ch == ']' || ch == '{' || ch == '}')
        {
            result += CharToString((char)ch);
        }
        else if(ch == ' ')
        {
            result += "%20";
        }
        else if(ch == '\n')
        {
            result += "%0A";
        }
        else if(ch <= 127) // ASCII karakterler
        {
            result += StringFormat("%%%02X", ch);
        }
        else // Unicode karakterler için basit değiştirme
        {
            result += "?"; // Bilinmeyen karakterleri ? ile değiştir
        }
    }
    return result;
}

//+------------------------------------------------------------------+
//| Manuel Telegram test (Comment ile çağır)                       |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    if(id == CHARTEVENT_KEYDOWN)
    {
        if(lparam == 84) // T tuşu
        {
            Print("🧪 Manuel Telegram test başlatıldı...");
            bool test_result = SendTelegramMessage("🧪 MANUAL TEST\n\nTest message sent by pressing T key.\n⏰ " + TimeToString(TimeCurrent()));
            if(test_result)
            {
                Print("✅ Manuel test başarılı!");
            }
            else
            {
                Print("❌ Manuel test başarısız!");
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Durum raporu gönder                                             |
//+------------------------------------------------------------------+
void SendStatusReport()
{
    if(!Enable_Telegram || !Send_Status_Reports || StringLen(Telegram_Token) == 0 || StringLen(Telegram_ChatID) == 0)
        return;

    // Belirlenen saatte durum raporu gönder
    if(TimeCurrent() - last_status_report >= Status_Report_Hours * 3600)
    {
        double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
        double account_equity = AccountInfoDouble(ACCOUNT_EQUITY);
        double account_profit = AccountInfoDouble(ACCOUNT_PROFIT);
        int open_positions = PositionsTotal();

        string status_message = "📊 SENTINEL FX STATUS REPORT\n\n" +
                              "📈 Symbol: " + _Symbol + " - " + GetTimeframeString() + "\n" +
                              "💰 Balance: " + DoubleToString(account_balance, 2) + "\n" +
                              "💎 Equity: " + DoubleToString(account_equity, 2) + "\n" +
                              "📊 Daily P&L: " + DoubleToString(account_profit, 2) + "\n" +
                              "📦 Open Positions: " + IntegerToString(open_positions) + "\n" +
                              "🎯 Total Signals: " + IntegerToString(total_signals) + "\n" +
                              "✅ Successful Trades: " + IntegerToString(successful_trades) + "\n" +
                              "⏰ " + TimeToString(TimeCurrent());

        SendTelegramMessage(status_message);
        last_status_report = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Timeframe string al                                             |
//+------------------------------------------------------------------+
string GetTimeframeString()
{
    switch(_Period)
    {
        case PERIOD_M1: return "M1";
        case PERIOD_M5: return "M5";
        case PERIOD_M15: return "M15";
        case PERIOD_M30: return "M30";
        case PERIOD_H1: return "H1";
        case PERIOD_H4: return "H4";
        case PERIOD_D1: return "D1";
        case PERIOD_W1: return "W1";
        case PERIOD_MN1: return "MN1";
        default: return "Unknown";
    }
}

//+------------------------------------------------------------------+
//| Herhangi bir pozisyon var mı kontrol et                         |
//+------------------------------------------------------------------+
bool HasAnyPosition()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number)
                {
                    return true;
                }
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Trailing Stop güncelle                                          |
//+------------------------------------------------------------------+
void UpdateTrailingStop()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket > 0)
        {
            if(PositionSelectByTicket(ticket))
            {
                if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
                   PositionGetInteger(POSITION_MAGIC) == Magic_Number)
                {
                    double current_sl = PositionGetDouble(POSITION_SL);
                    double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
                    int position_type = (int)PositionGetInteger(POSITION_TYPE);

                    double current_price = (position_type == POSITION_TYPE_BUY) ?
                                         SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                                         SymbolInfoDouble(_Symbol, SYMBOL_ASK);

                    double trailing_distance = Trailing_Stop_Pips * _Point * 10;
                    double new_sl = 0;

                    if(position_type == POSITION_TYPE_BUY)
                    {
                        new_sl = current_price - trailing_distance;
                        if(new_sl > current_sl && new_sl > open_price)
                        {
                            trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP));
                            debug_log_number++;
                            Print("📈 [", debug_log_number, "] BUY Trailing Stop güncellendi: ", DoubleToString(new_sl, 5));

                            // Telegram bildirimi
                            if(Enable_Telegram && Send_Trade_Alerts)
                            {
                                SendTelegramMessage("📈 TRAILING STOP UPDATED\n\n" +
                                                  "🎫 Ticket: " + IntegerToString(ticket) + "\n" +
                                                  "📊 " + _Symbol + " BUY\n" +
                                                  "📉 New SL: " + DoubleToString(new_sl, 5) + "\n" +
                                                  "⏰ " + TimeToString(TimeCurrent()));
                            }
                        }
                    }
                    else if(position_type == POSITION_TYPE_SELL)
                    {
                        new_sl = current_price + trailing_distance;
                        if((current_sl == 0 || new_sl < current_sl) && new_sl < open_price)
                        {
                            trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP));
                            debug_log_number++;
                            Print("📉 [", debug_log_number, "] SELL Trailing Stop güncellendi: ", DoubleToString(new_sl, 5));

                            // Telegram bildirimi
                            if(Enable_Telegram && Send_Trade_Alerts)
                            {
                                SendTelegramMessage("📉 TRAILING STOP UPDATED\n\n" +
                                                  "🎫 Ticket: " + IntegerToString(ticket) + "\n" +
                                                  "📊 " + _Symbol + " SELL\n" +
                                                  "📈 New SL: " + DoubleToString(new_sl, 5) + "\n" +
                                                  "⏰ " + TimeToString(TimeCurrent()));
                            }
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| İşlem zamanı kontrolü                                            |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
    if(!Use_Time_Filter) return true;

    datetime current_time = TimeCurrent();
    MqlDateTime time_struct;
    TimeToStruct(current_time, time_struct);

    // Gün kontrolü
    bool day_allowed = false;
    switch(time_struct.day_of_week)
    {
        case 1: day_allowed = Trade_Monday; break;    // Pazartesi
        case 2: day_allowed = Trade_Tuesday; break;   // Salı
        case 3: day_allowed = Trade_Wednesday; break; // Çarşamba
        case 4: day_allowed = Trade_Thursday; break;  // Perşembe
        case 5: day_allowed = Trade_Friday; break;    // Cuma
        case 6: day_allowed = Trade_Saturday; break;  // Cumartesi
        case 0: day_allowed = Trade_Sunday; break;    // Pazar
        default: day_allowed = false;
    }

    if(!day_allowed) return false;

    // Saat kontrolü
    int current_hour = time_struct.hour;
    int current_minute = time_struct.min;
    int current_time_minutes = current_hour * 60 + current_minute;
    int start_time_minutes = Start_Hour * 60 + Start_Minute;
    int end_time_minutes = End_Hour * 60 + End_Minute;

    // Gece geçen zaman aralığı kontrolü (örn: 22:00 - 06:00)
    if(start_time_minutes > end_time_minutes)
    {
        return (current_time_minutes >= start_time_minutes || current_time_minutes <= end_time_minutes);
    }
    else
    {
        return (current_time_minutes >= start_time_minutes && current_time_minutes <= end_time_minutes);
    }
}

//+------------------------------------------------------------------+
//| Zaman bilgisi string formatında                                 |
//+------------------------------------------------------------------+
string GetTimeInfo()
{
    datetime current_time = TimeCurrent();
    MqlDateTime time_struct;
    TimeToStruct(current_time, time_struct);

    string day_names[] = {"Pazar", "Pazartesi", "Salı", "Çarşamba", "Perşembe", "Cuma", "Cumartesi"};
    string day_name = day_names[time_struct.day_of_week];

    return StringFormat("%s %02d:%02d", day_name, time_struct.hour, time_struct.min);
}

//+------------------------------------------------------------------+
//| Lot büyüklüğü hesapla                                           |
//+------------------------------------------------------------------+
double CalculateLotSize(double sl_distance)
{
    if(!Use_Risk_Management)
    {
        return Fixed_Lot_Size;
    }

    // Hesap bilgileri
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * Risk_Percent / 100.0;

    // Sembol bilgileri
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    // SL mesafesini pip'e çevir
    double sl_pips = sl_distance / point;

    // Lot hesapla
    double lot_size = 0;
    if(sl_pips > 0 && tick_value > 0)
    {
        // Risk miktarı / (SL pip × pip değeri)
        double pip_value = tick_value * (point / tick_size);
        lot_size = risk_amount / (sl_pips * pip_value);
    }
    else
    {
        lot_size = Fixed_Lot_Size;
    }

    // Lot sınırları kontrolü
    if(lot_size < min_lot) lot_size = min_lot;
    if(lot_size > max_lot) lot_size = max_lot;
    if(lot_size > Max_Lot_Size) lot_size = Max_Lot_Size;
    if(lot_size < Min_Lot_Size) lot_size = Min_Lot_Size;

    // Lot step'e göre yuvarla
    lot_size = MathFloor(lot_size / lot_step) * lot_step;

    // Debug bilgisi
    if(Enable_Debug)
    {
        static datetime last_lot_debug = 0;
        if(TimeCurrent() - last_lot_debug >= 10)
        {
            debug_log_number++;
            Print("💰 [", debug_log_number, "] LOT HESAPLAMA:");
            Print("   Bakiye: ", DoubleToString(account_balance, 2), " Risk: ", Risk_Percent, "% = ", DoubleToString(risk_amount, 2));
            Print("   SL Mesafe: ", DoubleToString(sl_distance, 5), " (", DoubleToString(sl_pips, 1), " pip)");
            Print("   Pip Değeri: ", DoubleToString(tick_value * (point / tick_size), 2));
            Print("   Hesaplanan Lot: ", DoubleToString(lot_size, 3), " (Min:", min_lot, " Max:", max_lot, ")");
            last_lot_debug = TimeCurrent();
        }
    }

    return lot_size;
}
